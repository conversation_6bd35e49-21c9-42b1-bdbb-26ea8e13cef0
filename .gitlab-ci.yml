image: docker:24.0.5

services:
  - docker:24.0.5-dind

variables:
  REPOSITORY_URL_DEV: 834602855693.dkr.ecr.ap-south-1.amazonaws.com/dev-cart-service
  TASK_DEFINITION_NAME_DEV: dev-cart-service
  CLUSTER_NAME_DEV: dev-dentalkart
  SERVICE_NAME_DEV: svc-dev-cart-service-v2

  REPOSITORY_URL_PROD: 834602855693.dkr.ecr.ap-south-1.amazonaws.com/cart-service-prod
  TASK_DEFINITION_NAME_PROD: cart-service-prod-td
  CLUSTER_NAME_PROD: production-dentalkart
  SERVICE_NAME_PROD: cart-service-svc

before_script:
  - apk add --no-cache curl jq python3 py3-pip
  - pip install awscli --no-build-isolation
  - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
  - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
  - aws configure set region $AWS_DEFAULT_REGION
  - $(aws ecr get-login --no-include-email --region "${AWS_DEFAULT_REGION}")
  - IMAGE_TAG="$(echo $CI_COMMIT_SHA | head -c 8)"

stages:
  - build
  - deploy

build-dev:
  stage: build
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL_DEV:latest .
    - echo "Tagging image..."
    - docker tag $REPOSITORY_URL_DEV:latest $REPOSITORY_URL_DEV:$IMAGE_TAG
    - echo "Pushing image..."
    - docker push $REPOSITORY_URL_DEV:latest
    - docker push $REPOSITORY_URL_DEV:$IMAGE_TAG
  only:
    - development

deploy-dev:
  stage: deploy
  script:
    - echo $REPOSITORY_URL_DEV:$IMAGE
    - echo "Registering new container definition..."
    - echo "Updating the service..."
    - aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${CLUSTER_NAME_DEV}" --service "${SERVICE_NAME_DEV}"  --task-definition "${TASK_DEFINITION_NAME_DEV}" --force-new-deployment
  only:
    - development

build-prod:
  stage: build
  script:
    - echo "Building image..."
    - docker build -t $REPOSITORY_URL_PROD:latest .
    - echo "Tagging image..."
    - docker tag $REPOSITORY_URL_PROD:latest $REPOSITORY_URL_PROD:$IMAGE_TAG
    - echo "Pushing image..."
    - docker push $REPOSITORY_URL_PROD:latest
    - docker push $REPOSITORY_URL_PROD:$IMAGE_TAG
  only:
    - main

deploy-prod:
  stage: deploy
  script:
    - echo $REPOSITORY_URL_PROD:$IMAGE
    - echo "Registering new container definition..."
    - echo "Updating the service..."
    - aws ecs update-service --region "${AWS_DEFAULT_REGION}" --cluster "${CLUSTER_NAME_PROD}" --service "${SERVICE_NAME_PROD}"  --task-definition "${TASK_DEFINITION_NAME_PROD}" --force-new-deployment
  only:
    - main
