{"name": "cart-service", "version": "0.0.1", "description": "", "author": "<PERSON><PERSON><PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "rimraf src/graphql.ts && nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:up": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo"}, "dependencies": {"@nestjs/apollo": "^10.0.14", "@nestjs/axios": "^0.0.8", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/graphql": "^10.0.15", "@nestjs/platform-express": "^8.0.0", "@nestjs/swagger": "^5.2.1", "apollo": "^2.34.0", "apollo-server-core": "^3.8.1", "apollo-server-express": "^3.8.2", "aws-xray-sdk": "^3.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "dotenv": "^16.4.5", "helmet": "^5.1.0", "mysql2": "^2.3.3", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "sequelize": "^6.21.3", "sequelize-typescript": "^2.1.3", "swagger-ui-express": "^4.4.0", "ts-morph": "^15.1.0", "uuid": "^8.3.2", "webpack": "^5.74.0", "winston": "^3.7.2", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/express": "^4.17.13", "@types/jest": "27.4.0", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}