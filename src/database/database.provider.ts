import { Sequelize } from 'sequelize-typescript';
import config from '../config/env';
import { Quote } from './entities/quote';
import { QuoteAddress } from './entities/quote_address';
import { QuoteDiscount } from './entities/quote_discount';
import { QuoteItem } from './entities/quote_item';
import { QuoteShippingRate } from './entities/quote_shipping_rate';
import { ShippingRates } from './entities/shipping_rate';
import { CarriersConfig } from './entities/carriers_config';
import { QuoteItemExtensionAttribute } from './entities/quote_item_extension_attribute';
import { AmountPromotion } from './entities/amount_promotion';
import { ItemPromotion } from './entities/item_promotion';
import { QuoteExtraInfo } from './entities/quote_extra_info';
import { PostcodeRegion } from './entities/postcode_region';
import * as AWSXRay from 'aws-xray-sdk';
import { QuoteAmountPromotion } from './entities/quote_amount_promotion';
import { QuoteItemPromotion } from './entities/quote_item_promotion';
const mysql = AWSXRay.captureMySQL(require('mysql2'));

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    useFactory: async () => {
      const sequelize = new Sequelize({
        ...config.database,
        dialect: 'mysql',
        dialectModule: mysql,
        logging: false,
        models: [
          Quote,
          QuoteItem,
          QuoteAddress,
          QuoteShippingRate,
          QuoteDiscount,
          ShippingRates,
          CarriersConfig,
          QuoteItemExtensionAttribute,
          ItemPromotion,
          AmountPromotion,
          QuoteExtraInfo,
          PostcodeRegion,
          QuoteAmountPromotion,
          QuoteItemPromotion,
        ],
      });

      await sequelize.sync();
      return sequelize;
    },
  },
];
