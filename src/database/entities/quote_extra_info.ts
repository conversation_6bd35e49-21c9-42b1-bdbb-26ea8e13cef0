import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Quote } from './quote';

@Table({
  tableName: 'quote_extra_info',
  timestamps: true,
  paranoid: true,
})
export class QuoteExtraInfo extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;
  @ForeignKey(() => Quote)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_id: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_active: boolean;
  @Column({ type: DataType.INTEGER, allowNull: false })
  customer_id: number;
}
