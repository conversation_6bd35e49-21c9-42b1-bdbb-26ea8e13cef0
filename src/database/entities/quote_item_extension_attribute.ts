import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { QuoteItem } from './quote_item';
import { ItemPromotion } from './item_promotion';
import { AmountPromotion } from './amount_promotion';

export enum discountTypeEnum {
  FIXED = 'fixed',
  PERCENTAGE = 'percentage',
}
@Table({
  tableName: 'quote_item_extension_attribute',
  paranoid: true,
  timestamps: true,
})
export class QuoteItemExtensionAttribute extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  extension_attribute_id: number;

  @ForeignKey(() => QuoteItem)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_item_id: number;

  @BelongsTo(() => QuoteItem)
  quote_item: QuoteItem;

  @ForeignKey(() => ItemPromotion)
  @Column({ type: DataType.BIGINT, allowNull: true })
  item_promotion_id: number;

  @BelongsTo(() => ItemPromotion)
  item_promotion: ItemPromotion;

  @ForeignKey(() => AmountPromotion)
  @Column({ type: DataType.BIGINT, allowNull: true })
  amount_promotion_id: number;

  @BelongsTo(() => AmountPromotion)
  amount_promotion: AmountPromotion;

  @Column({ type: DataType.STRING(10), allowNull: true })
  referral_code: string;

  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  is_free_product: boolean;

  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  admin_discount_percent: number;

  @Column({ type: DataType.DECIMAL(20, 3), defaultValue: 0 })
  admin_discount_amount: number;

  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  buying_guide_qty: number;

  @Column({ type: DataType.ENUM, values: Object.values(discountTypeEnum) })
  discount_type: discountTypeEnum;
}
