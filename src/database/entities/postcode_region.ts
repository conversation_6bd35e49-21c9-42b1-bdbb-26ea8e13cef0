import { Column, DataType, Model, Table, Unique } from 'sequelize-typescript';

@Table({ tableName: 'postcode_region', timestamps: true })
export class PostcodeRegion extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;
  @Column({ type: DataType.STRING, allowNull: false })
  country_id: string;
  @Column({ type: DataType.STRING })
  region_code: string;
  @Column({ type: DataType.BIGINT })
  region_id: number;
  @Column({ type: DataType.STRING })
  region: string;
  @Unique
  @Column({ type: DataType.STRING })
  postcode: string;
}
