import {
  Bel<PERSON>sTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { Quote } from './quote';
import { QuoteDiscount } from './quote_discount';
import { QuoteItemExtensionAttribute } from './quote_item_extension_attribute';
import { QuoteItemPromotion } from './quote_item_promotion';
@Table({ tableName: 'quote_item', paranoid: true, timestamps: true })
export class QuoteItem extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  quote_item_id: number;

  @BelongsTo(() => Quote)
  quote: Quote;

  @ForeignKey(() => Quote)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_id: number;

  @Column({ type: DataType.BIGINT, allowNull: false })
  product_id: number;
  @Column({ type: DataType.BIGINT, allowNull: false })
  store_id: number;
  @Column({ type: DataType.BIGINT })
  parent_item_id: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_virtual: boolean;
  @Column({ type: DataType.STRING })
  sku: string;
  @Column({ type: DataType.STRING })
  name: string;
  @Column({ type: DataType.TEXT })
  description: string;
  @Column({ type: DataType.TEXT })
  applied_tax_ids: string;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  no_discount: boolean;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  weight: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  qty: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  price: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_price: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  custom_price: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  discount_percent: number;
  @Column({ type: DataType.DECIMAL(20, 3), defaultValue: 0 })
  discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  tax_percent: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  row_total: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_row_total: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  row_total_with_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  row_weight: number;
  @Column({ type: DataType.STRING })
  product_type: string;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_tax_before_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  tax_before_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  original_custom_price: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_cost: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  price_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_price_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  row_total_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_row_total_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  discount_tax_compensation_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_discount_tax_compensation_amount: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  free_shipping: boolean;
  @Column({ type: DataType.STRING })
  product_tax_class_id: string;
  @Column({ type: DataType.INTEGER })
  reward_points_earned: number;
  @Column({ type: DataType.TEXT })
  applied_tax_titles: string;
  @Column({ type: DataType.STRING })
  manufacturer: string;
  @Column({ type: DataType.TEXT })
  categories: string[];
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_cod: boolean;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  item_handling_fee: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  row_total_savings: number;
  @Column({ type: DataType.BIGINT })
  parent_id: number;
  @HasOne(() => QuoteItemExtensionAttribute)
  extension_attribute: QuoteItemExtensionAttribute;
  @HasMany(() => QuoteItemPromotion, { onDelete: 'CASCADE', hooks: true })
  quoteItemPromotions: QuoteItemPromotion[];
}
