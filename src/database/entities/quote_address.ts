import {
  Column,
  DataType,
  Foreign<PERSON>ey,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { Quote } from './quote';
import { QuoteShippingRate } from './quote_shipping_rate';

@Table({ tableName: 'quote_address', paranoid: true, timestamps: true })
export class QuoteAddress extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  quote_address_id: number;
  @ForeignKey(() => Quote)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_id: number;
  @Column({ type: DataType.BIGINT })
  customer_id: number;
  @Column({ type: DataType.BIGINT })
  customer_address_id: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  save_in_address_book: boolean;
  @Column({ type: DataType.STRING, allowNull: false })
  address_type: string;
  @Column({ type: DataType.STRING })
  customer_email: string;
  @Column({ type: DataType.STRING })
  customer_firstname: string;
  @Column({ type: DataType.STRING })
  customer_lastname: string;
  @Column({ type: DataType.STRING })
  customer_company: string;
  @Column({ type: DataType.STRING })
  customer_street: string;
  @Column({ type: DataType.STRING })
  customer_city: string;
  @Column({ type: DataType.DOUBLE, allowNull: true })
  latitude: number;
  @Column({ type: DataType.DOUBLE, allowNull: true })
  longitude: number;
  @Column({ type: DataType.STRING, allowNull: true })
  tag: string;
  @Column({ type: DataType.TEXT, allowNull: true })
  map_address: string;
  @Column({ type: DataType.TEXT, allowNull: true })
  customer_street_2:string;
  @Column({ type: DataType.STRING })
  customer_region: string;
  @Column({ type: DataType.STRING })
  customer_region_id: number;
  @Column({ type: DataType.STRING })
  customer_postcode: string;
  @Column({ type: DataType.STRING })
  customer_country_id: string;
  @Column({ type: DataType.STRING })
  customer_telephone: string;
  @Column({ type: DataType.STRING })
  customer_fax: string;
  @Column({ type: DataType.BOOLEAN, defaultValue: true })
  same_as_billing: boolean;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  collect_shipping_rates: boolean;
  @Column({ type: DataType.STRING })
  shipping_method: string;
  @Column({ type: DataType.STRING })
  shipping_description: string;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  weight: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  subtotal: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_subtotal: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  subtotal_with_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_subtotal_with_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  shipping_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_shipping_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  shipping_tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_shipping_tax_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  grand_total: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_grand_total: number;
  @Column({ type: DataType.TEXT })
  applied_taxes: string;
  @Column({ type: DataType.TEXT })
  discount_description: string;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  shipping_discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_shipping_discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  subtotal_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_subtotal_total_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  discount_tax_compensation_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_discount_tax_compensation_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  shipping_discount_tax_compensation_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_shipping_discount_tax_compensation_amnt: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  shipping_incl_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_shipping_incl_tax: number;
  @Column({ type: DataType.TEXT })
  gst_id: string;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  gst_is_valid: boolean;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  free_shipping: boolean;
  @Column({ type: DataType.STRING })
  customer_region_code: string;

  @HasOne(() => QuoteShippingRate)
  shipping_rate: QuoteShippingRate;
}
