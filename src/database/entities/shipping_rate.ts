import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({ tableName: 'shipping_rates', timestamps: true })
export class ShippingRates extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;
  @Column({ type: DataType.STRING(4) })
  dest_country_id: string;
  @Column({ type: DataType.INTEGER })
  dest_region_id: number;
  @Column({ type: DataType.STRING(10) })
  dest_zip: string;
  @Column({ type: DataType.STRING(30) })
  condition_name: string;
  @Column({ type: DataType.DOUBLE })
  condition_value: number;
  @Column({ type: DataType.DOUBLE })
  price: number;
  @Column({ type: DataType.DOUBLE })
  cost: number;
}
