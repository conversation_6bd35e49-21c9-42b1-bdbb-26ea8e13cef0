import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { QuoteItem } from './quote_item';
import { ItemPromotion } from './item_promotion';
import { ItemPromotionProductData } from 'src/interface/promotion-product-data';

@Table({
  tableName: 'quote_item_promotion',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class QuoteItemPromotion extends Model<QuoteItemPromotion> {
  @Column({ type: DataType.BIGINT, autoIncrement: true, primaryKey: true })
  id: number;

  @ForeignKey(() => QuoteItem)
  @Column({ type: DataType.BIGINT, allowNull: false, onDelete: 'CASCADE' })
  quote_item_id: number;

  @ForeignKey(() => ItemPromotion)
  @Column({ type: DataType.BIGINT, allowNull: false })
  promotion_id: number;

  @Column({ type: DataType.STRING, allowNull: false })
  sku: string;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    validate: { min: 1, isInt: true },
    allowNull: false,
  })
  qty: number;

  @Column({ type: DataType.JSON, allowNull: false })
  meta_info: ItemPromotionProductData;

  @BelongsTo(() => QuoteItem)
  quoteItem: QuoteItem;

  @BelongsTo(() => ItemPromotion, { onDelete: 'CASCADE' })
  itemPromotion: ItemPromotion;
}
