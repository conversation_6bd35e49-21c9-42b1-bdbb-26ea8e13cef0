import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { QuoteAddress } from './quote_address';

@Table({ tableName: 'quote_shipping_rate', paranoid: true, timestamps: true })
export class QuoteShippingRate extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  quote_shipping_rate_id: number;
  @ForeignKey(() => QuoteAddress)
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    unique: true,
  })
  quote_address_id: number;
  @Column({ type: DataType.STRING, allowNull: false })
  carrier: string;
  @Column({ type: DataType.STRING, allowNull: false })
  carrier_title: string;
  @Column({ type: DataType.STRING, allowNull: false })
  code: string;
  @Column({ type: DataType.STRING, allowNull: false })
  method: string;
  @Column({ type: DataType.TEXT })
  method_description: string;
  @Column({ type: DataType.TEXT })
  error_message: string;
  @Column({ type: DataType.TEXT, allowNull: false })
  method_title: string;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  price: number;
}
