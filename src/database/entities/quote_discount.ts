import {
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { QuoteDiscountsObject } from 'src/interface/discount';
import { Quote } from './quote';

@Table({ tableName: 'quote_discount', timestamps: true })
export class QuoteDiscount extends Model {
  @Column({
    type: DataType.BIGINT,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  quote_discount_id: number;
  @ForeignKey(() => Quote)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_id: number;

  @Column({ type: DataType.JSON, defaultValue: [] })
  discounts: QuoteDiscountsObject[];
}
