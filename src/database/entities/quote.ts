import {
  Column,
  DataType,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { QuoteAddress } from './quote_address';
import { QuoteDiscount } from './quote_discount';
import { QuoteItem } from './quote_item';
import { QuoteExtraInfo } from './quote_extra_info';
import { QuoteAmountPromotion } from './quote_amount_promotion';

@Table({
  tableName: 'quote',
  paranoid: true,
  timestamps: true,
  hooks: {
    afterDestroy: (instance, options) => {
      instance.set('masked_id', null).save();
    },
  },
  defaultScope: {
    where: {
      is_active: true,
    },
  },
  initialAutoIncrement: '70000000',
})
export class Quote extends Model {
  @Column({ type: DataType.BIGINT, autoIncrement: true, primaryKey: true })
  quote_id: number;
  @Column({ type: DataType.BIGINT, allowNull: false })
  store_id: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_active: boolean;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_virtual: boolean;

  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  items_count: number;
  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  items_qty: number;
  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  orig_order_id: number;
  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  store_to_base_rate: number;
  @Column({ type: DataType.INTEGER, allowNull: false, defaultValue: 0 })
  store_to_quote_rate: number;
  @Column({ type: DataType.STRING, allowNull: false })
  base_currency_code: string;
  @Column({ type: DataType.STRING, allowNull: false })
  store_currency_code: string;
  @Column({ type: DataType.STRING, allowNull: false })
  quote_currency_code: string;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  grand_total: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  base_grand_total: number;
  @Column({ type: DataType.STRING })
  checkout_method: string;
  @Column({ type: DataType.INTEGER })
  customer_id: number;
  @Column({ type: DataType.INTEGER })
  customer_tax_class_id: number;
  @Column({ type: DataType.INTEGER })
  customer_group_id: number;
  @Column({ type: DataType.STRING })
  customer_email: string;
  @Column({ type: DataType.STRING })
  customer_firstname: string;
  @Column({ type: DataType.STRING })
  customer_lastname: string;
  @Column({ type: DataType.TEXT })
  customer_note: string;
  @Column({ type: DataType.BOOLEAN })
  customer_note_notify: boolean;
  @Column({ type: DataType.BOOLEAN })
  customer_is_guest: boolean;
  @Column({ type: DataType.STRING })
  remote_ip: string;
  @Column({ type: DataType.STRING })
  applied_rule_ids: string;
  @Column({ type: DataType.STRING })
  reserved_order_id: string;
  @Column({ type: DataType.STRING })
  coupon_code: string;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_to_global_rate: number;
  @Column({ type: DataType.DECIMAL(20, 2), defaultValue: 0 })
  base_to_quote_rate: number;
  @Column({ type: DataType.STRING })
  customer_gst_id: string;
  @Column({ type: DataType.STRING })
  customer_gender: string;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  subtotal: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  base_subtotal: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  subtotal_with_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  base_subtotal_with_discount: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_changed: boolean;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  trigger_recollect: boolean;
  @Column({ type: DataType.TEXT })
  ext_shipping_info: string;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_persistent: boolean;
  @Column({ type: DataType.STRING })
  payment_method: string;
  @Column({ type: DataType.STRING(32), unique: true })
  masked_id: string;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  discount_amount: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  subtotal_including_tax: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  total_savings: number;
  @Column({ type: DataType.STRING, allowNull: false })
  quote_currency_symbol: string;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  rewards_discount: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  total_weight: number;
  @Column({ type: DataType.DECIMAL(20, 2), allowNull: false, defaultValue: 0 })
  overweight_delivery_charges: number;
  @Column({ type: DataType.BOOLEAN, defaultValue: false })
  is_active_membership: boolean;
  @HasMany(() => QuoteItem)
  items: QuoteItem[];

  @HasMany(() => QuoteAmountPromotion, { onDelete: 'CASCADE' })
  quoteAmountPromotions: QuoteAmountPromotion[];

  @HasMany(() => QuoteAddress)
  addresses: QuoteAddress[];

  @HasOne(() => QuoteDiscount)
  discount: QuoteDiscount;

  @HasOne(() => QuoteExtraInfo)
  quoteExtraInfo: QuoteExtraInfo;
}
