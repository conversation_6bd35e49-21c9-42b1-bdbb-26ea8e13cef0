import {
  Column,
  DataType,
  Model,
  ForeignKey,
  BelongsTo,
  Table,
} from 'sequelize-typescript';
import { Quote } from './quote';
import { AmountPromotion } from './amount_promotion';
import { AmountPromotionProductData } from 'src/interface/promotion-product-data';
import { INTEGER } from 'sequelize';

@Table({
  tableName: 'quote_amount_promotion',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class QuoteAmountPromotion extends Model<QuoteAmountPromotion> {
  @Column({ type: DataType.BIGINT, autoIncrement: true, primaryKey: true })
  id: number;

  @ForeignKey(() => Quote)
  @Column({ type: DataType.BIGINT, allowNull: false })
  quote_id: number;

  @ForeignKey(() => AmountPromotion)
  @Column({ type: DataType.BIGINT, allowNull: false })
  promotion_id: number;

  @Column({ type: DataType.STRING, allowNull: false })
  sku: string;

  @Column({
    type: DataType.INTEGER.UNSIGNED,
    validate: { min: 1, isInt: true },
    allowNull: false,
  })
  qty: number;

  @Column({ type: DataType.JSON, allowNull: false })
  meta_info: AmountPromotionProductData;

  @BelongsTo(() => Quote)
  quote: Quote;

  @BelongsTo(() => AmountPromotion)
  amountPromotion: AmountPromotion;
}
