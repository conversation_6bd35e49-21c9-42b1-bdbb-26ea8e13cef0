'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'quote_address',
        'latitude',
        {
          type: Sequelize.DOUBLE,
          allowNull: true,
          comment: 'Latitude',
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'quote_address',
        'longitude',
        {
          type: Sequelize.DOUBLE,
          allowNull: true,
          comment: 'Longitude',
        },
        { transaction },
      );
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('quote_address', 'latitude', {
        transaction,
      });
      await queryInterface.removeColumn('quote_address', 'longitude', {
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
