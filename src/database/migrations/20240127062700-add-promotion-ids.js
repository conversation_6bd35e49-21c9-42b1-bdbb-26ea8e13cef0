'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'quote_item_extension_attribute',
      'item_promotion_id',
      {
        type: Sequelize.BIGINT,
        allowNull: true,
        references: {
          model: 'item_promotions',
          key: 'promotion_id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    );

    await queryInterface.addColumn(
      'quote_item_extension_attribute',
      'amount_promotion_id',
      {
        type: Sequelize.BIGINT,
        allowNull: true,
        references: {
          model: 'amount_promotions',
          key: 'amount_promotion_id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    );
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(
      'quote_item_extension_attribute',
      'item_promotion_id',
    );
    await queryInterface.removeColumn(
      'quote_item_extension_attribute',
      'amount_promotion_id',
    );
  },
};
