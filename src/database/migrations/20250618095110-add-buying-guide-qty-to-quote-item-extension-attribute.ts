'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'quote_item_extension_attribute',
      'buying_guide_qty',
      {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        defaultValue: 0,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'quote_item_extension_attribute',
      'buying_guide_qty',
    );
  },
};
