import { BadRequestException, HttpStatus } from '@nestjs/common';

export class CustomBadRequestException extends BadRequestException {
  private readonly errorCode: string;

  constructor(message: string, errorCode: string) {
    super(message);
    this.errorCode = errorCode;
  }

  getErrorCode(): string {
    return this.errorCode;
  }

  getStatus(): number {
    return HttpStatus.BAD_REQUEST;
  }
}
