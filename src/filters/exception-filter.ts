import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { Response, Request } from 'express';

@Catch(HttpException)
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
    }

    if (exception.response?.message) {
      const devMessage = exception.response.message;
      const respMsg = Array.isArray(exception.response.message)
        ? exception.response.message[0]
        : exception.response.message;

      if (exception.response.message.includes('Cart not found')) {
        return response.status(status).json({
          message: respMsg || 'Internal server error',
          isError: true,
          status,
          devMessage: devMessage,
          code: 'CART_NOT_FOUND',
          timestamp: new Date().toISOString(),
          path: request.url,
        });
      }

      if (status === 400) {
        if (exception instanceof CustomBadRequestException) {
          const code = exception?.getErrorCode();
          return response.status(status).json({
            message: respMsg,
            isError: true,
            status,
            code: code,
            devMessage: devMessage,
            timestamp: new Date().toISOString(),
            path: request.url,
          });
        }
        return response.status(status).json({
          message: 'Request could not be processed due to an error.',
          isError: true,
          status,
          code: 'VALIDATION_ERROR',
          devMessage: devMessage,
          timestamp: new Date().toISOString(),
          path: request.url,
        });
      }

      return response.status(status).json({
        message: respMsg || 'Internal server error',
        isError: true,
        status,
        devMessage: devMessage,
        timestamp: new Date().toISOString(),
        path: request.url,
      });
    }

    return response.status(status).json({
      isError: true,
      status,
      timestamp: new Date().toISOString(),
      message: exception.response,
    });
  }
}
