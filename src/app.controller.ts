import {
  Body,
  Controller,
  Get,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CartUpdateService } from './utils/cart-webhook-service';
import { WebhookApiGuard } from './guards/webhook-api-guard';
import { WebhookInterceptor } from './interceptors/cart-webhook-interceptor';

@Controller()
export class AppController {
  constructor(private readonly cartUpdateService: CartUpdateService) {}

  @Get('/health')
  healthCheck(): string {
    return 'Working successfully prod';
  }

  @UseGuards(WebhookApiGuard)
  @UseInterceptors(WebhookInterceptor)
  @Post('update/newCart')
  async updateCart(@Body() data: any) {
    return data?.cart
      ? await this.cartUpdateService.updateCart(data.cart)
      : false;
  }
}
