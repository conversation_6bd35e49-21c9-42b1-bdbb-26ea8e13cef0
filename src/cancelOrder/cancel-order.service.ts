import {
  Injectable,
  InternalServerErrorException,
  OnModuleInit,
} from '@nestjs/common';
import { NotifyERP } from 'src/utils/notify-erp';
import { SalesOrder } from '../database/entities/sales-order';
import { SalesOrderItem } from '../database/entities/sales-order-item';
import { SalesOrderPayment } from '../database/entities/sales-order-payment';
import { SalesOrderAddress } from '../database/entities/sales-order-address';
import { OrderStatuses } from 'src/config/constants';
import { RmqService } from 'src/utils/rmq.service';
import { logger } from '../utils/service-logger';
import { CANCEL_ORDER_QUEUE_OPTIONS } from '../config/constants';
import { SalesOrderExtraInfo } from 'src/database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from 'src/database/entities/order-item-extra-info';
import { SalesOrderAmountPromotion } from 'src/database/entities/sales-order-amount-promotion';
import { SalesOrderItemPromotion } from 'src/database/entities/sales-order-item-promotion';

@Injectable()
export class CancelOrderService implements OnModuleInit {
  constructor(
    private readonly notifyERP: NotifyERP,
    private readonly rmqService: RmqService,
  ) {
    this.CancelOrderSatus = this.CancelOrderSatus.bind(this);
  }

  async onModuleInit() {
    await this.rmqService.consumer(
      {
        exchangeName: CANCEL_ORDER_QUEUE_OPTIONS.EXCHANGE_NAME,
        queue: CANCEL_ORDER_QUEUE_OPTIONS.QUEUE,
        exchangeType: CANCEL_ORDER_QUEUE_OPTIONS.EXCHANGE_TYPE,
        routingKey: CANCEL_ORDER_QUEUE_OPTIONS.ROUTING_KEY,
      },
      this.CancelOrderSatus,
    );
  }

  async CancelOrderSatus(consumerData: any) {
    try {
      const data = JSON.parse(consumerData);

      console.log(
        '++++++_cancel_req_data+++++++++',
        JSON.stringify(data),
        '++++++_cancel_req_data+++++++++',
      );

      if (!data?.order_id)
        throw new InternalServerErrorException('order_id is mandatory');
      if (!data?.is_full_cancel) return;
      const order: SalesOrder = await SalesOrder.findOne({
        where: {
          increment_id: data.order_id,
        },
      });
      if (!order) throw new InternalServerErrorException('order_id not found');
      if (
        [
          OrderStatuses.PAYMENT_PENDING,
          OrderStatuses.NEW_ORDER,
          OrderStatuses.AUTO_INVOICED,
        ].indexOf(order.status) !== -1 &&
        data?.can_origin_order_cancel
      ) {
        const updatedData = await this.updateStatus(
          OrderStatuses.CANCELLED,
          data.order_id,
        );
        console.log(
          `+++++${data.order_id}-updated++++++++`,
          JSON.stringify(updatedData),
          `++++++${data.order_id}-updated+++++++++`,
        );
        if (updatedData) {
          const orderData = await SalesOrder.findByPk(order.order_id, {
            include: [
              SalesOrderAddress,
              SalesOrderPayment,
              SalesOrderAmountPromotion,
              {
                model: SalesOrderExtraInfo,
                attributes: ['registration_no', 'processed_at'],
              },
              {
                model: SalesOrderItem,
                include: [
                  {
                    model: OrderItemExtraInfo,
                    attributes: ['referral_code', 'is_free_product'],
                  },
                  SalesOrderItemPromotion,
                ],
              },
            ],
          });

          this.notifyERP.notifyStatusUpdate(
            orderData,
            orderData.items,
            orderData.address,
            orderData.payment,
          );
        }
      }
      logger.info(`${data.order_id} cancelled successfully.`);
      return;
    } catch (e) {
      logger.error('Error in CancelOrderSatus', e);
    }
  }

  async updateStatus(orderStatus: string, orderId: string) {
    return await SalesOrder.update(
      { status: orderStatus },
      { where: { increment_id: orderId } },
    );
  }
}
