import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { defaultCarrierConfig, QuoteConfig } from '../config/constants';
import { CarriersConfig } from '../database/entities/carriers_config';
import { ShippingRates } from '../database/entities/shipping_rate';
import { ShippinMethodArgs } from '../interface/shipping-method-argument';
import * as _ from 'lodash';

@Injectable()
export class ShippingMethodService {
  /**
   * It update defaultCarrierConfig and save it to the db
   * @param updatedData carriers object with modified keys
   * @returns
   */
  async UpDateCarriersConfigObject(updatedData?: any) {
    let config = await CarriersConfig.findOne({});
    if (!config) {
      config = await CarriersConfig.create({
        carriers: defaultCarrierConfig.carriers,
      });
    }
    try {
      if (updatedData?.carriers) {
        Object.keys(updatedData.carriers).map((key) => {
          if (key in config.carriers) {
            Object.keys(updatedData.carriers[key]).map((subKey) => {
              config.carriers[key][subKey] = updatedData.carriers[key][subKey];
              return null;
            });
          }
          return null;
        });
        await CarriersConfig.update(
          { carriers: config.carriers },
          {
            where: { id: config.id },
          },
        );
        return await CarriersConfig.findByPk(config.id);
      }
      return config;
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  /**
   * It returns shipping_carriers array along with appropriate delivery charges
   * @param args input argument of type shippinMethodArgs
   * @returns
   */
  async getShippingMethods(args: ShippinMethodArgs) {
    try {
      const carrier_methods = [];
      const carriers_config: CarriersConfig = await CarriersConfig.findOne({});
      if (!carriers_config?.carriers) return carrier_methods;
      for (const key in carriers_config.carriers) {
        if (key === 'tablerate') {
          if (
            carriers_config.carriers[key].active === '1' &&
            (carriers_config.carriers[key].sallowspecific === '0' ||
              (carriers_config.carriers[key].specificcountry &&
                carriers_config.carriers[key].specificcountry
                  .split(',')
                  .indexOf(args.countryCode) !== -1))
          ) {
            carrier_methods.push({
              method_code: key,
              carrier_code: key,
              carrier_title: carriers_config.carriers[key].title,
              method_title: carriers_config.carriers[key].title,
              charges: await this.getDeliveryCharges(args),
              currency: QuoteConfig.defaultCurrencyCode,
              sort_order: +carriers_config.carriers[key].sort_order,
            });
          }
        }
        if (key === 'freeshipping') {
          if (
            carriers_config.carriers[key].active === '1' &&
            (carriers_config.carriers[key].sallowspecific === '0' ||
              (carriers_config.carriers[key].specificcountry &&
                carriers_config.carriers[key].specificcountry
                  .split(',')
                  .indexOf(args.countryCode) !== -1)) &&
            args.subTotal >
              +carriers_config.carriers[key].free_shipping_subtotal
          ) {
            carrier_methods.push({
              method_code: key,
              carrier_code: key,
              carrier_title: carriers_config.carriers[key].title,
              method_title: carriers_config.carriers[key].title,
              charges: 0,
              currency: QuoteConfig.defaultCurrencyCode,
              sort_order: +carriers_config.carriers[key].sort_order,
            });
          }
        }
      }
      return _.sortBy(carrier_methods, ['sort_order']);
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  /**
   * It returns delivery charges for specific country coressponding to weightslab
   * @param args input argument of type shippinMethodArgs
   * @returns
   */
  async getDeliveryCharges(args: ShippinMethodArgs) {
    const country_price_list: ShippingRates[] = await ShippingRates.findAll({
      where: { dest_country_id: args.countryCode },
    });
    if (country_price_list.length === 0)
      throw new BadRequestException(
        `Service currently unavailable at your country`,
      );
    if (country_price_list.length === 1) return +country_price_list[0].price;
    const weight = +(args.weight * 1000).toFixed(2);
    if (
      weight >=
      country_price_list[country_price_list.length - 1].condition_value
    ) {
      return +country_price_list[country_price_list.length - 1].price;
    }
    for (let i = 0; i < country_price_list.length; i++) {
      if (
        weight >= +country_price_list[i].condition_value &&
        weight <= +country_price_list[i + 1].condition_value
      ) {
        return +country_price_list[i].price;
      }
    }
    return 0;
  }

  async weightSlabsForCountries(country_code: string) {
    try {
      const rows = await ShippingRates.findAll({
        where: {
          dest_country_id: country_code,
        },
      });

      return {
        data: rows.map((data, index) => ({
          currency: 'INR',
          price: data.price,
          weight: data.condition_value,
          weight_range: this.getWeightRange(
            data.condition_value,
            rows?.[index + 1]?.condition_value,
          ),
        })),
      };
    } catch (e) {
      throw new InternalServerErrorException(e?.message);
    }
  }

  getWeightRange(min: number, max: number) {
    return max ? `${min}-${max}` : `>${min}`;
  }
}
