import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ShippingMethodService } from './shipping-method.service';
import { WebhookApiGuard } from '../guards/webhook-api-guard';

@Controller('shippingMethod')
export class ShippingMethodController {
  constructor(private readonly shippingMethodService: ShippingMethodService) {}

  @UseGuards(WebhookApiGuard)
  @Post('/updateCarrierObject')
  async UpDateCarriersConfigObject(@Body() data: any) {
    return await this.shippingMethodService.UpDateCarriersConfigObject(data);
  }
}
