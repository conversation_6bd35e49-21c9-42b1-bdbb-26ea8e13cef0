type CartTaxItemV2 {
  amount: CartMoneyV2!
  label: String!
}

type DiscountV2 {
  amount: CartMoneyV2!
  label: String!
}
type CartDiscountV2 {
  amount: CartMoneyV2!
  label: [String]!
}

type CartMoneyV2 {
  currency: String
  currency_symbol: String
  value: Float
}

type RewardDiscountV2 {
  amount: CartMoneyV2
  label: String
}

type CartPricesV2 {
  applied_taxes: [CartTaxItemV2]
  discount: CartDiscountV2
  discounts: [DiscountV2]
  grand_total: CartMoneyV2
  overweight_delivery_charges: CartMoneyV2
  rewardsdiscount: RewardDiscountV2
  subtotal_excluding_tax: CartMoneyV2
  subtotal_including_tax: CartMoneyV2
  subtotal_with_discount_excluding_tax: CartMoneyV2
  total_savings: CartMoneyV2
}

type AppliedCouponV2 {
  code: String!
}

type SelectedShippingMethodV2 {
  amount: CartMoneyV2!
  base_amount: CartMoneyV2
  carrier_code: String!
  carrier_title: String!
  method_code: String!
  method_title: String!
}

type CartItemInterfaceV3 {
  brand_image: String
  discount: Float
  is_free_product: Boolean
  error_messages: [ItemErrorsV2]
  id: String!
  prices: CartItemPricesV2
  product: ProductInterfaceV2!
  qty_increments: Float
  quantity: Float!
  reward_point_product: Int
  stock_status: Int
  updated_at: String
}

type CartItemPricesV2 {
  discounts: [DiscountV2]
  price: CartMoneyV2!
  row_total: CartMoneyV2!
  row_total_including_tax: CartMoneyV2!
  total_item_discount: CartMoneyV2
}

type AvailablePaymentMethodV2 {
  code: String!
  title: String!
}

type CartAddressCountryV2 {
  code: String
  label: String
}

type ComplexTextValueV2 {
  html: String
}

type ProductImageV2 {
  label: String
  url: String
}

type ProductMediaGalleryEntriesVideoContentV2 {
  media_type: String
  video_description: String
  video_metadata: String
  video_provider: String
  video_title: String
  video_url: String
}

type CartPriceV2 {
  amount: CartMoneyV2
}

type CartProductPricesV2 {
  maximalPrice: CartPriceV2
  minimalPrice: CartPriceV2
  regularPrice: CartPriceV2
}

type MediaGalleryEntryV2 {
  disabled: Boolean
  file: String
  id: Int
  label: String
  media_type: String
  position: Int
  types: [String]
  video_content: ProductMediaGalleryEntriesVideoContentV2
}

enum ProductStockStatusV2 {
  IN_STOCK
  OUT_OF_STOCK
}

type ProductTierPricesV2 {
  customer_group_id: String
  percentage_value: Float
  qty: Float
  value: Float
  website_id: Float
}

type ProductInterfaceV2 {
  average_rating: String
  categories: [CategoryInterfaceV3]
  dentalkart_custom_fee: Float
  description: ComplexTextValueV2
  dispatch_date: String
  dispatch_days: Int
  expiry: String
  gift_message_available: String
  id: Int
  image: ProductImageV2
  is_cod: Int
  manufacturer: Int
  max_sale_qty: Int
  media_gallery_entries: [MediaGalleryEntryV2]
  meta_description: String
  meta_keyword: String
  meta_title: String
  msrp: Float
  name: String
  only_x_left_in_stock: Float
  pd_expiry_date: String
  price: CartProductPricesV2
  rating_count: String
  reward_point_product: Int
  short_description: ComplexTextValueV2
  sku: String
  small_image: ProductImageV2
  special_price: Float
  stock_status: ProductStockStatusV2
  thumbnail: ProductImageV2
  tier_prices: [ProductTierPricesV2]
  type_id: String
  url_key: String
  url_path: String
  weight: Float
}

interface CategoryInterfaceV3 {
  app_link: String
  id: Int
  level: Int
  name: String
  position: Int
  url_path: String
}

type AvailableShippingMethodV2 {
  amount: CartMoneyV2!
  available: Boolean!
  base_amount: CartMoneyV2
  carrier_code: String!
  carrier_title: String!
  error_message: String
  method_code: String
  method_title: String
  price_excl_tax: CartMoneyV2!
  price_incl_tax: CartMoneyV2!
}

type CartAddressRegionV2 {
  code: String
  label: String
}

type ShippingCartAddressV2 {
  available_shipping_methods: [AvailableShippingMethodV2]
  city: String
  company: String
  country: CartAddressCountryV2!
  customer_notes: String
  firstname: String
  items_weight: Float
  lastname: String
  postcode: String
  region: CartAddressRegionV2
  selected_shipping_method: SelectedShippingMethodV2
  street: [String]
  telephone: String
  customer_address_id: Int
}

type ItemErrorsV2 {
  code: String
  message: String
}

type CartV2 {
  applied_coupon: AppliedCouponV2
  applied_coupons: [AppliedCouponV2]
  email: String
  global_errors: String
  global_errors_v2: [ItemErrorsV2]
  id: ID!
  is_virtual: Boolean!
  items: [CartItemInterfaceV3]
  prices: CartPricesV2
  shipping_addresses: [ShippingCartAddressV2]!
  available_payment_methods: [AvailablePaymentMethodV2]
  total_quantity: Float!
}

type AddProductsToCartOutput {
  cart: CartV2!
}

input SetGuestEmailOnCartInputV2 {
  cart_id: String!
  email: String!
}

type CartOutput {
  cart: CartV2!
}

input BillingAddressInputV2 {
  address: CartAddressInputV2
  customer_address_id: Int
  same_as_shipping: Boolean
  use_for_shipping: Boolean
}

input CartAddressInputV2 {
  alternate_mobile: String
  city: String
  company: String
  country_code: String!
  firstname: String
  lastname: String
  postcode: String
  region: String
  save_in_address_book: Boolean
  street: [String]
  telephone: String
  region_code: String
  region_id: Int
  gst_id: String
}

input CreateEmptyCartInputV2 {
  cart_id: String
}

input CartItemInputV2 {
  quantity: Float!
  sku: String!
  referral_code: String
  parent_id: Int
}

input SimpleProductCartItemInputV2 {
  data: CartItemInputV2!
}

input AddProductsToCartInputV2 {
  cart_id: String!
  cart_items: [SimpleProductCartItemInputV2]!
}

input ApplyCouponToCartInputV2 {
  cart_id: String
  coupon_code: String
  is_buy_now_cart: Boolean
}

input RemoveCouponFromCartInputV2 {
  cart_id: String!
  is_buy_now_cart: Boolean
}

input SetBillingAddressOnCartInputV2 {
  billing_address: BillingAddressInputV2!
  cart_id: String!
}

input RemoveItemFromCartInputV2 {
  cart_id: String!
  cart_item_id: Int!
}

input ShippingAddressInputV2 {
  address: CartAddressInputV2
  customer_address_id: Int
  customer_notes: String
}

input SetShippingAddressesOnCartInputV2 {
  cart_id: String!
  shipping_addresses: [ShippingAddressInputV2]!
}

input CartItemUpdateInputV2 {
  cart_item_id: Int!
  quantity: Float
}

input UpdateCartItemsInputV2 {
  cart_id: String!
  cart_items: [CartItemUpdateInputV2]!
}

type UpdateCartItemsOutputV2 {
  cart: CartV2!
}

type ApplicableRewardPointsOutput {
  max_applied_points: Float
}

type GetAllItemPromotionProducts {
  product_sku: String
  free_product_sku: String
  free_product_quantity: String
  buy_qty: String
  parent_sku: String
  parent_id: Int
}

type GroupPromotionMessages {
  sku: String!
  message: String!
}

type GetPromotionalOfferOutput {
  message: String
  group_messages: [GroupPromotionMessages]
}

input BuyNowInput {
  cart_items: [SimpleProductCartItemInputV2]
  shipping_address: BuyNowAddressInput
}

input BuyNowAddressInput {
  address: CartAddressInputV2
}

input SetShippingAddressOnBuyNowCart {
  shipping_address: BuyNowAddressInput!
  cart_id: String!
}

type CustomerCoupon {
  coupon_code: String
  description: String
  expiry_date: String
}

type Mutation {
  createEmptyCartV2(input: CreateEmptyCartInputV2): String
  addSimpleProductsToCartV2(
    input: AddProductsToCartInputV2
  ): AddProductsToCartOutput
  addVirtualProductsToCartV2(
    input: AddProductsToCartInputV2
  ): AddProductsToCartOutput
  setGuestEmailOnCartV2(input: SetGuestEmailOnCartInputV2): CartOutput
  setBillingAddressOnCartV2(input: SetBillingAddressOnCartInputV2): CartOutput
  removeItemFromCartV2(input: RemoveItemFromCartInputV2): CartOutput
  mergeCartsV2(destination_cart_id: String!, source_cart_id: String!): CartV2!
  setShippingAddressesOnCartV2(
    input: SetShippingAddressesOnCartInputV2
  ): CartOutput
  applyCouponToCartV2(input: ApplyCouponToCartInputV2): CartOutput
  dkApplyRewardPointsV2(
    rewardpoints: Int!
    is_buy_now_cart: Boolean
    cart_id: String
  ): CartOutput
  removeCouponFromCartV2(input: RemoveCouponFromCartInputV2): CartOutput
  updateCartItemsV2(input: UpdateCartItemsInputV2): UpdateCartItemsOutputV2
  buyNow(input: BuyNowInput): CartOutput
  setShippingOnBuyNowCart(input: SetShippingAddressOnBuyNowCart!): CartOutput
  mergeBuyNowCart(
    customer_cart_id: String!
    buy_now_cart_id: String!
  ): CartOutput
}

type Query {
  cartV2(cart_id: String): CartV2!
  getAllItemPromotionProducts: [GetAllItemPromotionProducts]
  getItemPromotionOfferBySku(
    sku: String
    parent_id: Int
  ): GetPromotionalOfferOutput
  getAmountPromotionByCartValue(value: Float!): GetPromotionalOfferOutput
  CustomerCoupons(cart_id: String, is_buy_now_cart: Boolean): [CustomerCoupon]
  applicableRewardPointsV2(
    cart_id: String
    is_buy_now_cart: Boolean
  ): ApplicableRewardPointsOutput
}
