import { ExternalApiHelper } from './utils/external-api.helper';
import { logger } from './utils/service-logger';
import { CartService } from './utils/cart.service';
import { BuyNowService } from './buy-now/buy-now.service';
import { Quote } from './database/entities/quote';
import { Request } from 'express';
import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Body,
  ParseIntPipe,
  BadRequestException,
  Req,
  Post,
  ValidationPipe,
  Query,
  UseInterceptors,
  UseFilters,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  UpdateCartItemBody,
  AddItemsToCartBody,
} from 'src/interface/add-product-to-cart-request';
import {
  BuyNowProdRequestBody,
  BuyNowProdRequest,
  BuyNowBody,
} from './interface/buy-now-add-request';
import { ResponseMapperInterceptor } from './interceptors/response.interceptor';
import {
  SetShiipingAddressOnCartBody,
  SetEmailOnGuestCartBody,
} from 'src/interface/set-shipping-address-on-cart-request';
import { CartAddressService } from './utils/cart-address-service';
import { RewardService } from './utils/reward-service';
import { CouponService } from './utils/coupon-service';
import { ApplyDiscountBody } from 'src/interface/apply-cooupon-api-request';
import { MergeCartApiRequestBody } from 'src/interface/merge-carts-request';
import { OutputResponseType } from 'src/config/constants';
import { OrderServiceApiService } from 'src/order-service-api/order-service-api.service';
import { InactiveCartGuard } from 'src/guards/admin-token-guard';
import { AmountPromotionService } from 'src/cart-promotion/amount-promotion/amount-promotion.service';
import { ShippingMethodService } from 'src/shipping-method/shipping-method.service';
import { AllExceptionsFilter } from './filters/exception-filter';
import { DeleteItemQueryDto } from './interface/delete-item-request';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { ItemPromotionService } from 'src/cart-promotion/item-promotion/item-promotion.service';
import { ItemPromotionQueryDTO } from 'src/interface/item-promotion-request';
import { BillingAddressDto } from './interface/set-billing-address-on-cart';
@Controller('cart/api/v1/carts')
@UseFilters(AllExceptionsFilter)
export class CartController {
  constructor(
    private readonly cartService: CartService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly buyNowService: BuyNowService,
    private readonly cartAddressService: CartAddressService,
    private readonly couponService: CouponService,
    private readonly rewardService: RewardService,
    private readonly orderApiService: OrderServiceApiService,
    private readonly amountPromotionService: AmountPromotionService,
    private readonly shippingMethodService: ShippingMethodService,
    private readonly itemPromotionService: ItemPromotionService,
  ) {}

  @Get('/amount-promotions')
  amountPromotionsList() {
    return this.amountPromotionService.allActiveAmountPromotions();
  }

  @Get('/item-promotions')
  async itemPromotionsList(
    @Query(new ValidationPipe({ transform: true }))
    query: ItemPromotionQueryDTO,
  ) {
    const { parent_id, sku } = query;
    let itemPromotions = null;
    if (parent_id || sku) {
      itemPromotions = await this.itemPromotionService.getItemPromotionOffer(
        sku,
        parent_id,
      );
      return { data: itemPromotions };
    }
    itemPromotions =
      await this.itemPromotionService.getAllItemPromotionProductsGQL();

    return { data: itemPromotions };
  }

  @Get(':cart_id/coupons')
  async CustomerCoupons(
    @Param('cart_id') cart_id: string,
    @Query('buy_now') buy_now: string,
    @Req() request: Request,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists)
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );

    // const data = await this.couponService.customerCoupons(
    //   cart_id,
    //   buy_now === 'true',
    //   customerExists.customer_id,
    //   customerExists.group_id,
    // );

    const couponsList = await this.couponService.customerCoupons(
      cart_id,
      buy_now === 'true',
      customerExists.customer_id,
      customerExists.group_id,
    );

    return { data: couponsList };
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Get(':cart_id')
  async getCart(@Param('cart_id') cart_id: string, @Req() request: Request) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      if (!cart_id) throw new BadRequestException('Please provide cart_id.');
      const data = await this.cartService.getCartData(
        cart_id,
        null,
        null,
        null,
        OutputResponseType.REST,
      );
      return { cart: data };
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
      cart_id,
    );

    if (!customerExists) {
      // logger.info({
      //   message: 'Customer not found',
      //   inputToken: authToken,
      // });
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }

    const data = await this.cartService.getCartData(
      cart_id,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
    );
    return { cart: data };
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Delete(':cart_id/items/:item_id')
  async removeCartItem(
    @Param('cart_id') cart_id: string,
    @Param('item_id', ParseIntPipe) item_id: number,
    @Req() request: Request,
    @Query(new ValidationPipe({ transform: true })) query: DeleteItemQueryDto,
  ) {
    const { buy_now } = query;
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      return this.cartService.removeItemFromCart(
        {
          cart_id,
          cart_item_id: item_id,
        },
        null,
        null,
        null,
        OutputResponseType.REST,
      );
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }

    return this.cartService.removeItemFromCart(
      { cart_id, cart_item_id: item_id },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
      buy_now,
    );
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Put(':cart_id/items')
  async updateCartItems(
    @Param('cart_id') cart_id: string,
    @Body(new ValidationPipe({ transform: true })) body: UpdateCartItemBody,
    @Req() request: Request,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );
    if (!authToken) {
      // for guest user
      return this.cartService.updateCartItems(
        {
          cart_id,
          cart_items: body.cart_items,
        },
        null,
        null,
        null,
        false,
        OutputResponseType.REST,
      );
    }
    // for registered user
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    return await this.cartService.updateCartItems(
      { cart_id, cart_items: body.cart_items },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      false,
      OutputResponseType.REST,
      body.buy_now,
    );
  }

  @Post()
  async generateCartId(@Req() req: Request) {
    const authToken = this.cartService.getUserToken(
      req.headers['authorization'],
    );
    const ip: any = req.headers['x-forwarded-for'];
    if (!authToken) {
      const cartId = await this.cartService.createGuestCart(ip);
      return { cart_id: cartId };
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    const customerCart = await Quote.findOne({
      where: { customer_id: customerExists.id, is_active: true },
    });

    if (customerCart) return { cart_id: customerCart.masked_id };
    const customerTaxClassId =
      await this.externalApiHelper.getCustomerTaxClassFromGroup(
        customerExists.group_id,
      );

    const cartId = await this.cartService.createCart(
      ip,
      customerExists,
      customerTaxClassId,
    );

    return { cart_id: cartId };
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post(`:cart_id/items`)
  async addItemsToCart(
    @Param('cart_id') cart_id: string,
    @Body(new ValidationPipe({ transform: true })) body: AddItemsToCartBody,
    @Req() request: Request,
    @Query('buying_guide') buying_guide: string,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      //send easy insight data
      this.externalApiHelper
        .sendEasyInsightsData(request, body, null, 'add_to_cart')
        .catch((error) => {
          logger.error({
            message: 'Failed to send EasyInsights data',
            error: error?.message || error,
          });
        });
      // for guest user
      return this.cartService.addProductsToCart(
        {
          cart_id,
          cart_items: body.cart_items,
        },
        null,
        null,
        OutputResponseType.REST,
        buying_guide === 'true',
      );
    }
    // for registered user
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
      cart_id,
    );

    if (!customerExists) {
      // logger.info({
      //   message: 'Customer not found',
      //   inputToken: authToken,
      //   cartId: cart_id,
      // });
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }

    //send easy insight data
    this.externalApiHelper
      .sendEasyInsightsData(request, body, customerExists.id, 'add_to_cart')
      .catch((error) => {
        logger.error({
          message: 'Failed to send EasyInsights data',
          error: error?.message || error,
        });
      });

    return this.cartService.addProductsToCart(
      { cart_id, cart_items: body.cart_items },
      customerExists.id,
      customerExists.group_id,
      OutputResponseType.REST,
      buying_guide === 'true',
    );
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post(':cart_id/merge')
  async mergeCarts(
    @Param('cart_id') cart_id: string,
    @Req() request: Request,
    @Body(new ValidationPipe({ transform: true }))
    body: MergeCartApiRequestBody,
  ) {
    const { destination_cart_id, buy_now } = body;
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException('Invalid auth token');
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    if (buy_now) {
      return this.buyNowService.mergeBuyNowCartItems(
        cart_id,
        destination_cart_id,
        customerExists,
        OutputResponseType.REST,
      );
    }

    const data = await this.cartService.mergeCarts(
      { source_cart_id: cart_id, destination_cart_id: destination_cart_id },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
    );

    return { cart: data };
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post('/buy-now')
  async buyNow(
    @Req() request: Request,
    @Body(new ValidationPipe({ transform: true })) body: BuyNowProdRequestBody,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    const req = {
      cart_items: body.cart_items,
      shipping_address: {
        address: {
          country_code: body?.country_code || 'IN',
        },
      },
    } as BuyNowProdRequest;
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    return this.buyNowService.addProdToBuyNowCart(
      '',
      req,
      customerExists,
      OutputResponseType.REST,
    );
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post(':cart_id/addresses')
  async setShippingAddressesOnCart(
    @Param('cart_id') cart_id: string,
    @Req() request: Request,
    @Body(new ValidationPipe({ transform: true }))
    body: SetShiipingAddressOnCartBody,
  ) {
    const { shipping_addresses, buy_now, billing_address } = body;
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );
    if (!authToken) {
      return this.cartAddressService.setShippingAddressOnCart(
        {
          cart_id: cart_id,
          shipping_addresses: shipping_addresses,
        },
        null,
        null,
        null,
        OutputResponseType.REST,
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }

    if (buy_now) {
      return this.buyNowService.setShipingAddressOnBuyNowCart(
        shipping_addresses[0],
        cart_id,
        customerExists,
        OutputResponseType.REST,
        billing_address,
      );
    }

    return this.cartAddressService.setShippingAddressOnCart(
      {
        cart_id: cart_id,
        shipping_addresses: shipping_addresses,
        billing_address: billing_address,
      },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
    );
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post(':cart_id/apply-discount-element')
  async applyDiscountToCart(
    @Param('cart_id') cart_id: string,
    @Req() request: Request,
    @Body(new ValidationPipe({ transform: true }))
    applyDiscountBody: ApplyDiscountBody,
  ) {
    const { coupon_code, reward_points, buy_now } = applyDiscountBody;
    if (!coupon_code && reward_points === undefined) {
      throw new BadRequestException(
        'Please provide discount element to apply.',
      );
    }

    if (coupon_code && reward_points !== undefined) {
      throw new BadRequestException(
        'Only one discount element can be applied at once.',
      );
    }

    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );

    if (coupon_code && !authToken) {
      return this.couponService.applyCouponToCart(
        {
          cart_id,
          coupon_code,
        },
        null,
        null,
        null,
        OutputResponseType.REST,
      );
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }

    if (coupon_code) {
      return await this.couponService.applyCouponToCart(
        {
          cart_id,
          coupon_code,
          is_buy_now_cart: buy_now,
        },
        customerExists.id,
        customerExists.magento_customer_id,
        customerExists.group_id,
        OutputResponseType.REST,
      );
    }

    //apply reward
    return this.rewardService.applyRewardPoints(
      {
        cart_id,
        rewardpoints: +reward_points,
        is_buy_now_cart: buy_now,
      },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
    );
  }

  @UseInterceptors(ResponseMapperInterceptor)
  @Post(':cart_id/remove-coupon')
  async removeCouponFromCart(
    @Param('cart_id') cart_id: string,
    @Req() request: Request,
    @Body(new ValidationPipe({ transform: true }))
    buyNow: BuyNowBody,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );
    if (!authToken) {
      return await this.couponService.removeCouponFromCart(
        {
          cart_id,
        },
        null,
        null,
        null,
        OutputResponseType.REST,
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    return await this.couponService.removeCouponFromCart(
      {
        cart_id,
        is_buy_now_cart: buyNow.buy_now,
      },
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
      OutputResponseType.REST,
    );
  }

  @Get(':cart_id/reward-points')
  async getApplicableRewardPoints(
    @Param('cart_id') cart_id: string,
    @Query(new ValidationPipe({ transform: true })) query: DeleteItemQueryDto,
    @Req() request: Request,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    return await this.rewardService.applicableRewardPoints(
      customerExists.customer_id,
      { cart_id, is_buy_now_cart: query.buy_now },
    );
  }

  @Patch(':cart_id/email')
  async setEmailOnGuestCart(
    @Param('cart_id') cart_id: string,
    @Body(new ValidationPipe({ transform: true }))
    body: SetEmailOnGuestCartBody,
  ) {
    return this.cartAddressService.setEmailOnGuestCart(cart_id, body.email);
  }

  @Get(':cart_id/payment-info')
  async fetchCartPaymentDetails(
    @Param('cart_id') cart_id: string,
    @Query('buy_now') buy_now: string,
    @Req() request: Request,
  ) {
    const authToken = this.cartService.getUserToken(
      request.headers['authorization'],
    );
    if (!authToken) {
      return this.orderApiService.fetchCartAndPaymentInformation(
        cart_id,
        null,
        false,
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) {
      throw new CustomBadRequestException(
        VALIDATION_ERRORS['invalid_token'].message,
        VALIDATION_ERRORS['invalid_token'].errorCode,
      );
    }
    return this.orderApiService.fetchCartAndPaymentInformation(
      cart_id,
      customerExists,
      buy_now === 'true',
    );
  }
  @UseGuards(InactiveCartGuard)
  @Put(':cart_id/inactive')
  async inactiveCart(
    @Param('cart_id') cart_id: string,
    @Query('buy_now') buy_now: string,
  ) {
    return this.orderApiService.inActiveCart(cart_id, buy_now === 'true');
  }

  @Get('countries/:country_id/shipping-rates')
  async WeightSlabsForCountries(@Param('country_id') country_id: string) {
    if (!country_id) {
      throw new BadRequestException('please Provide country_id');
    }
    return this.shippingMethodService.weightSlabsForCountries(country_id);
  }
}
