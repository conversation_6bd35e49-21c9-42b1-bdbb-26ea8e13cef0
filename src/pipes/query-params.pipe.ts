import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import * as moment from 'moment';
@Injectable()
export class ParseQueryPipe implements PipeTransform {
  constructor(private readonly defaultValue: number = 0) {}

  transform(value: any, metadata: ArgumentMetadata) {
    const val = parseInt(value, 10);
    return isNaN(val) ? this.defaultValue : val;
  }
}

@Injectable()
export class ParseDatePipe implements PipeTransform<string, string> {
  transform(value: string, metadata: ArgumentMetadata): string {
    // Validate if the value is a valid date format using moment.js
    if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
      return '';
    }
    return value;
  }
}
