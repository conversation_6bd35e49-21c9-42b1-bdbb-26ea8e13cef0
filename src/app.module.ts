import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module, ValidationPipe } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CartResolver } from './cart.resolver';
import { DatabaseModule } from './database/database.module';
import { UtilsModule } from './utils/utils.module';
import { ShippingMethodModule } from './shipping-method/shipping-method.module';
import { OrderServiceApiModule } from './order-service-api/order-service-api.module';
import { OrderServiceAdminController } from './order-service-api/order-service-admin.controller';
import { APP_FILTER, APP_PIPE } from '@nestjs/core';
import { HttpExceptionFilter } from './filters';
import { AmountPromotionModule } from './cart-promotion/amount-promotion/amount-promotion.module';
import { ItemPromotionModule } from './cart-promotion/item-promotion/item-promotion.module';
import { CartPromotionModule } from './cart-promotion/cart-promotion.module';
import { BuyNowModule } from './buy-now/buy-now.module';
import { CartController } from './cart.controller';

@Module({
  imports: [
    DatabaseModule,
    UtilsModule,
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      playground: true,
      typePaths: ['./**/*.graphql'],
      definitions: {
        path: join(process.cwd(), 'src/graphql.ts'),
      },
    }),
    ShippingMethodModule,
    OrderServiceApiModule,
    CartPromotionModule,
    AmountPromotionModule,
    ItemPromotionModule,
    BuyNowModule,
  ],
  controllers: [AppController, CartController],
  providers: [AppService, CartResolver],
})
export class AppModule {}
