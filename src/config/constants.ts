/**
 * Possible user-types for place-order Action
 */
export enum UserTypes {
  GUEST = 'guest',
  REGISTERED = 'registered',
}

/**
 * Possible address-types associated with an order
 */
export enum AddressTypes {
  BILLING = 'billing',
  SHIPPING = 'shipping',
}

/**
 * Some available payment methods
 */
export enum PaymentMethods {
  RAZOR_PAY = 'razorpay',
  COD = 'cashondelivery',
}

/**
 * Hard coded admin global configurations
 */
export const AdminGlobalConfig = {
  defaultStoreId: 1,
  defaultStoreCurrencyCode: 'INR',
  baseCurrencyCode: 'INR',
};

/**
 * Hard coded configurations used by this service
 */
export const QuoteConfig = {
  defaultCurrencyCode: 'INR',
  defaultCurrencySymbol: '₹',
};

export enum StockStatuses {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
}

/**
 * Available product types
 */
export const ProductTypes = {
  SIMPLE: 'simple',
  VIRTUAL: 'virtual',
};

export enum ProductStatuses {
  ENABLE = 1,
  DISABLE = 2,
}

export enum SalesRulesSimpleAction {
  CART_FIXED = 'cart_fixed',
  BUY_X_GET_Y = 'buy_x_get_y',
  BY_PERCENT = 'by_percent',
  BY_FIXED = 'by_fixed',
}

export const DiscountType = {
  REWARD_DISCOUNT: 'reward_point_discount',
  COUPON_DISCOUNT: 'coupon_discount',
  REMOVE_COUPON_DISCOUNT: 'add_coupon_discount',
};

export const defaultCarrierConfig = {
  carriers: {
    flatrate: {
      active: '0',
      title: 'Delivery Charges',
      name: null,
      type: 'O',
      price: '100',
      handling_type: 'F',
      handling_fee: null,
      specificerrmsg: null,
      sallowspecific: '0',
      specificcountry: 'IN,ID,IR',
      showmethod: '0',
      sort_order: null,
    },
    tablerate: {
      active: '1',
      title: 'Delivery charge',
      name: 'Delivery Charge',
      condition_name: 'package_weight',
      include_virtual_price: '1',
      handling_type: 'F',
      handling_fee: null,
      specificerrmsg:
        'This shipping method is currently unavailable. If you would like to ship using this shipping method, please contact Us.',
      sallowspecific: '0',
      specificcountry: null,
      showmethod: '0',
      sort_order: '1',
    },
    freeshipping: {
      active: '1',
      title: 'Free shipping',
      name: 'Free',
      free_shipping_subtotal: '499',
      specificerrmsg:
        'This shipping method is currently unavailable. If you would like to ship using this shipping method, please contact us.',
      sallowspecific: '1',
      specificcountry: 'IN',
      showmethod: '0',
      sort_order: '2',
    },
  },
};

export const addressErrors = {
  NO_ADDRESS_ERR: 'No addresses found for this user. Please add a new address.',
  NO_REGION_ERR:
    'No region found for selected address. Kindly add or select valid address.',
  NO_POSTCODE_ERR:
    'No postcode found for selected address. Kindly add or select valid address.',
  NO_REGION_ID_ERR:
    'No region found for selected address. Kindly add or select valid address.',
};

export const itemsErrors = {
  out_of_stock: 'Some of the products are out of stock.',
  internationally_inactive:
    'Some of the products are unavailable in your country',
  max_qty_error:
    'Some of the products quantity exceeds maximum saleable quantity',
  disabled_product: 'Some of the products are disabled. Kindly refresh cart',
  min_qty_error:
    'Some products have minimum qty error. Kindly remove or update product.',
  max_allowed_qty: `Limited quantity available. Please order items with some lesser quantity.`,
};

export const promotionsError = {
  SKU_MATCH: 'X and Y product should be different!',
  SKU_EXIST: 'Product with this sku already exist!',
  NO_AMOUNT_PROMOTION_ERROR: 'No amount promotions data found!',
  PROMOTION_BULK_EXPIRY_FAILED: 'Promotion bulk expiry update failed!',
  NO_PROMOTION_IDS_PROVIDED: 'No promotion id provided!',
};

export const promotionsMsg = {
  PROMOTION_BULK_EXPIRY: 'Promotions expiries updated successfully',
  PROMOTION_BULK_DELETE: 'Promotions bulk deleted successfully',
};

export enum CartAction {
  ADD_TO_CART = 'add-to-cart',
  REMOVE_FROM_CART = 'remove-from-cart',
  UPDATE_CART = 'update-cart',
  GET_CART = 'get-cart',
  MERGE_CART = 'merge-cart',
  SET_SHIPPING = 'set-shipping',
  APPLY_COUPON = 'apply-coupon',
  REMOVE_COUPON = 'remove-coupon',
  APPLY_REWARD = 'apply-reward',
  REMOVE_REWARD = 'remove-reward',
  BUY_NOW_ADD = 'buy-now-add',
  BUY_NOW_MERGE = 'buy-now-merge',
  BUY_NOW_SET_SHIPPING = 'buy-now-set-shipping',
  SET_SHIPPING_BUY_NOW = 'set-shipping-buy-now',
  SET_GUEST_EMAIL = 'set-guest-email',
  SET_BILLING = 'set-billing',
}
export const SERVER_ADDED_HOURS = 5.5 * 60 * 60 * 1000; // +5:30

export const MEMBERSHIP_PRODUCTS = [40828, 41954];

export const MEMBERSHIP_SKUS = ['XXXXX00001-837', 'XXXXX00001-706'];

export enum OutputResponseType {
  GQL = 'graphql',
  REST = 'rest',
}

export const MIN_CART_SUBTOTAL_FOR_FREE_DELIVERY = 2500;

export const VALIDATION_ERRORS = {
  invalid_token: {
    message: 'Invalid token. Please login again',
    errorCode: 'invalid_token',
  },
  plan_limit_exceeded: {
    message: 'Only one plan per purchase allowed',
    errorCode: 'plan_limit_exceeded',
  },
  out_of_stock: {
    message: (name: string) => `${name} is out of stock`,
    errorCode: 'out_of_stock',
  },
  max_qty_error: {
    message: (name: string, sale_qty: number) =>
      `We have only ${sale_qty} qty left at the moment`,
    errorCode: 'max_qty_error',
  },
  max_allowed_qty: {
    message: (name: string, allowed_qty: number) =>
      `Maximum qty that you can purchase is ${allowed_qty}`,
    errorCode: 'max_allowed_qty',
  },
  min_qty_error: {
    message: (name: string, min_sale_qty: number) =>
      `Minimum qty that you can purchase is ${min_sale_qty}`,
    errorCode: 'min_qty_error',
  },
  internationally_inactive: {
    message: (name: string) => `This product is internationally inactive`,
    errorCode: 'internationally_inactive',
  },
  disabled_product: {
    message: (name: string) => `This product is disabled. You can’t add it.`,
    errorCode: 'disabled_product',
  },
  wrong_type_product: {
    message: `Only Simple or virtual type of items can be added. Please remove other type of items`,
    errorCode: 'wrong_type_product',
  },
  invalid_coupon: {
    message: `This coupon code is not valid anymore`,
    errorCode: 'invalid_coupon',
  },
  reward_limit_exceeded: {
    message: (available_point: number) =>
      `You've only ${available_point} reward points`,
    errorCode: 'reward_limit_exceeded',
  },
  already_exist_plan: {
    message: `This membership plan is already in your cart`,
    errorCode: 'same_membership_sku_addon',
  },
};
