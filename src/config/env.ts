import * as dotenv from 'dotenv';
dotenv.config();

export default {
  app: {
    host: process.env.APP_HOST,
    port: +process.env.APP_PORT,
    x_api_key: process.env.APP_X_API_KEY,
  },
  database: {
    host: process.env.MYSQL_HOST,
    port: +process.env.MYSQL_PORT,
    username: process.env.MYSQL_USERNAME,
    password: process.env.MYSQL_PASSWORD,
    database: process.env.MYSQL_DATABASE,
  },
  dk: {
    x_api_key: process.env.DK_X_API_KEY,
    guest_cart_url: process.env.DK_GUEST_CART_URL,
    appsync_url: process.env.DK_APPSYNC_URL,
    admin_token: process.env.DK_ADMIN_TOKEN,
    admin_cart_url: process.env.DK_ADMIN_CART_URL,
    user_cart_url: process.env.DK_USER_CART_URL,
    frontend_view_cart_url: process.env.DK_FRONTEND_VIEW_CART_URL,
    customer_details_url: process.env.DK_CUSTOMER_DETAILS_URL,
    prod_for_service_url: process.env.DK_PROD_FOR_SERVICE_URL,
    publish_order_url: process.env.PUBLISH_ORDER_EVENT_BUS_URL,
    publish_order_url_api_key: process.env.PUBLISH_ORDER_EVENT_BUS_URL_API_KEY,
    publish_order_secret: process.env.PUBLISH_ORDER_SECRET,
    guest_new_cart_url: process.env.DK_GUEST_NEW_CART_URL,
    admin_new_cart_url: process.env.DK_ADMIN_NEW_CART_URL,
    user_new_cart_url: process.env.DK_USER_NEW_CART_URL,
    new_customer_details_url: process.env.DK_NEW_CUSTOMER_DETAILS_URL,
    get_admin_cart_url: process.env.NEW_ADMIN_CART_URL,
    get_admin_payment_info:
      process.env.NEW_ADMIN_CART_URL + '/payment-information',
    buy_now_cart_url: process.env.BUY_NOW_CART_URL,
    buy_now_cart_token: process.env.BUY_NOW_CART_TOKEN,
    delivery_info_secret: process.env.DELIVERY_INFO_SECRET,
    cart_base_url: process.env.DK_CART_BASE_URL,
    utility_service_base_url: process.env.UTILITY_SERVICE_BASE_URL,
    coupon_service_base_url: process.env.DK_COUPON_SERVICE_BASE_URL,
    coupon_service_api_key: process.env.DK_COUPON_SERVICE_API_KEY,
  },
  payment: {
    create_order_url: process.env.PAYMENT_CREATE_ORDER_URL,
    x_api_key: process.env.PAYMENT_X_API_KEY,
    failure_wait_time: process.env.FAILURE_WAIT_TIME,
  },
  notification: {
    mailer_url: process.env.NOTIFICATION_MAILER_URL,
    mailer_username: process.env.NOTIFICATION_MAILER_USERNAME,
    mailer_x_api_key: process.env.NOTIFICATION_MAILER_X_API_KEY,
    bcc_mail_id: process.env.BCC_EMAIL_ID,
    whatsapp_url: process.env.WHATSAPP_NOTIFICATION_URL,
  },
  erp: {
    order_status_webhook_url: process.env.ERP_ORDER_STATUS_WEBHOOK_URL,
    x_api_key: process.env.ERP_X_API_KEY,
  },
  razorpay: {
    api_key: process.env.RAZORPAY_API_KEY,
    api_secret: process.env.RAZORPAY_API_SECRET,
    methods_api_url: `https://api.razorpay.com/v1/methods`,
  },
  RMQ: {
    user: process.env.CLOUD_RMQ_USERNAME,
    password: process.env.CLOUD_RMQ_PASSWORD,
    host: process.env.CLOUD_RMQ_HOST,
  },
  easy_insights_url: process.env.EASY_INSIGHT_URL,
};
