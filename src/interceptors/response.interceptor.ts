import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CartMapper } from 'src/mapper/cart.mapper';
import { CartResponse } from '../interface/cart-rest-api-response';

@Injectable()
export class ResponseMapperInterceptor implements NestInterceptor {
  constructor(private readonly cartMapper: CartMapper) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data: CartResponse | { cart: CartResponse }) => {
        // Apply the mapper function to the response data
        return this.cartMapper.buildRestCartResponse(data);
      }),
    );
  }
}
