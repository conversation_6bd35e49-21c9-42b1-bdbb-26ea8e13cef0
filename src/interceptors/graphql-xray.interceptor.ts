// graphql-xray.interceptor.ts
import {
  Injectable,
  ExecutionContext,
  CallHandler,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import * as AWSXRay from 'aws-xray-sdk';
import { GqlExecutionContext } from '@nestjs/graphql';

@Injectable()
export class GraphQLXRayInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = GqlExecutionContext.create(context);
    // const request = ctx.getRequest();

    const operationName = ctx.getHandler().name;

    const segment = AWSXRay.getSegment().addNewSubsegment(
      `GraphQL: ${operationName}`,
    );
    segment.addAnnotation('OperationName', operationName);
    return next.handle().pipe(
      tap(() => {
        segment.close();
      }),
      catchError((error) => {
        segment.addErrorFlag();
        segment.addMetadata('error', error);
        segment.addError(error);
        segment.close();
        throw error; // Ensure the error is rethrown so the client gets the error response
      }),
    );
  }
}
