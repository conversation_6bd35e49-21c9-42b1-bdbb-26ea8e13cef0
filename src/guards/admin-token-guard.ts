import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import config from '../config/env';

@Injectable()
export class InactiveCartGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<any> {
    const request = context.switchToHttp().getRequest();
    const inactive_cart_token =
      request.headers?.['authorization']?.split(' ')[1];
    if (inactive_cart_token === config.dk.inactive_cart_token) return true;
    return false;
  }
}
