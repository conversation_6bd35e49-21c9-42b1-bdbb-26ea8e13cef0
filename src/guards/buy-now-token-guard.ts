import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import config from '../config/env';

@Injectable()
export class BuyNowInactiveCartGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<any> {
    const request = context.switchToHttp().getRequest();
    const buy_now_cart_token =
      request.headers?.['authorization']?.split(' ')[1];
    if (buy_now_cart_token === config.dk.buy_now_cart_token) return true;
    return false;
  }
}
