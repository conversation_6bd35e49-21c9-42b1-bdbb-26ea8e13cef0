import { HttpService } from '@nestjs/axios';
import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import env from 'src/config/env';

@Injectable()
export class AdminValidationGuard implements CanActivate {
  constructor(private httpService: HttpService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const token = request?.headers?.authorization?.split(' ')[1];

    if (!token) throw new UnauthorizedException('No token Found');

    const response = await this.httpService
      .post(env.admin_validation_api, {
        token,
      })
      .toPromise();

    if (!response || !response.data) {
      throw new UnauthorizedException('Admin user not found');
    }

    return true;
  }
}
