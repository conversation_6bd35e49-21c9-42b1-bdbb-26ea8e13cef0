import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import config from '../config/env';

@Injectable()
export class WebhookApiGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<any> {
    const request = context.switchToHttp().getRequest();
    const x_api_key = request.headers['x-api-key'];
    if (x_api_key === config.dk.db_webhook_secret) return true;
    return false;
  }
}
