import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { AddressTypes, CartAction } from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { CartMapper } from 'src/mapper/cart.mapper';
import { ExternalApiHelper } from './external-api.helper';
import { logger } from './service-logger';
import * as _ from 'lodash';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { Sequelize } from 'sequelize-typescript';
import { DiscountType } from '../config/constants';
import { CartHelperFunctions } from './cart-service-helper';
import { CartUtilityFunctions } from './cart-utility-function';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { applyRewardPoints } from '../interface/applyRewardPointRequest';
import { OutputResponseType } from 'src/config/constants';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';

@Injectable()
export class RewardService {
  constructor(
    private readonly cartMapper: CartMapper,
    private readonly cartHelperFunctions: CartHelperFunctions,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly cartUtilityFun: CartUtilityFunctions,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  /**
   * It applies requested reward points to the user's
   * active cart
   * @param rewardPoints
   * @param customerId
   * @returns
   */
  async applyRewardPoints(
    request: applyRewardPoints,
    customerId: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    const { rewardpoints, is_buy_now_cart, cart_id } = request;

    const quoteFilters = cart_id
      ? {
          customer_id: customerId,
          masked_id: cart_id,
          is_active: is_buy_now_cart ? false : true,
        }
      : { customer_id: customerId };

    const cartExists = await Quote.findOne({
      where: { ...quoteFilters },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });

    if (!cartExists) {
      throw new BadRequestException('Cart not found');
    }

    if (cartExists.items.length === 0) {
      throw new BadRequestException('Reward can not apply on empty cart');
    }

    try {
      const skuWiseErrors = {},
        parentMappedPrice = {};

      const shippingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const skus = cartExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartExists.items,
        [],
        'apply_reward',
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      cartExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          shippingAddress?.customer_country_id,
        );
      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: cartExists.items,
          quote: cartExists,
          billingAddress,
          shippingAddress,
          // t1,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartExists),
          customerId,
          quote_filter: is_buy_now_cart ? { is_active: false } : null,
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction:
            rewardpoints <= 0
              ? CartAction.REMOVE_REWARD
              : CartAction.APPLY_REWARD,
          appliedPoints: rewardpoints,
          itemPromotions,
          productTaxes,
        });

      const updatedQuote = is_buy_now_cart
        ? await Quote.findOne({
            where: {
              quote_id: cartExists.quote_id,
              is_active: false,
            },
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          })
        : await Quote.findByPk(cartExists.quote_id, {
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        productDetailsClone ?? [],
        // productDetails ?? [],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
      );
    } catch (error) {
      logger.error('Error in applyRewardPoints', error);
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   *It will compute customer max-applied points at checkout based on subtotal and config details
   * @param customerId registered user's customer-id
   * @returns
   */
  async applicableRewardPoints(
    customerId: number,
    request?: {
      cart_id: string;
      is_buy_now_cart: boolean;
    },
  ) {
    let cartExists = null;
    if (request?.cart_id) {
      const { cart_id, is_buy_now_cart } = request;
      cartExists = await Quote.findOne({
        where: {
          customer_id: +customerId,
          masked_id: cart_id,
          is_active: is_buy_now_cart ? false : true,
        },
        attributes: ['customer_id', 'subtotal'],
      });
    } else {
      cartExists = await Quote.findOne({
        where: { customer_id: customerId },
        attributes: ['customer_id', 'subtotal'],
      });
    }
    if (!cartExists) {
      throw new BadRequestException('Cart not found');
    }
    if (cartExists.subtotal) {
      try {
        const rewardAndMembershipInfo =
          await this.externalApiHelper.getRewardAndMembershipInfo(
            customerId,
            cartExists.subtotal,
          );
        return {
          max_applied_points: rewardAndMembershipInfo.max_applicable_points,
        };
      } catch (e) {
        this.cartUtilityFun.throwError(e);
      }
    }
    return { max_applied_points: null };
  }
}
