import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  CancellationAvailableStatuses,
  ERPItemStatuses,
  OrderStatuses,
  PaymentMethods,
  ReturnAvailableStatuses,
} from 'src/config/constants';
import config from 'src/config/env';
import { SalesOrder } from 'src/database/entities/sales-order';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import { logger } from './service-logger';
import { SalesOrderAddress } from 'src/database/entities/sales-order-address';
import { SalesOrderPayment } from 'src/database/entities/sales-order-payment';
import {
  REWARD_POINT_RATE_MULTIPLIER,
  ORDER_PUBLISH_SOURCE,
} from '../config/constants';
import { NotificationHelper } from './notification.helper';
import { SalesOrderExtraInfo } from '../database/entities/sales-order-extra-info';
import { OrderHelper } from './order.helper';
import {
  OrderEventAction,
  whatsappNotificationData,
} from 'src/interface/whatsapp-notification-data';
import { SalesOrderAmountPromotion } from 'src/database/entities/sales-order-amount-promotion';
import { SalesOrderItemPromotion } from 'src/database/entities/sales-order-item-promotion';

@Injectable()
export class NotifyERP {
  constructor(
    private readonly httpService: HttpService,
    private readonly notificationHelper: NotificationHelper,
    private readonly orderHelper: OrderHelper,
  ) {}

  /**
   * Call ERP's API to notify about order status update
   * @param order SalesOrder
   * @param orderItems: SalesOrderItem[],
   * @param orderAddresses: SalesOrderAddress[],
   * @param orderPayment: SalesOrderPayment,
   */
  async notifyStatusUpdate(
    order: SalesOrder,
    orderItems: SalesOrderItem[],
    orderAddresses: SalesOrderAddress[],
    orderPayment: SalesOrderPayment,
    status?: string,
    sales_order_extra_info?: SalesOrderExtraInfo,
    is_mail_noftfication = true,
    isFirstOrder?: boolean,
  ) {
    try {
      if (order.status === OrderStatuses.CANCELLED) {
        //decrement coupon usage updates
        this.orderHelper.updateRuleUsage(order);
      }

      if (
        (order.status === OrderStatuses.NEW_ORDER ||
          order.status === OrderStatuses.PAYMENT_RECEIVED ||
          order.status === OrderStatuses.CANCELLED ||
          status === OrderStatuses.FAILED) &&
        is_mail_noftfication
      ) {
        //send place order mail
        this.notificationHelper.sendOrderPlacedMail(
          orderPayment?.method,
          order,
          orderItems,
          orderAddresses,
        );
      }

      //Recompute Edd on razorpay status change
      if (order.status === OrderStatuses.PAYMENT_RECEIVED) {
        try {
          const productForService = await this.orderHelper.getProductDataByIds(
            order.items.map((item) => +item.product_id),
          );

          // Fetch EDD information
          const { max_delivery_days, max_dispatch_days } =
            (await this.orderHelper.getEDDInfo(
              order,
              orderItems,
              orderAddresses,
              productForService,
            )) || {};

          const orderExtraInfo =
            await this.orderHelper.addOrUpdateOrderExtraInfo(
              new Date(),
              order.order_id,
              max_delivery_days,
              max_dispatch_days,
              sales_order_extra_info,
            );

          if (orderExtraInfo) {
            sales_order_extra_info = orderExtraInfo;
          }
        } catch (e) {
          console.log(e, `Error in Recompute Edd on razorpay status change`);
        }
      }

      order.items = orderItems;
      order.address = orderAddresses;
      order.payment = orderPayment;
      order.sales_order_extra_info = sales_order_extra_info;
      const url = config.erp.order_status_webhook_url;
      const headers = { authorization: config.dk.publish_order_url_api_key };
      const data = this.buildWebhookPayload(order);

      //send WatssApp Notification
      this.sendWatsAppNotification(order);

      //first order key populate
      if (typeof isFirstOrder !== 'undefined') {
        data['isFirstOrder'] = isFirstOrder;
      }

      logger.info('notify ERP request', {
        url,
        headers,
        data: JSON.stringify(data),
      });

      const response = await firstValueFrom(
        this.httpService.post(
          config.dk.publish_order_url,
          JSON.stringify({
            Source: ORDER_PUBLISH_SOURCE,
            Detail: data,
            DetailType: 'Order Event push',
          }),
          { headers },
        ),
      );

      // const response = await firstValueFrom(
      //   this.httpService.post(url, data, { headers }),
      // );
      logger.info('notify ERP response', response?.data);
    } catch (error) {
      logger.error('error in notify erp', error);
    }
  }

  /**
   * Build order & item data required to be sent to ERP
   * @param order SalesOrder
   * @returns
   */
  buildWebhookPayload(order: SalesOrder) {
    const billingAddress = order.address.find(
      (o) => o.address_type === 'billing',
    );
    const shippingAddress = order.address.find(
      (o) => o.address_type === 'shipping',
    );
    const items = this.buildItemsObject(order, order.payment.method);
    const processedAtDate = order?.sales_order_extra_info?.processed_at ?? null;
    const isMembershipOrder = order.is_active_membership;
    return {
      entity_id: order.order_id,
      is_active_membership: isMembershipOrder,
      order_id: order.increment_id,
      order_status: order.status,
      order_date: this.getOrderProcessingDate(order.createdAt, processedAtDate),
      customer_id: order.customer_id || null,
      customer_email: order.customer_email,
      currency: order?.order_currency_symbol ?? null,
      payment_method_code: order.payment.method,
      rzp_order_id: order.payment?.razorpay_order_id ?? null,
      rzp_payment_id: order.payment?.razorpay_payment_id ?? null,
      platform: order.platform || null,
      app_version: order.app_version || null,
      source: 'order-service',
      billing_address: {
        name: billingAddress.lastname
          ? [billingAddress.firstname, billingAddress.lastname].join(' ')
          : billingAddress.firstname,
        company: billingAddress.company,
        street: billingAddress.street,
        city: billingAddress.city,
        postcode: billingAddress.postcode,
        region: billingAddress.region,
        region_id: billingAddress.region_id,
        vat_id: billingAddress.gst_id,
        telephone: billingAddress.telephone,
        country_id: billingAddress.country_id,
        latitude: billingAddress?.latitude,
        longitude: billingAddress?.longitude,
        tag: billingAddress?.tag,
        map_address: billingAddress?.map_address,
        customer_street_2: billingAddress?.customer_street_2,
      },
      shipping_address: shippingAddress
        ? {
            name: shippingAddress.lastname
              ? [shippingAddress.firstname, shippingAddress.lastname].join(' ')
              : shippingAddress.firstname,
            company: shippingAddress.company,
            street: shippingAddress.street,
            city: shippingAddress.city,
            postcode: shippingAddress.postcode,
            region: shippingAddress.region,
            region_id: shippingAddress.region_id,
            vat_id: shippingAddress.gst_id,
            telephone: shippingAddress.telephone,
            country_id: shippingAddress.country_id,
            latitude: shippingAddress?.latitude,
            longitude: shippingAddress?.longitude,
            tag: shippingAddress?.tag,
            map_address: shippingAddress?.map_address,
            customer_street_2: shippingAddress?.customer_street_2,
          }
        : null,
      can_return:
        ReturnAvailableStatuses.indexOf(order.status) !== -1 ? true : false,
      can_cancel:
        CancellationAvailableStatuses.indexOf(order.status) !== -1
          ? true
          : false,
      order_summary: [
        {
          label: 'Subtotal',
          value: order.subtotal_incl_tax,
          code: 'subtotal',
        },
        {
          label: 'Grand Total',
          value: order.grand_total,
          code: 'grand_total',
        },
        {
          label: 'Shipping Charge',
          value: order?.shipping_amount ?? null,
          code: 'shipping',
          area: null,
        },
        {
          label: order.coupon_code
            ? `Discount${order.coupon_code}`
            : `Discount`,
          value: order.discount_amount ? +order.discount_amount : 0,
          code: 'discount',
          area: null,
        },
        {
          code: 'handling_fee',
          label: 'Overweight delivery charges',
          value: order.handling_fee ?? 0,
        },
        {
          code: 'reward_discount',
          label: 'Reward Discount',
          value: +order.rewards_discount || 0,
        },
      ],
      items,
      dk_rewards: {
        reward_point_used: order.rewards_discount
          ? +(
              order.rewards_discount *
              (isMembershipOrder ? 1 : REWARD_POINT_RATE_MULTIPLIER)
            ).toFixed(0)
          : 0,
        reward_point_earned: this.calculateRewardPoints(order.items),
        reward_discount: order.rewards_discount ? +order.rewards_discount : 0,
      },
      shipping_discount: {
        base_shipping_discount_amount: order.base_shipping_discount_amount || 0,
        shipping_discount_amount: order.shipping_discount_amount || 0,
      },
      extension_attributes: order?.sales_order_extra_info?.registration_no
        ? [
            {
              attribute_code: 'registration_no',
              attribute_value: order.sales_order_extra_info.registration_no,
            },
          ]
        : [],
      applied_rule_ids: order?.applied_rule_ids ?? '',
      exp_delivery_days:
        order?.sales_order_extra_info?.exp_delivery_days ?? null,
      exp_dispatch_days:
        order?.sales_order_extra_info?.exp_dispatch_days ?? null,
      amount_promotions:
        order?.salesOrderAmountPromotions?.map((p) => ({
          free_sku: p.sku,
          free_qty: p.qty,
          promotion_id: p.promotion_id,
          price: p.meta_info?.price,
        })) ?? [],
    };
  }

  /**
   * Build all items present in an order such that it
   * can be sent to ERP
   * @param order Sales Order
   * @param paymentMethod order's payment method
   * @returns
   */
  buildItemsObject(order: SalesOrder, paymentMethod: string) {
    const items = [];
    for (const obj of order.items) {
      items.push({
        order_item_id: obj.order_item_id,
        status: this.calculateERPItemStatus(order, paymentMethod),
        product_id: obj.product_id,
        sku: obj.product_sku,
        name: obj.product_name,
        type: obj.product_type,
        url_key: obj.url_key,
        options: obj.product_options,
        qty_ordered: obj.qty_ordered,
        qty_invoiced: obj.qty_invoiced,
        qty_shipped: obj.qty_shipped,
        qty_canceled: obj.qty_canceled,
        qty_refunded: obj.qty_refunded,
        qty_backorded: obj.qty_backordered,
        weight: obj.weight,
        price: obj.price,
        price_incl_tax: obj.price_incl_tax,
        tax_percent: obj?.tax_percent ?? 0,
        tax_amount: obj?.tax_amount ?? 0,
        discount_amount: obj.discount_amount,
        amount_refunded: obj.amount_refunded,
        row_total: obj.row_total,
        row_total_incl_tax: obj.row_total_incl_tax,
        image: obj.thumbnail_url,
        rewardpoints: obj.reward_points,
        expiry_date: obj.product_expiry_date,
        item_handling_fee: obj.item_handling_fee,
        referral_code: obj?.itemExtraInfo?.referral_code ?? null,
        is_free_product: obj?.itemExtraInfo?.is_free_product ?? null,
        parent_id: obj?.parent_item_id ?? null,
        item_promotions:
          obj.salesOrderItemPromotions?.map((p) => ({
            free_sku: p.sku,
            free_qty: p.qty,
            promotion_id: p.promotion_id,
            price: p.meta_info?.price,
          })) ?? [],
      });

      if (obj.salesOrderItemPromotions?.length) {
        for (const promo of obj.salesOrderItemPromotions) {
          items.push({ ...this.mapPromoSkuItem(promo, order, paymentMethod) });
        }
      }
    }

    for (const promo of order.salesOrderAmountPromotions ?? []) {
      items.push({ ...this.mapPromoSkuItem(promo, order, paymentMethod) });
    }

    return items;
  }

  /**
   * It computes the item status based on payment-method
   * and order status
   * @param order Sales Order
   * @param paymentMethod order's payment method
   * @returns
   */
  calculateERPItemStatus(order: SalesOrder, paymentMethod: string) {
    if (paymentMethod === PaymentMethods.COD) {
      if (
        [
          OrderStatuses.AUTO_INVOICED,
          OrderStatuses.COMPLETE,
          OrderStatuses.DELIVERED,
          OrderStatuses.RETURNED,
        ].indexOf(order.status) !== -1
      )
        return ERPItemStatuses.INVOICED;
      else return ERPItemStatuses.PENDING;
    }
    if (paymentMethod === PaymentMethods.RAZOR_PAY) {
      if (
        [
          OrderStatuses.NOT_PAID,
          OrderStatuses.PAYMENT_AUTHORIZE,
          OrderStatuses.PAYMENT_PENDING,
        ].indexOf(order.status) !== -1
      )
        return ERPItemStatuses.PENDING;
      else return ERPItemStatuses.INVOICED;
    }
  }

  //fetch order processing date give priority to processindDate
  private getOrderProcessingDate(orderDate: Date, processindDate: Date) {
    const dateToUse = processindDate || orderDate;
    return new Date(dateToUse)
      .toLocaleString('en-GB', {
        timeZone: 'IST',
        dateStyle: 'long',
        timeStyle: 'medium',
      })
      .replace(' at', '');
  }

  //send whatsapp notification
  private async sendWatsAppNotification(order: SalesOrder) {
    try {
      const shippingAddress = order.address?.find(
        (o) => o.address_type === 'shipping',
      );

      const billingAddress = order.address?.find(
        (o) => o.address_type === 'billing',
      );

      const customerMobile =
        shippingAddress?.telephone || billingAddress?.telephone;

      if (!customerMobile) return;

      //process only for new order and payment received
      if (
        ![OrderStatuses.NEW_ORDER, OrderStatuses.PAYMENT_RECEIVED].includes(
          order.status,
        )
      ) {
        return;
      }

      let expectedDeliveryDate: string = null;

      if (order.sales_order_extra_info) {
        expectedDeliveryDate = this.orderHelper.getExpectedDeliveryDate(
          order.sales_order_extra_info.processed_at || order.createdAt,
          order.sales_order_extra_info.exp_delivery_days,
        );
      }

      const whatsappData: whatsappNotificationData = {
        distinct_id: customerMobile,
        phone: customerMobile,
        event: OrderEventAction.PLACE_ORDER,
        data: {
          order_type:
            order.status === OrderStatuses.NEW_ORDER ? 'COD' : 'PREPAID',
          order_id: order.increment_id,
          amount: order.grand_total,
          expected_delivery_date: expectedDeliveryDate ?? '',
          tracking_url: `https://www.dentalkart.com/track-page/${order.increment_id}`,
        },
      };

      await this.notificationHelper.sendWhatsAppNotification(whatsappData);
    } catch (e) {
      console.log(e, `Error in sendWatsAppNotification`);
    }
  }

  private calculateRewardPoints(items: SalesOrderItem[]): number {
    try {
      const total = items.reduce(
        (total, item) =>
          total +
          (item.reward_points != null && !isNaN(item.reward_points)
            ? item.reward_points
            : 0) *
            item.qty_ordered,
        0,
      );
      return Math.floor(total);
    } catch (error) {
      return 0; // Return 0 or any default value in case of error
    }
  }

  mapPromoSkuItem(
    promo: SalesOrderItemPromotion | SalesOrderAmountPromotion,
    order: SalesOrder,
    paymentMethod: string,
  ) {
    return {
      rewardpoints: 0,
      is_free_product: true,
      status: this.calculateERPItemStatus(order, paymentMethod),
      product_id: promo.meta_info?.product_id,
      sku: promo.sku,
      name: promo.meta_info?.name,
      type: promo.meta_info?.type_id,
      url_key: promo.meta_info?.url_key,
      qty_ordered: promo.qty,
      price: promo.meta_info?.price ?? 0,
      price_incl_tax: promo.meta_info?.price ?? 0,
      discount_amount: (promo.meta_info?.price ?? 0) * promo.qty,
      row_total: (promo.meta_info?.price ?? 0) * promo.qty,
      row_total_incl_tax: (promo.meta_info?.price ?? 0) * promo.qty,
      expiry_date: promo.meta_info?.product_expiry_date,
      item_handling_fee: 0,
      weight: promo.meta_info?.weight ?? 0,
      image: promo.meta_info?.thumbnail_url ?? null,
      tax_percent: promo.meta_info?.tax_percent ?? 0,
      tax_amount: promo.meta_info?.tax_amount ?? 0,
      order_item_id: null,
      options: null,
      qty_invoiced: 0,
      qty_shipped: 0,
      qty_canceled: 0,
      qty_refunded: 0,
      qty_backorded: 0,
      amount_refunded: 0,
      referral_code: null,
      parent_id: null,
    };
  }
}
