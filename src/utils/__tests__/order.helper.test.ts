import { Test, TestingModule } from '@nestjs/testing';
import { OrderHelper } from '../order.helper';
import { SalesOrderExtraInfo } from '../../database/entities/sales-order-extra-info';

// Mock the SalesOrderExtraInfo model
jest.mock('../../database/entities/sales-order-extra-info');

describe('OrderHelper - addOrUpdateOrderExtraInfo', () => {
  let orderHelper: OrderHelper;
  let mockSalesOrderExtraInfo: jest.Mocked<typeof SalesOrderExtraInfo>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OrderHelper],
    }).compile();

    orderHelper = module.get<OrderHelper>(OrderHelper);
    mockSalesOrderExtraInfo = SalesOrderExtraInfo as jest.Mocked<typeof SalesOrderExtraInfo>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('addOrUpdateOrderExtraInfo', () => {
    it('should create new order extra info with warehouse code', async () => {
      const mockExtraInfo = {
        extra_info_entity_id: 1,
        order_id: 123,
        exp_delivery_days: 5,
        exp_dispatch_days: 2,
        max_delivery_warehouse_code: 'WH001',
        processed_at: new Date(),
      };

      mockSalesOrderExtraInfo.create = jest.fn().mockResolvedValue(mockExtraInfo);

      const result = await orderHelper.addOrUpdateOrderExtraInfo(
        new Date(),
        123,
        5,
        2,
        null, // no existing extra info
        'WH001'
      );

      expect(mockSalesOrderExtraInfo.create).toHaveBeenCalledWith({
        order_id: 123,
        processed_at: expect.any(Date),
        exp_delivery_days: 5,
        exp_dispatch_days: 2,
        max_delivery_warehouse_code: 'WH001',
      });
      expect(result).toEqual(mockExtraInfo);
    });

    it('should update existing order extra info with warehouse code', async () => {
      const existingExtraInfo = {
        extra_info_entity_id: 1,
        order_id: 123,
        exp_delivery_days: 3,
        exp_dispatch_days: 1,
        max_delivery_warehouse_code: 'WH002',
        processed_at: new Date('2023-01-01'),
      } as SalesOrderExtraInfo;

      mockSalesOrderExtraInfo.update = jest.fn().mockResolvedValue([1]);

      const result = await orderHelper.addOrUpdateOrderExtraInfo(
        new Date(),
        123,
        5,
        2,
        existingExtraInfo,
        'WH001'
      );

      expect(mockSalesOrderExtraInfo.update).toHaveBeenCalledWith(
        {
          processed_at: expect.any(Date),
          exp_delivery_days: 5,
          exp_dispatch_days: 2,
          max_delivery_warehouse_code: 'WH001',
        },
        {
          where: {
            extra_info_entity_id: 1,
          },
        }
      );
      expect(result.max_delivery_warehouse_code).toBe('WH001');
    });

    it('should handle missing warehouse code gracefully', async () => {
      const mockExtraInfo = {
        extra_info_entity_id: 1,
        order_id: 123,
        exp_delivery_days: 5,
        exp_dispatch_days: 2,
        processed_at: new Date(),
      };

      mockSalesOrderExtraInfo.create = jest.fn().mockResolvedValue(mockExtraInfo);

      const result = await orderHelper.addOrUpdateOrderExtraInfo(
        new Date(),
        123,
        5,
        2,
        null, // no existing extra info
        undefined // no warehouse code
      );

      expect(mockSalesOrderExtraInfo.create).toHaveBeenCalledWith({
        order_id: 123,
        processed_at: expect.any(Date),
        exp_delivery_days: 5,
        exp_dispatch_days: 2,
        // max_delivery_warehouse_code should not be included
      });
      expect(result).toEqual(mockExtraInfo);
    });

    it('should return null on error', async () => {
      mockSalesOrderExtraInfo.create = jest.fn().mockRejectedValue(new Error('Database error'));

      const result = await orderHelper.addOrUpdateOrderExtraInfo(
        new Date(),
        123,
        5,
        2,
        null,
        'WH001'
      );

      expect(result).toBeNull();
    });
  });
});
