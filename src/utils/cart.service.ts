import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { AddressTypes, CartAction, ProductTypes } from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import {
  AddProductToCartRequest,
  updateCartItemsRqequest,
} from 'src/interface/add-product-to-cart-request';
import { CustomerDetails } from 'src/interface/customer';
import { CartMapper } from 'src/mapper/cart.mapper';
import { ExternalApiHelper } from './external-api.helper';
import { MaskHelper } from './mask.helper';
import { logger } from './service-logger';
import * as _ from 'lodash';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { QuoteShippingRate } from 'src/database/entities/quote_shipping_rate';
import { Sequelize } from 'sequelize-typescript';
import { RemoveItemFromCartRequest } from 'src/interface/remove-item-from-cart-request';
import { MergeCartsRequest } from 'src/interface/merge-carts-request';
import { CartHelperFunctions } from './cart-service-helper';
import { ProductStatuses } from 'src/config/constants';
import { CartUtilityFunctions } from './cart-utility-function';
import { GlobalCurrencyConfiguration } from 'src/interface/graphql-response';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { OutputResponseType, MEMBERSHIP_SKUS } from 'src/config/constants';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
//
@Injectable()
export class CartService {
  constructor(
    private readonly cartMapper: CartMapper,
    private readonly maskHelper: MaskHelper,
    private readonly cartHelperFunctions: CartHelperFunctions,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly cartUtilityFun: CartUtilityFunctions,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  /**
   * Validate authorization header token
   * @param authHeader string
   * @returns
   */
  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }

  /**
   * It builds cart data & stores the same in database
   * @param remoteIp
   * @param cartId
   * @returns
   */
  async createGuestCart(remoteIp: string, cartId?: string) {
    let masked_id;
    if (cartId) {
      const cartIDExists = await Quote.findOne({
        where: { masked_id: cartId },
      });
      if (cartIDExists) {
        throw new UnprocessableEntityException(
          `Cart with ID '${cartId}' already exists`,
        );
      }
      masked_id = cartId;
    } else {
      masked_id = await this.maskHelper.generateQuoteMaskedId();
    }
    const cartObj = this.cartMapper.mapGuestCart(
      remoteIp,
      {} as GlobalCurrencyConfiguration,
    );
    cartObj['masked_id'] = masked_id;

    const newCart = await Quote.create(cartObj, {});

    return newCart.masked_id;
  }

  /**
   * It builds cart data & stores the same in database
   * @param remoteIp
   * @param cartId
   * @param customerDetails
   * @param customerTaxClassId
   * @returns
   */
  async createCart(
    remoteIp: string,
    customerDetails: CustomerDetails,
    customerTaxClassId: string,
    cartId?: string,
  ) {
    let masked_id;
    if (cartId) {
      const cartIDExists = await Quote.findOne({
        where: { masked_id: cartId },
      });
      if (cartIDExists) {
        throw new UnprocessableEntityException(
          `Cart with ID '${cartId}' already exists`,
        );
      }
      masked_id = cartId;
    } else {
      masked_id = await this.maskHelper.generateQuoteMaskedId();
    }

    const cartObj = this.cartMapper.mapUserCart(
      remoteIp,
      {} as GlobalCurrencyConfiguration,
      customerDetails,
      customerTaxClassId,
    );

    cartObj['masked_id'] = masked_id;
    const newCart = await Quote.create(cartObj);
    return newCart.masked_id;
  }

  /**
   * It validates the requested products availability,
   * also updates the quantity of existing item as requested,
   * and adds new items with requested quantity to the cart
   * @param request AddProductToCartRequest
   * @param customerId registered user's customer-id
   * @returns
   */
  async addProductsToCart(
    request: AddProductToCartRequest,
    customerId?: number,
    customerGroupId?: number,
    outputResponseType?: OutputResponseType,
    is_buying_guide?: boolean,
  ) {
    try {
      const quoteExists = await Quote.findOne({
        where: { masked_id: request.cart_id, customer_id: customerId || null },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
            ],
          },
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!quoteExists) {
        // logger.info({
        //   message: 'Request cart not found',
        //   cartId: request.cart_id,
        //   request: JSON.stringify(request),
        //   customerId: customerId,
        // });
        throw new BadRequestException('Cart not found');
      }

      // logger.info({
      //   message: 'Request cart info',
      //   customerId: customerId,
      //   cartId: quoteExists.masked_id,
      // });

      const shippingAddress = quoteExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = quoteExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const skuWiseErrors = {},
        parentMappedPrice = {};
      const inputSkus = Object.values(request.cart_items).map(
        (o) => o['data'].sku,
      );

      let hasDuplicateMembershipSku = false;
      const productSkus = [...new Set([...inputSkus])];
      const requestedQtyAndSku = Object.values(request.cart_items).map(
        (o: any) => {
          if (
            o['data'].sku === MEMBERSHIP_SKUS[0] ||
            o['data'].sku === MEMBERSHIP_SKUS[1]
          ) {
            hasDuplicateMembershipSku = quoteExists.items.some(
              (item) => item.sku === o['data'].sku,
            );
          }

          const newItem = {
            sku: o['data'].sku,
            quantity: o['data'].quantity,
            parent_id: o['data']?.parent_id ?? null,
            referral_code: o['data']?.referral_code ?? null,
            buying_guide_qty: is_buying_guide ? o['data'].quantity : null,
          };

          return newItem;
        },
      );

      const cartSkuSet = new Set([
        ...productSkus,
        ...quoteExists?.items?.map((o) => o.sku),
      ]);

      if (hasDuplicateMembershipSku) {
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['already_exist_plan'].message,
          VALIDATION_ERRORS['already_exist_plan'].errorCode,
        );
      }

      if (
        cartSkuSet.has(MEMBERSHIP_SKUS[0]) &&
        cartSkuSet.has(MEMBERSHIP_SKUS[1])
      ) {
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['plan_limit_exceeded'].message,
          VALIDATION_ERRORS['plan_limit_exceeded'].errorCode,
        );
      }

      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...productSkus, ...quoteExists?.items?.map((o) => o.sku)],
          country_id,
          region_id,
        );

      if (!productDetails?.length) {
        // logger.info({
        //   message: 'Product details not found',
        //   cartId: quoteExists.masked_id,
        //   requestSKus: JSON.stringify([
        //     ...productSkus,
        //     ...quoteExists?.items?.map((o) => o.sku),
        //   ]),
        // });

        throw new InternalServerErrorException('Unable to add product to cart');
      }

      // product validation type
      let productDetailsForRequestSkus = productDetails?.filter((o) => {
        if (productSkus.indexOf(o.sku) !== -1) {
          if (o.status !== ProductStatuses.ENABLE) {
            throw new CustomBadRequestException(
              VALIDATION_ERRORS['disabled_product'].message(o.name),
              VALIDATION_ERRORS['disabled_product'].errorCode,
            );
          }
          if (
            [ProductTypes.SIMPLE, ProductTypes.VIRTUAL].indexOf(o.type_id) ===
            -1
          )
            throw new CustomBadRequestException(
              VALIDATION_ERRORS['wrong_type_product'].message,
              VALIDATION_ERRORS['wrong_type_product'].errorCode,
            );
          return o;
        }
      });

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        quoteExists.items,
        requestedQtyAndSku,
        CartAction.ADD_TO_CART,
      );

      this.cartUtilityFun.computeGroupChildPrice(
        productDetails,
        skuwiseParentIdQty,
        parentMappedPrice,
      );

      const itemsToModify = [];
      if (quoteExists.items.length) {
        for (const prod of quoteExists.items) {
          const index = productSkus.indexOf(prod.sku);
          if (index !== -1) {
            itemsToModify.push(prod.sku);
            productSkus.splice(index, 1);
          }
        }
      }
      // To modify quantity of existing cart item
      const insertedItems = [];
      if (itemsToModify.length) {
        const updatedItems =
          await this.cartHelperFunctions.mergeToExistingCartItemsWihoutTxn(
            itemsToModify,
            quoteExists.items,
            requestedQtyAndSku,
            productDetails ?? [],
            productTaxes ?? {},
            true,
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            shippingAddress?.customer_country_id,
            is_buying_guide,
          );
        insertedItems.push(...updatedItems);
        quoteExists.items = quoteExists.items.filter(
          (o) => !itemsToModify.some((item) => item.sku === o.sku),
        );
      }

      // To insert when product not already present
      if (productSkus.length) {
        const remaining_item_details = productDetailsForRequestSkus?.filter(
          (prod) => productSkus.indexOf(prod.sku) !== -1,
        );
        const items = await this.cartHelperFunctions.buildQuoteItemsObj(
          quoteExists.quote_id,
          requestedQtyAndSku,
          remaining_item_details,
          productTaxes ?? {},
          skuwiseParentIdQty,
          parentMappedPrice,
          country_id,
        );
        const newItems = await QuoteItem.bulkCreate(items, {
          include: [
            { model: QuoteItemExtensionAttribute, as: 'extension_attribute' },
          ],
        });
        if (newItems?.length) {
          insertedItems.push(...newItems);
          requestedQtyAndSku?.forEach(async (item) => {
            if (item?.referral_code) {
              const matchingItems = newItems.find(
                (newItem) => newItem.sku === item.sku,
              );
              await this.cartUtilityFun.updateReferralCode(
                item?.referral_code,
                matchingItems,
              );
            }
          });
        }
      }

      const allItems = _.uniqBy(
        [...quoteExists.items, ...insertedItems],
        'sku',
      );
      quoteExists.items = allItems;
      // return quoteExists;
      const cartResponse = this.cartMapper.buildCartResponse(
        quoteExists,
        productDetails ?? [],
        shippingAddress,
        [],
        [],
        skuWiseErrors,
        outputResponseType,
        null,
        billingAddress,
      );

      return cartResponse;
    } catch (error) {
      // console.log(error);
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It removes requested item from the cart
   * @param request RemoveItemFromCartRequest
   * @param customerId id of registered customer
   * @returns
   */
  async removeItemFromCart(
    request: RemoveItemFromCartRequest,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
    buyNow?: boolean,
  ) {
    const cartExists = await Quote.findOne({
      where: {
        masked_id: request.cart_id,
        customer_id: customerId || null,
        is_active: buyNow ? false : true,
      },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });
    if (!cartExists) {
      throw new BadRequestException('Cart not found');
    }
    // console.log(JSON.stringify(cartExists.items, null, 4), 'CART EXIEDVBS');
    const requestedItemIndex = cartExists.items.findIndex(
      (o) => o.quote_item_id === request.cart_item_id,
    );
    if (requestedItemIndex === -1)
      throw new BadRequestException('Requested item is not present in cart');
    const skuWiseErrors = {},
      parentMappedPrice = {};
    try {
      const updatedRequestedItemIndex = cartExists.items.findIndex(
        (o) => o.quote_item_id === request.cart_item_id,
      );
      await QuoteItem.destroy({
        where: { quote_item_id: request.cart_item_id },
      });
      await QuoteItemPromotion.destroy({
        where: { quote_item_id: request.cart_item_id },
      });

      cartExists.items.splice(updatedRequestedItemIndex, 1);

      const shippingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const skus = [...cartExists?.items?.map((o) => o.sku)];
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartExists.items,
        [],
        CartAction.REMOVE_FROM_CART,
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      //update remaining items
      if (cartExists.items.length) {
        cartExists.items =
          await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
            cartExists.items,
            productDetails ?? [],
            productTaxes ?? {},
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            country_id,
          );
      }

      // console.log(JSON.stringify(cartExists?.items, null, 4), 'JBSHUBSJ');

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: cartExists.items,
          quote: cartExists,
          billingAddress,
          shippingAddress,
          customerId,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartExists),
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.REMOVE_FROM_CART,
          outputResponseType,
          quote_filter: buyNow ? { is_active: false } : null,
          itemPromotions,
          productTaxes,
        });

      const updatedCart = buyNow
        ? await Quote.findOne({
            where: {
              quote_id: cartExists.quote_id,
              is_active: false,
            },
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          })
        : await Quote.findByPk(cartExists.quote_id, {
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          });
      // console.log(JSON.stringify(updatedCart, null, 4), 'UPDATED CART');
      return this.cartMapper.buildCartResponse(
        updatedCart,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
        billingAddress,
      );
    } catch (error) {
      // console.log(error, 'BSUHGBJSU');
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * Validate the cart-items, discounts & coupon applied
   * and returns error (if any) otherwise cart-details
   * @param customerId
   * @returns
   */
  async getCartData(
    cart_id: string,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    try {
      const skuWiseErrors = {},
        parentMappedPrice = {};
      const cart = await Quote.findOne({
        where: { masked_id: cart_id, customer_id: customerId || null },
        lock: true,
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!cart) {
        // logger.info({
        //   message: 'Request fetchCart not found',
        //   cartId: cart_id,
        //   customerId: customerId,
        // });
        throw new BadRequestException('Cart not found');
      }

      const shippingAddress = cart.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );

      const billingAddress = cart.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      cart.items = await this.cartUtilityFun.handleMultipleMemberShipAddon(
        cart.items,
      );

      const skus = cart.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      if (!productDetails?.length) {
        // logger.info({
        //   message: 'Product details not found',
        //   cartId: cart.masked_id,
        //   requestSKus: JSON.stringify([...cart?.items?.map((o) => o.sku)]),
        // });
      }

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cart.items,
        [],
        'get_cart',
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );
      if (cart.items.length && productDetails?.length) {
        cart.items =
          await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
            cart.items,
            productDetails ?? [],
            productTaxes ?? {},
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            shippingAddress?.customer_country_id,
          );
        const {
          availableShippingMethods,
          availablePaymentMethods,
          is_member_ship_active,
          min_shipping_amount,
          delivery_charges,
        } =
          await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2(
            {
              customerGroupId: customerGroupId || 0,
              allItems: cart.items,
              quote: cart,
              billingAddress,
              shippingAddress,
              customerId,
              countryId: country_id,
              regionId: region_id,
              couponCode: this.cartUtilityFun.getCustomerCouponCode(cart),
              skuwiseParentIdQty,
              parentMappedPrice,
              productDetails: productDetailsClone,
              cartAction: CartAction.GET_CART,
              outputResponseType,
              itemPromotions,
              skuWiseErrors,
              productTaxes,
            },
          );
        const updatedQuote = await Quote.findByPk(cart.quote_id, {
          include: [
            {
              model: QuoteItem,
              include: [
                {
                  model: QuoteItemExtensionAttribute,
                },
                {
                  model: QuoteItemPromotion,
                },
              ],
            },
            QuoteAmountPromotion,
            QuoteDiscount,
          ],
        });
        // logger.info({
        //   message: 'Function fetchCart completed',
        //   cartId: updatedQuote.masked_id,
        //   items: JSON.stringify(updatedQuote.items),
        //   customerId: customerId,
        //   subTotal: updatedQuote.subtotal,
        //   grandTotal: updatedQuote.grand_total,
        // });
        return this.cartMapper.buildCartResponse(
          updatedQuote,
          [...(productDetailsClone || [])],
          // [...(productDetails || [])],
          shippingAddress,
          availableShippingMethods,
          availablePaymentMethods,
          skuWiseErrors,
          outputResponseType,
          { is_member_ship_active, min_shipping_amount, delivery_charges },
          billingAddress,
        ).cart;
      }

      // logger.info({
      //   message: 'Function fetchCart completed',
      //   cartId: cart.masked_id,
      //   customerId: customerId,
      //   items: JSON.stringify(cart.items),
      //   subTotal: cart.subtotal,
      //   grandTotal: cart.grand_total,
      // });

      return this.cartMapper.buildCartResponse(
        cart,
        productDetailsClone ?? [],
        // productDetails ?? [],
        shippingAddress,
        [],
        [],
        skuWiseErrors,
        outputResponseType,
        null,
        billingAddress,
      ).cart;
    } catch (error) {
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It merges all items of guest cart into the registered user's cart
   * If item is already present in registered cart then qty of guest cart is added in it
   * @param request
   * @param customerId
   * @returns
   */
  async mergeCarts(
    request: MergeCartsRequest,
    customerId: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    const guestCart = await Quote.findOne({
      where: { masked_id: request.source_cart_id, customer_id: null },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });
    if (!guestCart) throw new BadRequestException('Guest cart not found');
    const userCart = await Quote.findOne({
      where: {
        masked_id: request.destination_cart_id,
        customer_id: customerId,
      },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });
    if (!userCart) throw new BadRequestException('User cart not found');
    try {
      const skuWiseErrors = {},
        parentMappedPrice = {};
      const shippingAddress = userCart.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = userCart.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const requestedQtyAndSku = guestCart.items?.map((o) => {
        return {
          sku: o.sku,
          quantity: +o.qty,
          parent_id: +o.parent_id,
          buying_guide_qty: o?.extension_attribute?.buying_guide_qty ?? null,
        };
      });

      const productSkus = guestCart.items.map((o) => o.sku);
      const skus = [...productSkus, ...userCart.items?.map((o) => o.sku)];

      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );
      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        userCart.items,
        requestedQtyAndSku,
        CartAction.MERGE_CART,
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      const productDetailsForRequestSkus = productDetails?.filter((o) => {
        if (productSkus.indexOf(o.sku) !== -1) return o;
      });
      const itemsToModify = [];
      if (userCart.items.length) {
        for (const prod of userCart.items) {
          const index = productSkus.indexOf(prod.sku);
          if (index !== -1) {
            itemsToModify.push(prod.sku);
            productSkus.splice(index, 1);
            const pdIndex = productDetailsForRequestSkus.findIndex(
              (o) => o.sku === prod.sku,
            );
            productDetailsForRequestSkus.splice(pdIndex, 1);
          }
        }
      }
      // To modify quantity of existing cart item
      const insertedItems = [];
      if (itemsToModify.length) {
        const updatedItems =
          await this.cartHelperFunctions.mergeToExistingCartItemsWihoutTxn(
            itemsToModify,
            userCart.items,
            requestedQtyAndSku,
            productDetails ?? [],
            productTaxes ?? {},
            false,
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            shippingAddress?.customer_country_id,
          );
        insertedItems.push(...updatedItems);
        userCart.items = userCart.items.filter(
          (o) => itemsToModify.indexOf(o.sku) === -1,
        );
      }

      // To insert when product not already present
      if (productSkus.length) {
        const items = await this.cartHelperFunctions.buildQuoteItemsObj(
          userCart.quote_id,
          requestedQtyAndSku,
          productDetailsForRequestSkus,
          productTaxes ?? {},
          skuwiseParentIdQty,
          parentMappedPrice,
          shippingAddress?.customer_country_id,
        );
        const newItems = await QuoteItem.bulkCreate(items, {
          include: [
            { model: QuoteItemExtensionAttribute, as: 'extension_attribute' },
          ],
        });
        newItems?.length && insertedItems.push(...newItems);
      }
      //update remaining items
      if (userCart.items.length) {
        userCart.items =
          await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
            userCart.items,
            productDetails ?? [],
            productTaxes ?? {},
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            shippingAddress?.customer_country_id,
          );
      }
      const allItems = _.uniqBy([...userCart.items, ...insertedItems], 'sku');
      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: allItems,
          quote: userCart,
          billingAddress,
          shippingAddress,
          customerId,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(userCart),
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.MERGE_CART,
          outputResponseType,
          itemPromotions,
          productTaxes,
        });

      // remove guest cart with items, discounts, address & shipping-rate
      await QuoteItem.destroy({
        where: { quote_id: guestCart.quote_id },
        force: true,
      });
      await QuoteDiscount.destroy({
        where: { quote_id: guestCart.quote_id },
      });
      const guestShippingAddress = guestCart.addresses.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      if (guestShippingAddress)
        await QuoteShippingRate.destroy({
          where: {
            quote_address_id: guestShippingAddress.quote_address_id,
          },
        });
      await QuoteAddress.destroy({
        where: { quote_id: guestCart.quote_id },
      });
      await guestCart.destroy({});

      const updatedQuote = await Quote.findByPk(userCart.quote_id, {
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteDiscount,
        ],
      });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
        billingAddress,
      )?.cart;
    } catch (error) {
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It validates the requested products availability,
   * also updates the quantity of existing isettem as requested,
   * and adds new items with requested quantity to the cart
   * @param request AddProductToCartRequest
   * @param customerId registered user's customer-id
   * @returns
   */
  async updateCartItems(
    request: updateCartItemsRqequest,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    isAdminRequest?: boolean,
    outputResponseType?: OutputResponseType,
    buyNow?: boolean,
  ) {
    try {
      const quoteExists = await Quote.findOne({
        where: {
          masked_id: request.cart_id,
          customer_id: customerId || null,
          is_active: buyNow ? false : true,
        },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!quoteExists) throw new NotFoundException('Cart not found');
      const requestedQtyAndIds = [];
      const itemIds = request.cart_items.map((data) => {
        requestedQtyAndIds.push({
          id: +data.cart_item_id,
          qty: +data.quantity,
        });
        return +data.cart_item_id;
      });
      if (!itemIds.length)
        throw new BadRequestException(
          'Atleast one item_id required in cart_items input array.',
        );

      const shippingAddress = quoteExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = quoteExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );
      // const skus = quoteItems.map((quoteItem) => quoteItem.sku);
      const skus = quoteExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );
      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        quoteExists.items,
        [],
        CartAction.UPDATE_CART,
        requestedQtyAndIds,
      );

      //update remaining items
      const skuWiseErrors = {},
        parentMappedPrice = {};

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      // To modify quantity of existing cart item
      const updatedItems =
        await this.cartHelperFunctions.updateCartItemsWithoutTxn(
          [...new Set([...itemIds])],
          quoteExists.items,
          [...requestedQtyAndIds],
          productDetails ?? [],
          productTaxes ?? {},
          skuwiseParentIdQty,
          parentMappedPrice,
          country_id,
          skuWiseErrors,
        );
      quoteExists.items = quoteExists.items.filter(
        (o) => [...itemIds].indexOf(o.quote_item_id) === -1,
      );
      if (quoteExists.items.length) {
        quoteExists.items =
          await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
            quoteExists.items,
            productDetails ?? [],
            productTaxes ?? {},
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            country_id,
          );
      }
      const allItems = _.uniqBy([...quoteExists.items, ...updatedItems], 'sku');

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems,
          quote: quoteExists,
          billingAddress,
          shippingAddress,
          customerId,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(quoteExists),
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.UPDATE_CART,
          outputResponseType,
          quote_filter: buyNow ? { is_active: false } : null,
          itemPromotions,
          productTaxes,
        });

      const updatedQuote = buyNow
        ? await Quote.findOne({
            where: { quote_id: quoteExists.quote_id, is_active: false },
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          })
        : await Quote.findByPk(quoteExists.quote_id, {
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          });
      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
        billingAddress,
      );
    } catch (error) {
      this.cartUtilityFun.throwError(error);
    }
  }
}
