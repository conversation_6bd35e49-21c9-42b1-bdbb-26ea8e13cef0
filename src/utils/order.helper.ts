import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { UserTypes } from 'src/config/constants';
import {
  CartPaymentInfoResponse,
  GuestCartResponse,
  productsGQLResponse,
  BuyNowCartResponse,
  AvailablePaymentMethods,
  productsForService,
} from 'src/interface/cart-external-response';
import config from '../config/env';
import { ExternalServiceCaller } from './external-service-caller';
import { customAlphabet } from 'nanoid';
import { logger } from './service-logger';
import { SalesOrder } from 'src/database/entities/sales-order';
import { OrderStatuses } from 'src/config/constants';
import * as CryptoJS from 'crypto-js';
import { AvailableShippingMethodsRequest } from 'src/interface/available-payment-methods';
import {
  DeliveryResponse,
  DeliveryRequestPayload,
  DeliveryDay,
} from 'src/interface/delivery-info-request-response';
import { SalesOrderAddress } from 'src/database/entities/sales-order-address';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import { SalesOrderExtraInfo } from 'src/database/entities/sales-order-extra-info';
import { OrderItemExtraInfo } from 'src/database/entities/order-item-extra-info';
import { Request } from 'express';
import { FireOrderEventDto } from 'src/dto/order-event.dto';
import { ValidationError as SequelizeValidationError } from 'sequelize';

@Injectable()
export class OrderHelper {
  constructor(private readonly externalServiceCaller: ExternalServiceCaller) {}

  /**
   * Validate authorization header token
   * @param authHeader string
   * @returns
   */
  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }

  /**
   * Call Dk's magento API to validate requested cart-id
   * @param cartId unique guestCartId
   * @returns
   */
  async checkCartExistence(
    userType: string,
    cartId: string,
    userToken: string,
    platform: string,
    version?: string,
  ): Promise<GuestCartResponse> {
    let headers = {};
    let url: string;
    const isWeb = this.isFetchFromNewCart(platform, version);
    if (userType === UserTypes.REGISTERED) {
      headers = { Authorization: `Bearer ${userToken}` };
      url = isWeb
        ? config.dk.user_new_cart_url + `/${cartId}`
        : config.dk.user_cart_url;
    } else {
      url = isWeb
        ? config.dk.guest_new_cart_url + `/${cartId}`
        : config.dk.guest_cart_url + `/${cartId}`;
    }
    const cartDetails = await this.externalServiceCaller.get(url, headers);
    return cartDetails;
  }

  /**
   * Call Dk's magento API to get all cart items associated
   * to requested cart
   * @param cartId unique guestCartId
   * @returns
   */
  async getGuestCartItems(cartId: string) {
    const url = config.dk.guest_cart_url + `/${cartId}/items`;
    const cartItems = await this.externalServiceCaller.get(url, {});
    return cartItems;
  }

  /**
   * Call Dk's magento API to get shipment & payment information
   * @param cartId unique guestCartId
   * @returns
   */
  async getPaymentAndShippingDetails(
    userType: string,
    cartId: string,
    userToken: string,
    platform: string,
    version?: string,
  ): Promise<CartPaymentInfoResponse> {
    let headers = {};
    let url: string;
    const isWeb = this.isFetchFromNewCart(platform, version);
    if (userType === UserTypes.REGISTERED) {
      headers = { Authorization: `Bearer ${userToken}` };
      url = isWeb
        ? config.dk.user_new_cart_url + `/${cartId}/payment-information`
        : config.dk.user_cart_url + '/payment-information';
    } else {
      url = isWeb
        ? config.dk.guest_new_cart_url + `/${cartId}/payment-information`
        : config.dk.guest_cart_url + `/${cartId}/payment-information`;
    }
    const paymentAndShippingData = await this.externalServiceCaller.get(
      url,
      headers,
    );
    return paymentAndShippingData;
  }

  /**
   * Retrieve product details from external graphQL call
   * @param skus skus to retrieve data for
   * @returns
   */
  async getProductDataFromSku(
    skus: string[],
    country_id?: string,
  ): Promise<productsGQLResponse> {
    const url = config.dk.prod_for_service_url;
    const errMsg = [];
    const query = `query Query {
      productForService(sku: ${JSON.stringify(skus)}) {
        id
        thumbnail_url
        sku
        url_key
        is_cod
        weight
        reward_point_product
		    pd_expiry_date
        error_msg
        is_saleable
        dentalkart_custom_fee
        name
        international_active
        pd_expiry_date
        price {
          maximalPrice{
            amount{
              value
            }
          }
          minimalPrice{
            amount{
              value
            }
          }
          regularPrice{
            amount{
              value
            }
          }
        }
      }
    }`;
    //const headers = { 'x-api-key': config.dk.x_api_key };
    const products = await this.externalServiceCaller.post(
      url,
      {},
      {
        query,
      },
    );

    products?.data?.productForService?.forEach((item: any) => {
      if (!item.is_saleable) {
        errMsg.push(item.error_msg);
      }
      if (country_id && country_id !== 'IN' && !item.international_active) {
        errMsg.push(`${item.name} is unavailable in your country.`);
      }
    });

    if (errMsg.length > 0) {
      logger.error('Error in productsForService gql', errMsg.join(' '));
      throw new Error(errMsg.join(' '));
    }
    return products?.data;
  }

  /**
   * Call external service to make cart inactive
   * @param cartId unique CartId
   * @returns
   */
  async inactiveCart(cartId: string, platform: string, veriosn?: string) {
    try {
      const isWeb = this.isFetchFromNewCart(platform, veriosn);
      const url = isWeb
        ? config.dk.admin_new_cart_url + `/${cartId}/inactive`
        : config.dk.admin_cart_url + `/${cartId}/inactive`;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const response = await this.externalServiceCaller.put(url, headers);
      if (!response) throw new Error();
      return response;
    } catch (error) {
      logger.error('Error in inactiveCart call', error);
      throw new Error('Invalid cart');
    }
  }

  /**
   * It fetches customer details by making a call
   * to external API
   * @param authToken user's session token
   * @returns
   */
  async getCustomerDetails(authToken: string) {
    const url = config.dk.new_customer_details_url + `?get_customer_id=true`;
    const headers = { Authorization: `Bearer ${authToken}` };
    const response = await this.externalServiceCaller.get(url, headers);
    if (!response) throw new BadRequestException('Invalid token');
    return response;
  }

  /**
   * It round input amount by two decimal digit and multiply it by base currency unit
   * @param amount grand total amount
   * @param decimalPoint decimal point upto which it shoul be rounded
   * @param currencyUnit base currency unit
   * @returns base currency amount
   */
  roundAndToBaseCurrencyAmount(
    amount: number,
    decimalPoint: number,
    currencyUnit: number,
  ) {
    return Math.trunc(+amount.toFixed(decimalPoint) * currencyUnit);
  }

  /**
   * It generates random unique id that is to be
   * stored in increment_id column
   * @returns
   */
  async generateIncrementId() {
    const nanoid = customAlphabet('0123456789', 9);
    const today = new Date();
    const month = today.getMonth() + 1;
    const year = today.getFullYear().toString().substring(2);
    const prefix = month < 10 ? `0${month}${year}` : `${month}${year}`;
    const incrementId = `${prefix}-${nanoid()}`;
    const idExists = await SalesOrder.findOne({
      where: { increment_id: incrementId },
    });
    if (idExists) this.generateIncrementId();
    else return incrementId;
  }

  async canUpdateStatus(order) {
    if (
      order.status == OrderStatuses.PAYMENT_PENDING ||
      order.status == OrderStatuses.PAYMENT_RECEIVED ||
      order.status == OrderStatuses.AUTO_INVOICED ||
      order.status == OrderStatuses.NEW_ORDER
    ) {
      return true;
    }

    return false;
  }

  /**
   * It generates random unique id encoded with date country and platform
   * stored in increment_id column
   * @returns
   */
  async generateIncrementIdV2(
    platform?: string,
    version?: string,
    country_id?: string,
  ) {
    try {
      const nanoid = customAlphabet('0123456789', 9);
      const today = new Date();
      let day: number | string = today.getDate();
      let month: number | string = today.getMonth() + 1;
      const year = today.getFullYear().toString().substring(2);
      if (day < 10) day = `0${day}`;
      if (month < 10) month = `0${month}`;
      const dateString = `${day}${month}${year}`;
      const country_code_identifier = this.getCountryCode(country_id);
      const increment_id = `${nanoid()}-${country_code_identifier}-${dateString
        .split('')
        .reverse()
        .join('')}`;
      const idExists = await SalesOrder.findOne({
        where: { increment_id: increment_id },
      });
      if (idExists) {
        this.generateIncrementIdV2(platform, version, country_id);
      }
      return increment_id;
    } catch (e) {
      logger.log('Error in increment_id generation', e);
      return this.generateIncrementId();
    }
  }

  getCountryCode(countryId?: string) {
    if (!countryId) return '00';
    return countryId.toLowerCase() == 'in' ? '00' : '01';
  }

  isFetchFromNewCart(platform: string, version?: string) {
    let app_new_cart_redirect = false;
    try {
      if (version) {
        const val = +version.split('.')[0];
        if (val > 93) app_new_cart_redirect = true;
      }
      // new cart
    } catch (err) {
      // old cart
    }
    return platform === 'web' || app_new_cart_redirect;
  }

  async fetchBuyNowCartPaymentInfo(
    cartId: string,
    userToken: string,
  ): Promise<BuyNowCartResponse> {
    const headers = { Authorization: `Bearer ${userToken}` };
    const url = config.dk.buy_now_cart_url + `/${cartId}/list`;
    const cartAndPaymentDetails = await this.externalServiceCaller.get(
      url,
      headers,
    );
    return cartAndPaymentDetails || {};
  }

  async inactiveBuyNowCart(cartId: string) {
    try {
      const url = config.dk.buy_now_cart_url + `/${cartId}/inactive`;
      const headers = {
        Authorization: `Bearer ${config.dk.buy_now_cart_token}`,
      };
      const response = await this.externalServiceCaller.put(url, headers);
      if (!response) throw new Error();
      return response;
    } catch (error) {
      console.log('Error in BuyNowIncativecCart');
      throw new Error('Invalid cart');
    }
  }

  decryptDeliveryInfo(encryptedData: string): number | null {
    try {
      const bytes = CryptoJS.AES.decrypt(
        encryptedData,
        config.dk.delivery_info_secret,
      );
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      const deliveryDays = parseInt(decrypted, 10);

      if (isNaN(deliveryDays)) {
        throw new Error('Invalid delivery days');
      }
      return deliveryDays;
    } catch (error) {
      console.log('Error in decryptDeliveryInfo');
      return null;
    }
  }

  async fetchCartPaymentInfo(
    cartId: string,
    userToken: string,
    buy_now: boolean,
  ): Promise<BuyNowCartResponse> {
    const headers = userToken ? { Authorization: `Bearer ${userToken}` } : {};
    const url =
      config.dk.cart_base_url +
      `/cart/api/v1/carts/${cartId}/payment-info?buy_now=${buy_now}`;
    const cartAndPaymentDetails = await this.externalServiceCaller.get(
      url,
      headers,
    );
    return cartAndPaymentDetails || {};
  }

  async inActiveCart(cartId: string, buy_now: boolean) {
    try {
      const url =
        config.dk.cart_base_url +
        `/cart/api/v1/carts/${cartId}/inactive?buy_now=${buy_now}`;
      const headers = {
        Authorization: `Bearer ${config.dk.admin_token}`,
      };
      const response = await this.externalServiceCaller.put(url, headers);
      if (!response) throw new Error('Error in inactive cart');
      return response;
    } catch (error) {
      console.log('Error in BuyNowIncativecCart');
      throw new Error(error?.message || 'Invalid cart');
    }
  }

  async fetchPaymentMethods(payload: AvailableShippingMethodsRequest) {
    try {
      const url =
        config.dk.utility_service_base_url +
        `/utility-py/api/v1/delivery/cart-payment-methods?country_code=${payload.country_code}&postcode=${payload.postcode}&cart_amount=${payload.cart_data.cart_amount}&cart_weight=${payload.cart_data.cart_weight}&is_cod_eligible=${payload.cart_data.is_cod_on_cart}`;
      const data = await this.externalServiceCaller.get(url, {});
      console.log(`fetchPaymentMethods: ${JSON.stringify(data)}`);
      if (!data?.response?.payment_methods) {
        return [];
      }
      return data.response.payment_methods;
    } catch (e) {
      console.log('Error in fetchPaymentMethods', e?.message || e);
      return [];
    }
  }

  /**
   * Retrieve product details from external graphQL call
   * @param ids ids to retrieve data for
   * @returns
   */
  async getProductDataByIds(ids: number[]) {
    const url = config.dk.prod_for_service_url;
    const query = `query Query {
      productForService(id: ${JSON.stringify(ids)}) {
        id
        is_cod
        weight
      }
    }`;
    const products = await this.externalServiceCaller.post(
      url,
      {},
      {
        query,
      },
    );
    return products?.data?.productForService ?? [];
  }

  getProductsWeightCodInfo(productForService: productsForService[]) {
    try {
      const productInfo = {
        isCod: true,
        totalWeight: 0,
      };
      if (productForService?.length) {
        productForService.reduce(
          (
            acc: { [key: string]: number | boolean },
            product: { [key: string]: any },
          ) => {
            acc.totalWeight += product.weight ?? 0;
            if (product.is_cod !== '1') {
              acc.isCod = false;
            }
            return acc;
          },
          productInfo,
        );

        if (productInfo.totalWeight > 0) {
          const roundedWeight = Number(productInfo.totalWeight.toFixed(2));
          productInfo.totalWeight = roundedWeight * 1000;
        }
      } else {
        productInfo.isCod = false;
      }

      return productInfo;
    } catch (e) {
      console.log('Error in getProductWeightCodInfo', e?.message || e);
      return {
        isCod: false,
        totalWeight: 0,
      };
    }
  }

  async getOrderAvailablePaymentMethods(order: SalesOrder): Promise<{
    availablePaymentMethods: AvailablePaymentMethods[];
    productForService: productsForService[];
  }> {
    const productForService = await this.getProductDataByIds(
      order.items.map((item) => +item.product_id),
    );
    const { isCod, totalWeight } =
      this.getProductsWeightCodInfo(productForService);

    const billingAddress = order.address.find(
      (o) => o.address_type === 'billing',
    );
    const shippingAddress = order.address.find(
      (o) => o.address_type === 'shipping',
    );
    const paymentPayload = {
      postcode: +shippingAddress.postcode || +billingAddress.postcode,
      country_code: shippingAddress?.country_id || billingAddress.country_id,
      cart_data: {
        is_cod_on_cart: isCod,
        cart_weight: totalWeight,
        cart_amount: order.grand_total,
      },
    };
    console.log(
      `order_id: ${order.order_id}, paymentPayload: ${JSON.stringify(
        paymentPayload,
      )}`,
    );
    const availablePaymentMethods = await this.fetchPaymentMethods({
      postcode: +shippingAddress.postcode || +billingAddress.postcode,
      country_code: shippingAddress?.country_id || billingAddress.country_id,
      cart_data: {
        is_cod_on_cart: isCod,
        cart_weight: totalWeight,
        cart_amount: order.grand_total,
      },
    });

    return {
      availablePaymentMethods: availablePaymentMethods,
      productForService: productForService,
    };
  }

  validateOrderAdddress(
    address: { [key: string]: string | number | boolean },
    cart_id: string,
  ) {
    const errorKeys = [];
    if (!address.postcode) {
      errorKeys.push('postcode');
    }
    if (!address.firstname) {
      errorKeys.push('name');
    }
    if (!address.region) {
      errorKeys.push('region');
    }
    if (!address.country_id) {
      errorKeys.push('country');
    }
    if (!address.telephone) {
      errorKeys.push('telephone');
    }
    if (!address.city) {
      errorKeys.push('city');
    }
    if (!address.street) {
      errorKeys.push('street');
    }

    if (errorKeys.length > 0) {
      console.log(
        `Address Validation failed: ${cart_id}`,
        JSON.stringify(address),
      );
      const missingFields = errorKeys.join(', ');
      throw new BadRequestException(
        `Your address is incomplete. Please update or change it to include: ${missingFields}`,
      );
    }
  }

  updateRuleUsage(order: SalesOrder) {
    try {
      if (!order.coupon_code && !order.applied_rule_ids) return;
      const applied_discounts = [];
      if (order.coupon_code) {
        applied_discounts.push({
          coupon_code: order.coupon_code,
          customer_id: order.customer_id,
          order_id: order.increment_id,
          used_on: new Date().toISOString(),
          action:
            order.status === OrderStatuses.CANCELLED ? 'decrease' : 'increase',
        });
      }

      if (order.applied_rule_ids.length > 0) {
        order.applied_rule_ids.split(',').map((id) => {
          const numId = Number(id);
          if (!isNaN(numId)) {
            applied_discounts.push({
              rule_id: numId,
              customer_id: order.customer_id,
              order_id: order.increment_id,
              used_on: new Date().toISOString(),
              action:
                order.status === OrderStatuses.CANCELLED
                  ? 'decrease'
                  : 'increase',
            });
          }
        });
      }

      if (applied_discounts.length === 0) return;

      const url =
        config.dk.coupon_service_base_url +
        `/promotion/api/v1/sales-rules/usage`;
      const headers = {
        'x-api-key': config.dk.coupon_service_api_key,
        'Content-Type': 'application/json',
      };

      this.externalServiceCaller
        .post(url, headers, { applied_discounts })
        .then((res) => {
          logger.info(
            ` Sale rule usage update usage response ${order.increment_id}--${
              order.status
            }
            ${JSON.stringify(res)}`,
          );
        })
        .catch((error) => logger.error('Error in updateRuleUsage Api', error));
    } catch (e) {
      logger.log('Error updateRuleUsage function', e);
    }
  }

  async fetchDeliveryInfo(payload: DeliveryRequestPayload) {
    try {
      const url =
        config.dk.utility_service_base_url +
        `/utility-py/api/v1/delivery/product-delivery-options`;
      const data: { response: DeliveryResponse } =
        await this.externalServiceCaller.post(url, {}, payload);
      console.log(`fetchDeliveryInfo: ${JSON.stringify(data)}`);
      if (data?.response?.delivery_info?.length) {
        return {
          max_delivery_days:
            data.response.delivery_info[0]?.max_delivery_days ?? null,
          max_dispatch_days:
            data.response.delivery_info[0]?.max_dispatch_days ?? null,
          max_delivery_warehouse_code:
            data.response.delivery_info[0]?.max_delivery_warehouse_code ?? null,
          delivery_days: data.response.delivery_info[0]?.delivery_days ?? [],
        };
      }
      return null;
    } catch (e) {
      console.log('Error in fetchDeliveryInfo', e?.message || e);
      return null;
    }
  }

  async getEDDInfo(
    order: SalesOrder,
    orderItems: SalesOrderItem[] | any[], // Can be SalesOrderItem[] or order item objects
    orderAddresses: SalesOrderAddress[],
    productForService: productsForService[],
  ): Promise<{
    max_delivery_days: number;
    max_dispatch_days: number;
    max_delivery_warehouse_code?: string;
    delivery_days?: DeliveryDay[];
  }> {
    const { isCod, totalWeight } =
      this.getProductsWeightCodInfo(productForService);

    const billingAddress = orderAddresses?.find(
      (o) => o.address_type === 'billing',
    );
    const shippingAddress = orderAddresses?.find(
      (o) => o.address_type === 'shipping',
    );

    const deliveryPayload: DeliveryRequestPayload = {
      country_code:
        shippingAddress?.country_id || billingAddress?.country_id || 'IN',
      postcode: +shippingAddress.postcode || +billingAddress.postcode,
      product_ids: orderItems?.map((item) => +item.product_id) ?? [],
      cart_data: {
        is_cod_eligible: isCod,
        cart_amount: +order.grand_total,
        cart_weight: totalWeight,
      },
    };

    console.log(
      `order_id: ${order.increment_id}, deliveryPayload: ${JSON.stringify(
        deliveryPayload,
      )}`,
    );

    const deliveryResponse = await this.fetchDeliveryInfo(deliveryPayload);

    console.log(
      `order_id: ${order.increment_id}, deliveryRespnse: ${JSON.stringify(
        deliveryResponse,
      )}`,
    );

    return deliveryResponse;
  }

  /**
   * Updates order item extra info with delivery information
   * @param orderItems Array of order items
   * @param deliveryDays Array of delivery day information per product
   */
  async updateOrderItemsWithDeliveryInfo(
    orderItems: SalesOrderItem[],
    deliveryDays: DeliveryDay[],
  ): Promise<void> {
    try {
      for (const orderItem of orderItems) {
        // Find delivery info for this product
        const deliveryInfo = deliveryDays.find(
          (dd) => dd.product_id === +orderItem.product_id,
        );

        if (deliveryInfo && orderItem.itemExtraInfo) {
          // Update existing extra info with delivery data
          await OrderItemExtraInfo.update(
            {
              delivery_info: {
                days: deliveryInfo.days,
                dispatch_days: deliveryInfo.dispatch_days,
                warehouse_code: deliveryInfo.warehouse_code,
              },
            },
            {
              where: { id: orderItem.itemExtraInfo.id },
            },
          );
        } else if (deliveryInfo && !orderItem.itemExtraInfo) {
          // Create new extra info with delivery data
          await OrderItemExtraInfo.create({
            item_id: orderItem.order_item_id,
            delivery_info: {
              days: deliveryInfo.days,
              dispatch_days: deliveryInfo.dispatch_days,
              warehouse_code: deliveryInfo.warehouse_code,
            },
          });
        }
      }
    } catch (error) {
      console.log('Error updating order items with delivery info:', error);
    }
  }

  async processOrderAndItemExtraInfo(
    order_processing_date: Date,
    order_id: number,
    max_delivery_days: number,
    max_dispatch_days: number,
    sales_order_extra_info: SalesOrderExtraInfo,
    max_delivery_warehouse_code?: string,
    orderItems?: SalesOrderItem[],
    deliveryDays?: any[],
  ) {
    try {
      // Update SalesOrderExtraInfo
      const updateObj: Partial<SalesOrderExtraInfo> = {
        processed_at: order_processing_date,
      };

      if (max_delivery_days && max_dispatch_days) {
        updateObj['exp_delivery_days'] = max_delivery_days;
        updateObj['exp_dispatch_days'] = max_dispatch_days;
      }

      // Include warehouse code if provided
      if (max_delivery_warehouse_code) {
        updateObj['max_delivery_warehouse_code'] = max_delivery_warehouse_code;
      }

      let updatedSalesOrderExtraInfo = sales_order_extra_info;

      if (sales_order_extra_info?.extra_info_entity_id) {
        await SalesOrderExtraInfo.update(
          {
            ...updateObj,
          },
          {
            where: {
              extra_info_entity_id: sales_order_extra_info.extra_info_entity_id,
            },
          },
        );

        sales_order_extra_info.exp_delivery_days = max_delivery_days;
        sales_order_extra_info.exp_dispatch_days = max_dispatch_days;
        sales_order_extra_info.processed_at = order_processing_date;

        // Update warehouse code if provided
        if (max_delivery_warehouse_code) {
          sales_order_extra_info.max_delivery_warehouse_code =
            max_delivery_warehouse_code;
        }

        console.log(
          'OrderExtra updated:',
          JSON.stringify(sales_order_extra_info),
        );

        updatedSalesOrderExtraInfo = sales_order_extra_info;
      } else {
        const extraInfo = await SalesOrderExtraInfo.create({
          order_id,
          ...updateObj,
        });

        console.log('OrderExtra info created:', JSON.stringify(extraInfo));
        updatedSalesOrderExtraInfo = extraInfo;
      }

      // Update OrderItemExtraInfo if orderItems and deliveryDays are provided
      if (orderItems && deliveryDays) {
        await this.updateOrderItemsWithDeliveryInfo(orderItems, deliveryDays);
        console.log(
          `Updated ${orderItems.length} order items with delivery info`,
        );
      }

      return updatedSalesOrderExtraInfo;
    } catch (e) {
      console.log(`Error in processOrderAndItemExtraInfo ${order_id}`, e);
      return null;
    }
  }

  async isFirstOrder(customerId: number) {
    try {
      console.log('++++++isFirstOrderFuncionInput++++++', customerId);
      if (!customerId) return false;
      const orderCount = await SalesOrder.count({
        where: { customer_id: customerId },
      });
      console.log(
        `++++isFirstOrder${customerId}`,
        orderCount,
        orderCount === 1,
      );
      return orderCount === 1;
    } catch (e) {
      return false;
    }
  }

  getHeaderValue = (header: string | string[] | undefined): string =>
    Array.isArray(header) ? header.join(', ') : header ?? '';

  async sendEasyInsightsData(
    request: Request,
    body: FireOrderEventDto,
    userId: number,
    eventName: string,
  ) {
    const easyInsightUrl = `${config.easy_insights_url}/backend/`;
    try {
      const protocol = request.headers['x-forwarded-proto'] || request.protocol;
      const host = request.headers['x-forwarded-host'] || request.get('host');
      const url = `${protocol}://${host}${request.originalUrl}`;

      const payload = {
        url: url,
        user_id: userId
          ? Buffer.from(userId.toString()).toString('base64')
          : null,
        fbp: request.headers['fbp'] || '',
        fbc: request.headers['fbc'] || '',
        gclid: request.headers['gclid'] || '',
        cid: request.headers['cid'] || '',
        ip: request.ip || request.headers['x-forwarded-for'] || '',
        user_agent: request.headers['user-agent'] || '',
        api_payload: JSON.stringify(body),
        event_name: eventName,
      };

      console.log(
        JSON.stringify({
          message: '+++easyInsightUrl payload+++++',
          payload,
        }),
      );

      await this.externalServiceCaller.post(easyInsightUrl, {}, payload);
    } catch (e) {
      console.log(
        JSON.stringify({
          message: '+++Error in sendEasyInsightsData+++',
          url: easyInsightUrl,
          error: e?.message,
        }),
      );
    }
  }

  /**
   * Utility function to handle errors gracefully.
   * @param error - The caught error instance.
   * @param contextMessage - Optional additional context message.
   */
  handleServiceError(error: any, contextMessage?: string): never {
    console.log(`${contextMessage || 'Service Error'}:`, error);

    if (error instanceof BadRequestException) {
      throw error;
    }

    if (error instanceof UnauthorizedException) {
      throw error;
    }

    if (error instanceof SequelizeValidationError) {
      throw new BadRequestException(
        'Database validation error. Please check your input data.',
      );
    }

    if (error instanceof Error) {
      throw new InternalServerErrorException(
        error.message || 'Something went wrong. Please try again later.',
      );
    }

    throw new InternalServerErrorException(
      'Unexpected error occurred. Please try again later.',
    );
  }

  normalizeDate(dateStr?: Date | string): Date {
    const date = dateStr ? new Date(dateStr) : new Date();

    // Normalize the date to UTC and set time to 00:00:00.000
    return new Date(
      Date.UTC(
        date.getUTCFullYear(),
        date.getUTCMonth(),
        date.getUTCDate(),
        0, // Hours
        0, // Minutes
        0, // Seconds
        0, // Milliseconds
      ),
    );
  }

  addDaysToDate(orderDate: Date, daysAddon: number): Date {
    const resultDate = new Date(orderDate.getTime());
    resultDate.setUTCDate(resultDate.getUTCDate() + daysAddon);
    return resultDate;
  }

  getExpectedDeliveryDate(
    orderDate: Date,
    expectedDeliveryDays: number,
  ): string | null {
    try {
      if (!orderDate || !expectedDeliveryDays) return null;
      orderDate = this.normalizeDate(orderDate);
      const resultDate = this.addDaysToDate(orderDate, expectedDeliveryDays);
      const formattedDate = resultDate.toISOString().split('T')[0];
      return formattedDate;
    } catch (e) {
      return null;
    }
  }

  async getMethods() {
    try {
      const url = config.razorpay.methods_api_url;
      const headers = {
        Authorization: `Basic ${Buffer.from(
          config.razorpay.api_key + ':',
        ).toString('base64')}`,
      };
      const data = await this.externalServiceCaller.get(url, headers);
      return data;
    } catch (e) {
      console.log('Error in getPaymentMethods', e?.message || e);
      return null;
    }
  }

  extractAllSkus(cart: GuestCartResponse): string[] {
    const skuSet = new Set<string>();

    // 1. Main item SKUs
    for (const item of cart.items ?? []) {
      if (item?.sku) {
        skuSet.add(item.sku);
      }

      // 2. SKUs from item-level promotions
      for (const promo of item.quote_item_promotions ?? []) {
        if (promo?.sku) {
          skuSet.add(promo.sku);
        }
      }
    }

    // 3. SKUs from cart-level amount promotions
    for (const promo of cart.quote_amount_promotions ?? []) {
      if (promo?.sku) {
        skuSet.add(promo.sku);
      }
    }

    return Array.from(skuSet);
  }
}
