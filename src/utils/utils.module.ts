import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { CartMapper } from 'src/mapper/cart.mapper';
import { CartService } from './cart.service';
import { ExternalApiCaller } from './external-api-caller';
import { CartHelperFunctions } from './cart-service-helper';
import { ExternalApiHelper } from './external-api.helper';
import { MaskHelper } from './mask.helper';
import { ShippingMethodModule } from '../shipping-method/shipping-method.module';
import { CartUpdateService } from './cart-webhook-service';
import { DatabaseModule } from 'src/database/database.module';
import { CartUtilityFunctions } from './cart-utility-function';
import { CouponService } from './coupon-service';
import { RewardService } from './reward-service';
import { CartAddressService } from './cart-address-service';

@Module({
  imports: [
    HttpModule.register({ maxRedirects: 5, timeout: 10000 }),
    ShippingMethodModule,
    DatabaseModule,
  ],
  providers: [
    CartMapper,
    CartService,
    CartHelperFunctions,
    ExternalApiHelper,
    ExternalApiCaller,
    MaskHelper,
    CartUpdateService,
    CartUtilityFunctions,
    CouponService,
    RewardService,
    CartAddressService,
  ],
  exports: [
    CartMapper,
    UtilsModule,
    HttpModule,
    ExternalApiHelper,
    ExternalApiCaller,
    CartUpdateService,
    CartUtilityFunctions,
    CartService,
    MaskHelper,
    CartHelperFunctions,
    RewardService,
    CouponService,
    CartAddressService,
  ],
})
export class UtilsModule {}
