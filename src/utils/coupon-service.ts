import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { AddressTypes, CartAction } from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { CartMapper } from 'src/mapper/cart.mapper';
import { ExternalApiHelper } from './external-api.helper';
import { logger } from './service-logger';
import * as _ from 'lodash';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { Sequelize } from 'sequelize-typescript';
import { SetCouponCodeToCart } from '../interface/set-coupon-cart-to-cart';
import { RemoveCouponFromCart } from 'src/interface/remove-coupon-from-cart';
import { DiscountType } from '../config/constants';
import { CartHelperFunctions } from './cart-service-helper';
import { CartUtilityFunctions } from './cart-utility-function';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import { AmountPromotion } from 'src/database/entities/amount_promotion';
import { OutputResponseType } from 'src/config/constants';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
@Injectable()
export class CouponService {
  constructor(
    private readonly cartMapper: CartMapper,
    private readonly cartHelperFunctions: CartHelperFunctions,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly cartUtilityFun: CartUtilityFunctions,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  /**
   * It build data to be stored in QuoteDiscount table
   * @param input          input
   * @param customerId     Registered user's customer-id
   * @returns
   **/
  async applyCouponToCart(
    input: SetCouponCodeToCart,
    customerId?: number,
    magentoCustomerId?: number,
    customerGroupId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    const cartIDExists = await Quote.findOne({
      where: {
        masked_id: input.cart_id,
        customer_id: customerId || null,
        is_active: input.is_buy_now_cart ? false : true,
      },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });

    if (!cartIDExists) {
      throw new BadRequestException('Cart not found!');
    }

    if (cartIDExists.items.length === 0) {
      throw new BadRequestException('Coupon can not apply on empty cart');
    }

    const couponCode = cartIDExists?.discount?.discounts?.find(
      (o) => o?.coupon_code === input.coupon_code,
    );

    if (couponCode) throw new BadRequestException('Coupon already applied!');

    try {
      const skuWiseErrors = {},
        parentMappedPrice = {};
      const shippingAddress = cartIDExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = cartIDExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const skus = cartIDExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartIDExists.items,
        [],
        'apply_coupon',
      );
      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      cartIDExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartIDExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          shippingAddress?.customer_country_id,
        );

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: cartIDExists.items,
          quote: cartIDExists,
          billingAddress,
          shippingAddress,
          couponCode: input.coupon_code,
          countryId: country_id,
          regionId: region_id,
          customerId,
          quote_filter: input.is_buy_now_cart ? { is_active: false } : null,
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          isCouponApply: true,
          outputResponseType,
          cartAction: CartAction.APPLY_COUPON,
          itemPromotions,
          productTaxes,
        });

      const updatedQuote = input.is_buy_now_cart
        ? await Quote.findOne({
            where: { quote_id: cartIDExists.quote_id, is_active: false },
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          })
        : await Quote.findByPk(cartIDExists.quote_id, {
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
      );
    } catch (error) {
      logger.error('Error in applyCouponCart', error);
      this.cartUtilityFun.throwError(error);
    }
  }
  /**It remove the coupon from Quote_discount and update the Quote table
   * according to coupon details
   * @param input         RemoveCouponFromCart
   * @param customerId    number
   * @returns
   */
  async removeCouponFromCart(
    input: RemoveCouponFromCart,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    try {
      const cartIDExists = await Quote.findOne({
        where: {
          masked_id: input.cart_id,
          customer_id: customerId || null,
          is_active: input.is_buy_now_cart ? false : true,
        },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });
      if (!cartIDExists) throw new BadRequestException('Cart not found');
      if (!cartIDExists.coupon_code)
        throw new BadRequestException('Coupon not applied');
      const singleCouponIndex = cartIDExists.discount?.discounts.findIndex(
        (coupon) => coupon.coupon_code,
      );
      if (singleCouponIndex === -1)
        throw new BadRequestException('Coupon not applied');
      const skuWiseErrors = {},
        parentMappedPrice = {};
      const shippingAddress = cartIDExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = cartIDExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const skus = cartIDExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartIDExists.items,
        [],
        'remove_coupon',
      );
      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );
      cartIDExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartIDExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          country_id,
        );

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: cartIDExists.items,
          quote: cartIDExists,
          billingAddress,
          shippingAddress,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartIDExists),
          customerId,
          countryId: country_id,
          regionId: region_id,
          quote_filter: input.is_buy_now_cart ? { is_active: false } : null,
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          isCouponRemove: true,
          outputResponseType,
          cartAction: CartAction.REMOVE_COUPON,
          itemPromotions,
          productTaxes,
        });

      const updatedQuote = input.is_buy_now_cart
        ? await Quote.findOne({
            where: { quote_id: cartIDExists.quote_id, is_active: false },
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          })
        : await Quote.findByPk(cartIDExists.quote_id, {
            include: [
              {
                model: QuoteItem,
                include: [
                  {
                    model: QuoteItemExtensionAttribute,
                  },
                  {
                    model: QuoteItemPromotion,
                  },
                ],
              },
              QuoteAmountPromotion,
              QuoteDiscount,
            ],
          });

      //updatedQuote.discount = cartIDExists.discount;
      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
      );
    } catch (error) {
      logger.error('Error in removeCouponFromCart', error);
      this.cartUtilityFun.throwError(error);
    }
  }

  async customerCoupons(
    cart_id: string,
    is_buy_now_cart: boolean,
    customerId?: number,
    customerGroupId?: number,
  ) {
    try {
      // if (cart_id) {
      //   const cart = await Quote.findOne({
      //     where: {
      //       masked_id: cart_id,
      //       customer_id: customerId || null,
      //       is_active: is_buy_now_cart ? false : true,
      //     },
      //     include: [
      //       {
      //         model: QuoteItem,
      //         include: [
      //           {
      //             model: QuoteItemExtensionAttribute,
      //             include: [ItemPromotion, AmountPromotion],
      //           },
      //         ],
      //       },
      //       QuoteAddress,
      //       QuoteDiscount,
      //     ],
      //   });
      //   if (!cart) throw new BadRequestException('Cart not found');
      //   const productsExcludeFreeProducts = cart.items.filter((item) => {
      //     if (!item?.extension_attribute?.is_free_product) {
      //       return item;
      //     }
      //   });

      //   const productDetails =
      //     await this.externalApiHelper.getProductDataFromSku(
      //       productsExcludeFreeProducts.map((o) => o.sku),
      //     );

      //   const shippingAddress = cart.addresses?.find(
      //     (o) => o.address_type === AddressTypes.SHIPPING,
      //   );
      //   const billingAddress = cart.addresses?.find(
      //     (o) => o.address_type === AddressTypes.BILLING,
      //   );
      //   const { country_id } = this.cartUtilityFun.getCountryAndRegion(
      //     shippingAddress,
      //     billingAddress,
      //   );

      //   const formattedProducts =
      //     this.cartUtilityFun.getFormattedProductsForCoupon(
      //       productsExcludeFreeProducts,
      //       productDetails,
      //     );

      //   const couponsList = await this.externalApiHelper.getCustomerCoupons(
      //     customerId,
      //     customerGroupId,
      //     country_id,
      //     formattedProducts,
      //   );

      //   const formattedCoupon = couponsList?.map((coupon) => ({
      //     coupon_code: coupon.code,
      //     description: coupon.title,
      //     expiry_date: coupon.expiry,
      //   }));

      //   return formattedCoupon;
      // } else {
      const couponsList = await this.externalApiHelper.getCustomerCouponsNew(
        customerId,
        customerGroupId,
      );

      const formattedCoupon = couponsList?.map((coupon) => ({
        coupon_code: coupon.code,
        description: coupon.title,
        expiry_date: coupon.expiry,
      }));

      return formattedCoupon;
      // }
    } catch (error) {
      logger.error('Error in customerCoupons', error);
      throw new InternalServerErrorException(error);
    }
  }
}
