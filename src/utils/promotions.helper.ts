import { ItemPromotion } from 'src/database/entities/item_promotion';

export class PromotionHelper {
  constructor() {}

  /**
   * Helper for updating product's extension attributes
   * @returns
   */
  extensionAttributePayload(
    promotions: ItemPromotion[],
    isDeleted: boolean,
    updatedEndDate?: Date,
  ) {
    const extensionAttributes = promotions.map(
      ({
        product_sku,
        free_product_sku,
        buy_qty,
        free_product_qty,
        is_multiply,
        start_date,
        end_date,
      }) => ({
        sku: product_sku,
        free_product_attributes: isDeleted
          ? []
          : [
              {
                free_sku: free_product_sku,
                free_qty: +free_product_qty,
                buy_qty: +buy_qty,
                is_multiply,
                start_date,
                end_date: updatedEndDate || end_date,
              },
            ],
      }),
    );

    return { extension_attributes: extensionAttributes };
  }
}
