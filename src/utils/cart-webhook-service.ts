import { Injectable, Inject } from '@nestjs/common';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { CartMapper } from 'src/mapper/cart.mapper';
import { logger } from './service-logger';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { Sequelize } from 'sequelize-typescript';
import * as _ from 'lodash';
import { ProductTypes, AddressTypes } from '../config/constants';

@Injectable()
export class CartUpdateService {
  constructor(
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
    private readonly cartMapper: CartMapper,
  ) {}

  async updateCart(data: any) {
    try {
      if (!data.customer_id) return false;
      if (+data.is_active === 0) return false;
      if (!data.masked_cart_id) return false;
      const quoteExists = await Quote.findOne({
        where: {
          customer_id: data.customer_id,
          masked_id: data.masked_cart_id,
        },
        include: [Quote<PERSON>tem, QuoteDiscount, QuoteAddress],
      });
      if (!quoteExists) {
        const quoteExists = await Quote.findOne({
          where: {
            customer_id: data.customer_id,
          },
        });
        if (quoteExists) {
          await Quote.update(
            {
              is_active: false,
            },
            {
              where: { quote_id: quoteExists.quote_id },
            },
          );
        }
        const createUserRes = await this.creatUserCartAndSaveDetails(data);

        // logger.info(
        //   `creatUserCartAndSaveDetails: ${data.customer_id}: ${JSON.stringify(
        //     createUserRes,
        //   )}`,
        // );

        return createUserRes;
      } else {
        const updateRes = await this.updateExistingCart(data, quoteExists);

        // logger.info(
        //   `updateExistingCart: ${data.customer_id}: ${JSON.stringify(
        //     updateRes,
        //   )}`,
        // );

        return updateRes;
      }
    } catch (e) {
      logger.error('Error in updateCart', e);
      logger.error(
        `ErrorIn Webhook updateCart: ${JSON.stringify(e?.message || e)}`,
      );
      return false;
    }
  }

  async creatUserCartAndSaveDetails(data: any) {
    try {
      const { quote, address } =
        this.cartMapper.mapMagentoCartToexistingCart(data);
      quote['masked_id'] = data.masked_cart_id;
      const newCart = await Quote.create(quote, {});
      if (address.shipping) {
        address.shipping['address_type'] = AddressTypes.SHIPPING;
        await this.updateOrCreateQuoteAddress(
          address.shipping,
          newCart.quote_id,
          null,
        );
      }
      if (address.billing) {
        address.billing['address_type'] = AddressTypes.BILLING;
        await this.updateOrCreateQuoteAddress(
          address.billing,
          newCart.quote_id,
          null,
        );
      }
      if (data.items.length) {
        const items = this.cartMapper.mapMagentoCartItems(
          data.items,
          newCart.quote_id,
        );
        await QuoteItem.bulkCreate(items, {});
      }
      return true;
    } catch (e) {
      logger.error('Error in creatUserCartAndSaveDetails', e);
      return false;
    }
  }

  async updateExistingCart(data: any, quoteData: Quote) {
    try {
      const { quote, address } =
        this.cartMapper.mapMagentoCartToexistingCart(data);
      const magentoItemsSkus = data?.items?.map((data: any) => data.sku);
      const destroyItemsId = [];
      const modifyItemsObj = [];
      const shippingAddress: QuoteAddress = this.getAddress(
        quoteData,
        AddressTypes.SHIPPING,
      );
      const billingAddress: QuoteAddress = this.getAddress(
        quoteData,
        AddressTypes.BILLING,
      );

      //updated quote table
      await Quote.update(
        { ...quote },
        {
          where: { quote_id: quoteData.quote_id },
        },
      );
      //update billing shipping address
      if (shippingAddress && address.shipping) {
        address.shipping['address_type'] = AddressTypes.SHIPPING;
        await this.updateOrCreateQuoteAddress(
          address.shipping,
          quoteData.quote_id,
          shippingAddress,
        );
      }
      if (billingAddress && address.billing) {
        address.billing['address_type'] = AddressTypes.BILLING;
        await this.updateOrCreateQuoteAddress(
          address.billing,
          quoteData.quote_id,
          billingAddress,
        );
      }
      if (magentoItemsSkus?.length) {
        for (const item of quoteData.items) {
          const index = magentoItemsSkus.indexOf(item.sku);
          if (index !== -1) {
            modifyItemsObj.push({
              sku: item.sku,
              quote_item_id: item.quote_item_id,
            });
            magentoItemsSkus.splice(index, 1);
          } else {
            destroyItemsId.push(item.quote_item_id);
          }
        }
      } else {
        await QuoteItem.destroy({
          where: { quote_id: quoteData.quote_id },
          force: true,
        });
      }
      //destroy unmatched skus items
      if (destroyItemsId.length) {
        await QuoteItem.destroy({
          where: { quote_item_id: [...destroyItemsId] },
          force: true,
        });
      }
      //modify existing  cart items
      if (modifyItemsObj.length) {
        await this.updateQuoteItems(modifyItemsObj, data.items);
      }
      //creates new quote items
      if (magentoItemsSkus.length) {
        const newItems = this.cartMapper.mapMagentoCartItems(
          data.items.filter(
            (data: any) => magentoItemsSkus.indexOf(data.sku) !== -1,
          ),
          quoteData.quote_id,
        );
        await QuoteItem.bulkCreate(newItems, {});
      }
      return true;
    } catch (e) {
      logger.error('Error in updateExistingCart', e);
      return false;
    }
  }

  async updateQuoteItems(modifyItemsObj: any, magentoItems: any) {
    for (const data of modifyItemsObj) {
      const item = _.find(magentoItems, { sku: data.sku });
      if (item) {
        const obj = {
          parent_item_id: null,
          is_virtual: item.product_type === ProductTypes.VIRTUAL ? true : false,
          sku: item.sku,
          name: item.name,
          description: item.description,
          weight: +item.weight,
          qty: +item.qty,
          price: +item.price,
          base_price: +item.price,
          row_total: +item.row_total,
          base_row_total: +item.base_row_total,
          row_total_with_discount: +item.row_total_with_discount,
          row_weight: +item.row_weight,
          product_type: item.product_type,
          price_incl_tax: +item.price_incl_tax,
          base_price_incl_tax: +item.base_price_incl_tax,
          row_total_incl_tax: +item.row_total_incl_tax,
          base_row_total_incl_tax: +item.base_row_total_incl_tax,
          discount_amount: +item.discount_amount,
          discount_percent: +item.discount_percent,
        };
        await QuoteItem.update(obj, {
          where: { quote_item_id: data.quote_item_id },
        });
      }
    }
  }

  getAddress(quote: Quote, OffType: string) {
    return quote.addresses.find((address) => address.address_type === OffType);
  }

  async updateOrCreateQuoteAddress(
    updateAddress: any,
    quote_id: number,
    addresseExist?: QuoteAddress,
  ) {
    if (addresseExist && updateAddress) {
      await QuoteAddress.update(
        { ...updateAddress },
        {
          where: {
            quote_address_id: addresseExist.quote_address_id,
          },
        },
      );
    } else {
      await QuoteAddress.create({
        quote_id: quote_id,
        ...updateAddress,
      });
    }
  }
}
