import { Quote } from 'src/database/entities/quote';
import * as uuid from 'uuid';

export class MaskHelper {
  /**
   * It generates & returns a unique masked_id of 32 alphanumeric characters
   * @returns
   */
  generateQuoteMaskedId = async () => {
    const masked_id = uuid.v4().replace(/-/g, '');
    const idExists = await Quote.findOne({ where: { masked_id } });
    if (idExists) {
      return this.generateQuoteMaskedId();
    }
    return masked_id;
  };

  /**
   * It formats requested date in yyyy-mm-dd format
   * @param date Date object
   * @returns
   */
  maskDateInYYYYMMDD = (date: Date) => {
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const day = date.getDate();
    return `${year}-${month < 10 ? `0${month}` : month}-${
      day < 10 ? `0${day}` : day
    }`;
  };
}
