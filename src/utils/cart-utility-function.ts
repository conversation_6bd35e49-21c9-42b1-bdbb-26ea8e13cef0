import {
  BadRequestException,
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  CartAction,
  MEMBERSHIP_SKUS,
  ProductStatuses,
  ProductTypes,
  SalesRulesSimpleAction,
  SERVER_ADDED_HOURS,
} from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import {
  DiscountValues,
  ItemDiscount,
} from '../interface/quote-discount-object';
import { QuoteAddress } from 'src/database/entities/quote_address';
import {
  DirectCondition,
  SalesRule,
  SalesRuleCondition,
} from 'src/interface/sales-rules';
import { QuoteDiscountsObject } from 'src/interface/discount';
import { DiscountType } from '../config/constants';
import { ValidateProuductArgs } from '../interface/validate-product-args';
import * as _ from 'lodash';
import { CarriersConfig } from '../database/entities/carriers_config';
import { ProductData } from 'src/interface/graphql-response';
import {
  CartSkuQtyInterface,
  CartIdQtyInterface,
} from 'src/interface/add-product-to-cart-request';
import {
  ParentUnitPriceInterface,
  SkuWiseParentIdAndQTy,
} from '../interface/parent-mapped-price';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { AmountPromotion } from 'src/database/entities/amount_promotion';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { DiscountItem } from 'src/interface/coupon-response';
import { RewardAndMembershipResponse } from 'src/interface/reward-and-membership-response';
import { Op } from 'sequelize';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
import { ExternalApiHelper } from './external-api.helper';
import {
  AmountPromotionProductData,
  ItemPromotionProductData,
} from 'src/interface/promotion-product-data';
import { CartMapper } from 'src/mapper/cart.mapper';
import { CartHelperFunctions } from './cart-service-helper';

@Injectable()
export class CartUtilityFunctions {
  constructor(
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly cartMapper: CartMapper, // private readonly cartHelperFunctions: CartHelperFunctions,
  ) {}

  validateProductAvailability(args: ValidateProuductArgs) {
    const {
      product,
      requestCartItems,
      throwError,
      skuWiseErrors,
      country_id,
      qty,
      qtyAddon,
      // actualItemQty,
    } = args;
    const itemErrors = [];
    if (product?.status !== ProductStatuses.ENABLE) {
      itemErrors.push({
        code: 'disabled_product',
        message: VALIDATION_ERRORS['disabled_product'].message(product.name),
      });
      if (throwError)
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['disabled_product'].message(product.name),
          VALIDATION_ERRORS['disabled_product'].errorCode,
        );
    }
    if (!product.is_in_stock) {
      itemErrors.push({
        code: 'out_of_stock',
        message: VALIDATION_ERRORS['out_of_stock'].message(product.name),
      });
      if (throwError)
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['out_of_stock'].message(product.name),
          VALIDATION_ERRORS['out_of_stock'].errorCode,
        );
    }
    if (product.is_cod === '0') {
      itemErrors.push({
        code: 'not_cod',
        message: `Cod not available`,
      });
    }

    let requestedQty = qty
      ? qty
      : _.find(requestCartItems, {
          sku: product.sku,
        })?.quantity;
    if (qtyAddon) requestedQty += qtyAddon;
    if (requestedQty < product.min_sale_qty) {
      itemErrors.push({
        code: 'min_qty_error',
        message: VALIDATION_ERRORS['min_qty_error'].message(
          product.name,
          product.min_sale_qty,
        ),
      });
      if (throwError)
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['min_qty_error'].message(
            product.name,
            product.min_sale_qty,
          ),
          VALIDATION_ERRORS['min_qty_error'].errorCode,
        );
    }
    if (requestedQty > product.qty && !product.backorders) {
      if (product.qty > 0) {
        itemErrors.push({
          code: 'max_allowed_qty',
          message: VALIDATION_ERRORS['max_allowed_qty'].message(
            product.name,
            product.qty,
          ),
        });
      }
      if (throwError) {
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['max_allowed_qty'].message(
            product.name,
            product.qty,
          ),
          VALIDATION_ERRORS['max_allowed_qty'].errorCode,
        );
      }
    }

    if (requestedQty > product.max_sale_qty) {
      itemErrors.push({
        code: 'max_qty_error',
        message: VALIDATION_ERRORS['max_qty_error'].message(
          product.name,
          product.max_sale_qty,
        ),
      });
      if (throwError) {
        throw new CustomBadRequestException(
          VALIDATION_ERRORS['max_qty_error'].message(
            product.name,
            product.max_sale_qty,
          ),
          VALIDATION_ERRORS['max_qty_error'].errorCode,
        );
      }
    }
    if (country_id && country_id !== 'IN') {
      if (!product.international_active) {
        itemErrors.push({
          code: 'internationally_inactive',
          message: VALIDATION_ERRORS['internationally_inactive'].message(
            product.name,
          ),
        });
        if (throwError) {
          throw new CustomBadRequestException(
            VALIDATION_ERRORS['internationally_inactive'].message(product.name),
            VALIDATION_ERRORS['internationally_inactive'].errorCode,
          );
        }
      }
    }

    if (skuWiseErrors) {
      skuWiseErrors[product.sku] = itemErrors;
    }

    const buyingGuideQty = _.find(requestCartItems, {
      sku: product.sku,
    })?.buying_guide_qty;

    if (buyingGuideQty) {
      product['buying_guide_qty'] = buyingGuideQty;
    }
    product['qty'] = requestedQty;
  }

  /**
   * It calculates total-amount & total-weight details to be stored
   * in quoted based on the items present in quote
   * @param allItems QuoteItem[]
   * @returns
   */
  calculateQuoteFiguresFromItems = (allItems: QuoteItem[]) => {
    let grandTotal = 0;
    let totalWeight = 0;
    let itemsQty = 0;
    let overWeightDeliveryCharges = 0;
    let subtotalWithDiscount = 0;
    let grandTotalExcludingVirtualProduct = 0;
    let totalWeightExcludingVirtualProduct = 0;
    let total_items_savings = 0;
    let admin_discount = 0;
    let adminItemWiseDiscount = {};
    let subtotalExcludingTax = 0;
    let reward_discount = 0;
    let delivery_charges = 0;
    let auto_discount = 0;
    let grandTotalForCoupon = 0;
    for (const item of allItems) {
      grandTotal += +item.row_total_incl_tax;
      subtotalExcludingTax += +item.row_total;
      if (item.row_total_savings) {
        total_items_savings += +item.row_total_savings;
      }
      if (item.item_handling_fee) {
        overWeightDeliveryCharges += +item.item_handling_fee * item.qty;
      }
      totalWeight += +item.row_weight;
      itemsQty += +item.qty;
      subtotalWithDiscount += +item.row_total_with_discount;
      if (item.product_type !== ProductTypes.VIRTUAL) {
        grandTotalExcludingVirtualProduct += +item.row_total_incl_tax;
        totalWeightExcludingVirtualProduct += +item.row_weight;
      }

      if (
        item?.extension_attribute?.admin_discount_amount !== undefined &&
        item?.extension_attribute?.admin_discount_amount >= 0
      ) {
        admin_discount += +item?.extension_attribute.admin_discount_amount;
        adminItemWiseDiscount[item.quote_item_id] = {
          admin_discount_amount:
            +item?.extension_attribute?.admin_discount_amount || 0,
          admin_discount_percent:
            +item?.extension_attribute?.admin_discount_percent || 0,
        };
      }
    }

    return {
      grandTotal,
      totalWeight,
      itemsQty,
      subtotalWithDiscount,
      grandTotalExcludingVirtualProduct,
      totalWeightExcludingVirtualProduct,
      overWeightDeliveryCharges,
      total_items_savings,
      admin_discount,
      adminItemWiseDiscount,
      subtotalExcludingTax,
      reward_discount,
      delivery_charges,
      auto_discount,
      grandTotalForCoupon,
    };
  };

  /**
   * It does mapping of discount rule condition
   * @param condition rule-condition/rule-action-condition
   * @returns
   */
  operatorMapping = (condition: {
    operator: string;
    attribute: string | number | string[];
    value: string;
    values?: string[];
  }) => {
    const { operator, attribute, value, values } = condition;

    switch (operator) {
      case '>=': {
        if (!value && values?.length) {
          for (const value of values) {
            if (attribute >= value) return true;
          }
          return false;
        }
        return attribute >= value;
      }
      case '<=': {
        if (!value && values?.length) {
          for (const value of values) {
            if (attribute <= value) return true;
          }
          return false;
        }
        return attribute <= value;
      }

      case '==': {
        if (attribute && typeof attribute === 'object') {
          if (values) {
            for (const obj of attribute as string[]) {
              if (values.indexOf(obj) !== -1) return true;
            }
            return false;
          } else return attribute.includes(value);
        }
        if (values?.length && typeof attribute !== 'object') {
          for (const value of values) {
            if (attribute == value) return true;
          }
          return false;
        }
        return attribute == value;
      }
      case '<': {
        if (!value && values?.length) {
          for (const value of values) {
            if (attribute < value) return true;
          }
          return false;
        }
        return attribute < value;
      }

      case '>': {
        if (!value && values?.length) {
          for (const value of values) {
            if (attribute > value) return true;
          }
          return false;
        }
        return attribute > value;
      }
      case '()': {
        if (typeof attribute !== 'object') return false;
        if (values?.length) {
          for (const value of values as string[]) {
            const items = value?.split(/[ ,]+/);
            if (items?.length) {
              for (const item of items) {
                if (attribute.indexOf(item) !== -1) return true;
              }
            }
          }
        } else if (value && typeof value === 'string') {
          const items = value?.split(/[ ,]+/);
          if (items?.length) {
            for (const item of items) {
              if (attribute.indexOf(item) !== -1) return true;
            }
          }
        }
        return false;
      }
      case '{}': {
        if (typeof attribute !== 'object') return false;
        if (values) {
          let isContained = false;
          for (const obj of values) {
            if (values.every((o) => o.includes(obj))) {
              isContained = true;
            }
          }
          return isContained;
        }
        return attribute.every((o) => o.includes(value));
      }

      default:
        return false;
    }
  };

  /**
   * It maps attributes of discount rule condition
   * @param name
   * @param subTotal
   * @param countryId
   * @param skus
   * @param manufacturers
   * @returns
   */
  attributeMapping = (
    name: string,
    subTotal: number,
    countryId: string,
    skus: string[],
    manufacturers: string[],
  ) => {
    switch (name) {
      case 'base_subtotal':
        return subTotal;
      case 'country_id':
        return countryId;
      case 'sku':
        return skus;
      case 'manufacturer':
        return manufacturers;
      default:
        break;
    }
  };

  /**
   * It verifies the discount rule condition having
   * single condition only
   * @param ruleCondition
   * @param subTotal
   * @param countryId
   * @param skus
   * @param manufacturers
   * @returns
   */
  performSingleCheck = (
    ruleCondition: DirectCondition,
    subTotal: number,
    countryId: string,
    skus: string[],
    manufacturers: string[],
  ) => {
    if (
      this.operatorMapping({
        attribute: this.attributeMapping(
          ruleCondition.attribute_name,
          subTotal,
          countryId,
          skus,
          manufacturers,
        ),
        operator: ruleCondition.operator,
        value: ruleCondition.value,
      })
    ) {
      return true;
      // apply discounts
    }
    return false;
  };

  /**
   * It verifies the discount rule condition having
   * two conditions only
   * @param ruleCondition
   * @param actionCondition
   * @param subTotal
   * @param countryId
   * @param skus
   * @param manufacturers
   * @returns
   */
  performCombinedCheck = (
    ruleCondition: SalesRuleCondition,
    actionCondition: SalesRuleCondition,
    subTotal: number,
    countryId: string,
    skus: string[],
    manufacturers: string[],
  ) => {
    let isCondTrue = false;
    if (ruleCondition.aggregator_type === 'all') {
      const isValid = ruleCondition.conditions.every((o) =>
        this.operatorMapping({
          attribute: this.attributeMapping(
            o.attribute_name,
            subTotal,
            countryId,
            skus,
            manufacturers,
          ),
          operator: o.operator,
          value: o.value,
        }),
      );
      isCondTrue = isValid;
    }
    if (ruleCondition.aggregator_type === 'any') {
      const values = ruleCondition.conditions.map((o) => o.value);
      this.operatorMapping({
        attribute: this.attributeMapping(
          ruleCondition.conditions[0]?.attribute_name,
          subTotal,
          countryId,
          skus,
          manufacturers,
        ),
        operator: ruleCondition.conditions[0]?.operator,
        value: null,
        values,
      })
        ? (isCondTrue = true)
        : (isCondTrue = false);
    }
    if (isCondTrue) {
      // verify action conditions
      if (actionCondition.conditions) {
        if (actionCondition.aggregator_type === 'any') {
          const values = actionCondition.conditions.map((o) => o.value);
          if (
            this.operatorMapping({
              attribute: this.attributeMapping(
                actionCondition.conditions[0].attribute_name,
                subTotal,
                countryId,
                skus,
                manufacturers,
              ),
              operator: actionCondition.conditions[0].operator,
              value: null,
              values,
            })
          ) {
            return true;
            // apply discounts
          }
          return false; // false case
        }
        if (actionCondition.aggregator_type === 'all') {
          if (
            actionCondition.conditions.every((o) =>
              this.operatorMapping({
                attribute: this.attributeMapping(
                  o.attribute_name,
                  subTotal,
                  countryId,
                  skus,
                  manufacturers,
                ),
                operator: o.operator,
                value: o.value,
              }),
            )
          ) {
            return true;
            // apply discounts
          }
          return false; // false case
        }
      }
      return true;
    }
    return false;
  };

  /**
   * It performs check on aggregation type for requested
   * discount rule condition
   * @param ruleCondition
   * @param subTotal
   * @param countryId
   * @param skus
   * @param manufacturers
   * @returns
   */
  applyAggregationCheck = (
    ruleCondition: SalesRuleCondition,
    subTotal: number,
    countryId: string,
    skus: string[],
    manufacturers: string[],
  ) => {
    if (ruleCondition.conditions) {
      if (ruleCondition.aggregator_type === 'any') {
        const values = ruleCondition.conditions.map((o) => o.value);
        if (
          this.operatorMapping({
            attribute: this.attributeMapping(
              ruleCondition.conditions[0].attribute_name,
              subTotal,
              countryId,
              skus,
              manufacturers,
            ),
            operator: ruleCondition.conditions[0].operator,
            value: null,
            values,
          })
        ) {
          return true;
          // apply discounts
        }
      }
      if (ruleCondition.aggregator_type === 'all') {
        if (
          ruleCondition.conditions.every((o) =>
            this.operatorMapping({
              attribute: this.attributeMapping(
                o.attribute_name,
                subTotal,
                countryId,
                skus,
                manufacturers,
              ),
              operator: o.operator,
              value: o.value,
            }),
          )
        ) {
          return true;
          // apply discounts
        }
      }
    } else return true;
    return false;
  };

  /**
   * It applies discount on cart with respective to
   * sample_action type
   * @param rule
   * @param quote
   * @param allItems
   * @param subTotal
   * @param countryId
   * @returns
   */
  applyDiscount = (
    rule: SalesRule,
    quote: Quote,
    allItems: QuoteItem[],
    subTotal: number,
    countryId: string,
  ) => {
    if (rule.simple_action === 'cart_fixed') {
      const discountRows = [];
      let discountAmount = 0;
      if (rule.discount_amount > 0) {
        for (const item of allItems) {
          const discount = +(
            (item.row_total_incl_tax / subTotal) *
            rule.discount_amount
          ).toFixed(3);
          discountRows.push({
            quote_item_id: item.quote_item_id,
            discount_percent: 0,
            discount_label: rule.name,
            discount_amount: discount,
            discount_id: rule.rule_id,
            is_free_shipping:
              rule.simple_free_shipping === '2' ||
              rule.simple_free_shipping === '1'
                ? true
                : false,
          });
          discountAmount += discount;
        }
        return { discountAmount, discountRows };
      }
      return this.applyDiscountOnCart(quote, rule);
    }
    return this.applyDiscountOnProduct(allItems, rule, subTotal, countryId);
  };

  /**
   * It's used to apply discount on cart with respect to
   * product (for simple_actions - by_percent, by_fixed, by_x_get_y)
   * @param quoteId
   * @param quoteItems
   * @param rule
   * @param subTotal
   * @param countryId
   * @returns
   */
  applyDiscountOnProduct = (
    quoteItems: QuoteItem[],
    rule: SalesRule,
    subTotal: number,
    countryId: string,
  ) => {
    const itemRules = [];
    for (const item of quoteItems) {
      if ((rule.condition.conditions?.[0] as SalesRuleCondition)?.conditions) {
        if (
          this.checkNestedConditions(
            rule,
            subTotal,
            countryId,
            [item.sku],
            [item.manufacturer],
          )
        ) {
          itemRules.push(item);
        }
      } else if (
        this.applyAggregationCheck(
          rule.condition as SalesRuleCondition,
          subTotal,
          countryId,
          [item.sku],
          [item.manufacturer],
        ) &&
        this.applyAggregationCheck(
          rule.action_condition as SalesRuleCondition,
          subTotal,
          countryId,
          [item.sku],
          [item.manufacturer],
        )
      ) {
        itemRules.push(item);
      } else if (
        !rule.condition.conditions &&
        this.applyAggregationCheck(
          rule.action_condition as SalesRuleCondition,
          subTotal,
          countryId,
          [item.sku],
          [item.manufacturer],
        )
      ) {
        // apply discounts
        itemRules.push(item);
      }
    }
    return this.applyDiscountOnItem(itemRules, rule);
  };

  /**
   * It's used to apply discount on cart (where sample_action is - cart_fixed)
   * @param quote
   * @param rule
   * @returns
   */
  applyDiscountOnCart = async (quote: Quote, rule: SalesRule) => {
    const discountRow = {
      quote_id: quote.quote_id,
      discount_label: rule.name,
      discount_percent: 0,
      discount_amount: rule.discount_amount,
      discount_id: rule.rule_id,
      is_free_shipping:
        rule.simple_free_shipping === '2' || rule.simple_free_shipping === '1'
          ? true
          : false,
    };
    return {
      discountAmount: rule.discount_amount,
      discountRows: [discountRow],
    };
  };

  /**
   * Applies actual discount on cart item
   * by inserting a row in quoteDiscount table
   * @param quoteId
   * @param ruleItems
   * @param rule
   * @returns
   */
  applyDiscountOnItem = (ruleItems: QuoteItem[], rule: SalesRule) => {
    const discountRows = [];
    let discountAmount = 0;
    for (const item of ruleItems) {
      if (rule.simple_action === SalesRulesSimpleAction.BUY_X_GET_Y) {
        let appliedQty = 0;
        let itemQtyInCart = +item.qty;
        let priceDiscount = 0;
        while (
          itemQtyInCart > 0 && rule.discount_qty
            ? rule.discount_qty >= appliedQty + rule.discount_step
            : true
        ) {
          if (itemQtyInCart >= rule.discount_amount + rule.discount_step) {
            priceDiscount += item.price_incl_tax * rule.discount_amount;
            itemQtyInCart -= rule.discount_amount + rule.discount_step;
            appliedQty += rule.discount_step;
          } else break;
        }
        discountRows.push({
          quote_item_id: item.quote_item_id,
          discount_label: rule.name,
          discount_percent: 0,
          discount_amount: priceDiscount,
          discount_id: rule.rule_id,
          item_product_id: item.product_id,
          is_free_shipping:
            rule.simple_free_shipping === '1' ||
            rule.simple_free_shipping === '2'
              ? true
              : false,
        });
        discountAmount += priceDiscount;
      } else {
        const discount =
          rule.simple_action === 'by_percent'
            ? (rule.discount_amount / 100) * item.price_incl_tax * item.qty
            : rule.discount_amount * item.qty;
        discountRows.push({
          quote_item_id: item.quote_item_id,
          discount_label: rule.name,
          discount_amount: discount,
          discount_percent:
            rule.simple_action === 'by_percent' ? rule.discount_amount : 0,
          discount_id: rule.rule_id,
          item_product_id: item.product_id,
          is_free_shipping:
            rule.simple_free_shipping === '1' ||
            rule.simple_free_shipping === '2'
              ? true
              : false,
        });
        discountAmount += discount;
      }
    }
    return { discountAmount, discountRows };
  };

  /**
   * It's used to check nested conditions present in
   * discount rule conditions/action-conditions
   * @param rule
   * @param subTotal
   * @param countryId
   * @param productSkus
   * @param productManufacturers
   * @returns
   */
  checkNestedConditions = (
    rule: SalesRule,
    subTotal: number,
    countryId: string,
    productSkus: string[],
    productManufacturers: string[],
  ) => {
    if ((rule.condition.conditions[0] as SalesRuleCondition)?.conditions) {
      // return found type logic
      const result = [];
      for (const con of rule.condition.conditions) {
        const checkRes = this.applyAggregationCheck(
          con as SalesRuleCondition,
          subTotal,
          countryId,
          productSkus,
          productManufacturers,
        );
        result.push(checkRes);
      }
      if (
        rule.condition.aggregator_type === 'all' &&
        result.every((o) => o === true)
      ) {
        if (rule.action_condition.conditions) {
          if (
            (rule.action_condition.conditions[0] as SalesRuleCondition)
              ?.conditions
          ) {
            if (
              (
                rule.action_condition.conditions[0] as SalesRuleCondition
              )?.conditions.every((o) =>
                this.applyAggregationCheck(
                  o as any,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                ),
              )
            ) {
              return true; // true case
            }
            return false; //false case
          } else {
            if (
              this.applyAggregationCheck(
                rule.action_condition as SalesRuleCondition,
                subTotal,
                countryId,
                productSkus,
                productManufacturers,
              )
            ) {
              return true; // true case
            }
            return false; // false case
          }
        }
        return true;
      }
      if (
        rule.condition.aggregator_type === 'any' &&
        result.some((o) => o === true)
      ) {
        if (rule.action_condition.conditions) {
          if (
            (rule.action_condition.conditions[0] as SalesRuleCondition)
              ?.conditions
          ) {
            if (
              (
                rule.action_condition.conditions[0] as SalesRuleCondition
              )?.conditions.some((o) =>
                this.applyAggregationCheck(
                  o as any,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                ),
              )
            ) {
              return true; // true case
            }
            return false;
          } else {
            if (
              this.applyAggregationCheck(
                rule.action_condition as SalesRuleCondition,
                subTotal,
                countryId,
                productSkus,
                productManufacturers,
              )
            ) {
              return true; // true case
            }
            return false; // false case
          }
        }
        return true; // true case
      }
    }
  };

  /**
   * It calculates the rewards points to be applied and
   * the discount amount in INR currency code
   * @param spentConfig
   * @param requestedRewardPoints
   * @param rewardId
   * @returns
   */
  deriveRewardsDiscounts(
    spentConfig: number,
    requestedRewardPoints: number,
    rewardId: number,
    discountObj: QuoteDiscountsObject,
  ) {
    const discountAmount = requestedRewardPoints / spentConfig;
    let discountRow;
    if (!discountObj) {
      discountRow = {
        discount_label: 'Reward points',
        discount_amount: discountAmount,
        discount_id: rewardId,
        is_free_shipping: false,
        reward_points: requestedRewardPoints,
      };
    } else {
      discountRow = {
        discount_label: discountObj.discount_label,
        discount_amount: discountAmount,
        discount_id: rewardId,
        is_free_shipping: discountObj.is_free_shipping,
        reward_points: requestedRewardPoints,
      };
    }

    return { discountAmount, discountRow };
  }

  calculateCouponRewardDiscount(
    quote: Quote,
    total_item_length: number,
    coupon_expired: boolean,
    couponDiscount?: DiscountValues,
    rewardDiscount?: DiscountValues,
    memberShipInfo?: any,
  ) {
    if (total_item_length === 0)
      return {
        coupon_discount: 0,
        reward_discount: 0,
        discount_rows: [],
        coupon_code: null,
      };
    const discounts = quote?.discount?.discounts;

    let discount_rows = discounts
      ? [...discounts.filter((obj) => obj.coupon_code || obj.reward_points)]
      : [];
    let coupon_code = couponDiscount?.coupon_code || quote.coupon_code;
    let coupon_discount =
      +discount_rows.find((obj) => obj.coupon_code === quote.coupon_code)
        ?.discount_amount || 0;
    let reward_discount =
      +discount_rows.find((obj) => obj.reward_points)?.discount_amount || 0;
    if (
      coupon_expired ||
      couponDiscount?.discount_type === DiscountType.REMOVE_COUPON_DISCOUNT
    ) {
      const singleCouponIndex = discount_rows.findIndex(
        (coupon) => coupon.coupon_code,
      );
      if (singleCouponIndex === -1)
        throw new BadRequestException('Coupon not applied');
      discount_rows.splice(singleCouponIndex, 1);
      coupon_discount = 0;
      coupon_code = null;
    }

    if (rewardDiscount?.discount_type === DiscountType.REWARD_DISCOUNT) {
      const rewardsConfigIndex = discount_rows.findIndex(
        (o) => o.reward_points,
      );
      if (rewardsConfigIndex !== -1) {
        discount_rows.splice(rewardsConfigIndex, 1);
      }
      if (rewardDiscount.discount_amount === 0) reward_discount = 0;
      if (rewardDiscount.discount_amount > 0) {
        if (memberShipInfo.is_active && memberShipInfo.spent_rate) {
          reward_discount =
            rewardDiscount.discountRows[0].reward_points /
            memberShipInfo.spent_rate;
          rewardDiscount.discountRows[0].discount_amount = reward_discount;
        } else {
          reward_discount = +rewardDiscount.discount_amount;
        }
        discount_rows = discount_rows.concat(rewardDiscount.discountRows);
      }
    }

    if (couponDiscount?.discount_type === DiscountType.COUPON_DISCOUNT) {
      const singleCouponIndex = discount_rows.findIndex(
        (coupon) => coupon.coupon_code,
      );
      coupon_discount = +couponDiscount.discount_amount;
      if (singleCouponIndex !== -1) {
        discount_rows[singleCouponIndex] = {
          ...couponDiscount.discountRows[0],
        };
      } else {
        discount_rows = discount_rows.concat(couponDiscount.discountRows);
      }
    }
    return {
      coupon_discount,
      reward_discount,
      discount_rows,
      coupon_code,
    };
  }

  getCountryAndRegion(
    shippingAddress: QuoteAddress,
    billingAddress: QuoteAddress,
  ) {
    return {
      country_id:
        shippingAddress?.customer_country_id ||
        billingAddress?.customer_country_id,
      region_id:
        shippingAddress?.customer_region_code ||
        billingAddress?.customer_region_code,
    };
  }

  calculateItemsDiscount(mrp: number, sellingPrice: number, qty: number) {
    if (!mrp && !sellingPrice && mrp < sellingPrice)
      return {
        precentage: 0,
        discount_amount: 0,
      };
    const mrp_amount = mrp * qty;
    const selling_amount = sellingPrice * qty;
    const percentage = ((mrp_amount - selling_amount) / mrp_amount) * 100;
    return {
      discount_percent: +percentage.toFixed(2),
      row_total_savings: mrp_amount - selling_amount,
    };
  }

  validateDate(fromDate?: string, toDate?: string) {
    if (!fromDate && !toDate) return true;
    if (toDate) {
      return (
        new Date(toDate).setHours(0, 0, 0, 0) >= new Date().setHours(0, 0, 0, 0)
      );
    }
    if (fromDate) {
      return (
        new Date(fromDate).setHours(0, 0, 0, 0) <=
        new Date().setHours(0, 0, 0, 0)
      );
    }
  }

  isMemberShipActive(customerMembershipDetails?: any) {
    return (
      customerMembershipDetails?.results.some(
        (o: any) => o.is_active && !o.is_expired,
      ) || false
    );
  }

  async isFreeShipping(
    freeshipping: boolean,
    isMemberShipActive: boolean,
    subTotal: number,
    isVirtualCart: boolean,
  ) {
    let memeberShipFreeShipping = false,
      min_shipping_amount = 0;
    if (isMemberShipActive) {
      const config = await CarriersConfig.findOne({});
      if (config?.['carriers']?.['freeshipping']?.['free_shipping_subtotal']) {
        if (
          subTotal >
          +config['carriers']['freeshipping']['free_shipping_subtotal']
        ) {
          min_shipping_amount =
            +config['carriers']['freeshipping']['free_shipping_subtotal'];
          memeberShipFreeShipping = true;
        }
      }
    }
    return {
      is_free_shipping:
        freeshipping || isVirtualCart || memeberShipFreeShipping,
      min_shipping_amount,
    };
  }

  computeTierPrice(
    tier_prices: any,
    productItem: any,
    item_parent_id: number,
    parentMappedPrice: ParentUnitPriceInterface,
  ): number {
    let productPrice = productItem?.price?.minimalPrice?.amount?.value ?? 0;
    let qty = productItem.qty;
    if (item_parent_id != 0) {
      if (item_parent_id in parentMappedPrice) {
        const isUnitPriceExit = parentMappedPrice[item_parent_id].find(
          (data) => data.unit_price === productPrice,
        );
        if (isUnitPriceExit) qty = isUnitPriceExit.qty;
      }
    }
    tier_prices?.forEach((obj: any) => {
      if (qty >= obj.qty) productPrice = +obj.value;
    });
    return productPrice;
  }

  computeGroupChildPrice(
    products: ProductData[],
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
  ) {
    for (const prod of products) {
      const productPrice = prod?.price?.minimalPrice?.amount?.value ?? 0;
      const parent_id = +skuwiseParentIdQty[prod.sku]?.parent_id || 0;
      const qty = skuwiseParentIdQty[prod.sku]?.qty || 0;
      if (parent_id !== 0 && prod?.tier_prices?.length) {
        if (parent_id in parentMappedPrice) {
          const indexExist = parentMappedPrice[parent_id].findIndex(
            (data: any) => data.unit_price === productPrice,
          );
          if (indexExist !== -1) {
            parentMappedPrice[parent_id][indexExist] = {
              unit_price: productPrice,
              qty: parentMappedPrice[parent_id][indexExist].qty + qty,
            };
          } else {
            parentMappedPrice[parent_id].push({
              unit_price: productPrice,
              qty: qty,
            });
          }
        } else {
          parentMappedPrice[parent_id] = [
            {
              unit_price: productPrice,
              qty: qty,
            },
          ];
        }
      }
    }
  }

  buildSkuwiseParentIdQty = (
    quoteItems: QuoteItem[],
    requestedCartItems: CartSkuQtyInterface[],
    action_type: string,
    requestedQtyAndIds?: CartIdQtyInterface[],
  ) => {
    const skuParentIdQty = {};
    for (const item of quoteItems) {
      const alreadyExit =
        requestedCartItems.length > 0
          ? _.find(requestedCartItems, {
              sku: item.sku,
            })
          : false;
      if (alreadyExit) {
        skuParentIdQty[item.sku] = {
          parent_id: alreadyExit.parent_id,
          qty: +item.qty + alreadyExit.quantity,
        };
      } else {
        skuParentIdQty[item.sku] = {
          parent_id: +item.parent_id,
          qty:
            action_type === CartAction.UPDATE_CART
              ? this.getQuntity(item, requestedQtyAndIds)
              : +item.qty,
        };
      }
    }
    if (requestedCartItems.length) {
      for (const data of requestedCartItems) {
        if (!skuParentIdQty.hasOwnProperty(data.sku)) {
          skuParentIdQty[data.sku] = {
            parent_id: data?.parent_id || 0,
            qty: +data.quantity,
          };
        }
      }
    }
    return skuParentIdQty;
  };

  getQuntity(item: QuoteItem, requestedQtyAndIds: CartIdQtyInterface[]) {
    if (requestedQtyAndIds?.length) {
      const idExist = _.find(requestedQtyAndIds, { id: item.quote_item_id });
      if (idExist) return +idExist.qty;
    }
    return +item.qty;
  }

  async updateReferralCode(referral_code: string, quote_item: QuoteItem) {
    if (referral_code) {
      if (quote_item?.extension_attribute) {
        const extensionAttribute = await QuoteItemExtensionAttribute.findOne({
          where: {
            extension_attribute_id:
              quote_item?.extension_attribute.extension_attribute_id,
            quote_item_id: quote_item.quote_item_id,
          },
        });
        extensionAttribute?.update({ referral_code });
      } else {
        await QuoteItemExtensionAttribute.create({
          quote_item_id: quote_item?.quote_item_id,
          referral_code,
        });
      }
    }
  }

  /**
   * Validate authorization header token
   * @param authHeader string
   * @returns
   */
  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }

  throwError(error: any) {
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException
    ) {
      throw error;
    } else {
      throw new InternalServerErrorException(error?.message);
    }
  }

  getFormattedProductsForCoupon(
    quoteItems: QuoteItem[],
    productDetails: ProductData[],
  ) {
    if (productDetails) {
      const productDetailsMap = productDetails.reduce((map, item) => {
        map[item.id] = item;
        return map;
      }, {});
      const formattedProducts = [];
      quoteItems.forEach((item) => {
        if (!item?.extension_attribute?.is_free_product) {
          let prodDetails = productDetailsMap[item.product_id];
          if (prodDetails) {
            const adminDiscount = isNaN(
              +item?.extension_attribute?.admin_discount_amount,
            )
              ? 0
              : +item?.extension_attribute?.admin_discount_amount;
            const manufacturer = isNaN(+item?.manufacturer)
              ? -1
              : +item?.manufacturer;
            formattedProducts.push({
              product_id: item.product_id,
              manufacturer: manufacturer,
              name: prodDetails?.name,
              sku: item.sku,
              qty: +item.qty,
              price: +item.price_incl_tax,
              category_ids: prodDetails?.categories?.map((o) => o?.id),
              discount_amount: item.discount_amount,
              applied_discount: adminDiscount,
              weight: item?.weight,
            });
          }
        }
      });

      return formattedProducts;
    }
  }

  accumulateDiscounts = (
    discountSource: {
      [key: string]: DiscountItem;
    },
    combinedDiscounts: { [key: string]: any },
  ) => {
    if (discountSource) {
      for (const [productId, discount] of Object.entries(discountSource)) {
        if (!combinedDiscounts[productId]) {
          combinedDiscounts[productId] = {
            product_id: productId,
            discount_amount: 0,
            discount_percent: 0,
          };
        }
        combinedDiscounts[productId].discount_amount +=
          discount.discount_amount ?? 0;
        combinedDiscounts[productId].discount_percent +=
          discount.discount_percentage ?? 0;
      }
    }
  };

  getCustomerCouponCode(quote: Quote) {
    const couponCode = quote?.discount?.discounts.find(
      (o) => o.coupon_code === quote.coupon_code,
    )?.coupon_code;
    return couponCode;
  }

  combinedCouponAndAdminDiscount(
    allItems: QuoteItem[],
    couponAndAutoDiscountMap,
    adminItemWiseDiscount,
  ) {
    const itemWiseDiscount: ItemDiscount = {};
    const existingItemWiseDiscount: ItemDiscount = {};
    for (const quoteItem of allItems) {
      const itemDiscountAmount =
        couponAndAutoDiscountMap[quoteItem.product_id]?.discount_amount ?? 0;
      const itemDiscountPercent =
        couponAndAutoDiscountMap[quoteItem.product_id]?.discount_percent ?? 0;
      const adminDiscountAmount =
        adminItemWiseDiscount[quoteItem.quote_item_id]?.admin_discount_amount ??
        0;
      const adminDiscountPercent =
        adminItemWiseDiscount[quoteItem.quote_item_id]
          ?.admin_discount_percent ?? 0;
      itemWiseDiscount[quoteItem.quote_item_id] = {
        discount_amount: itemDiscountAmount + adminDiscountAmount,
        discount_percent: itemDiscountPercent + adminDiscountPercent,
      };
      existingItemWiseDiscount[quoteItem.quote_item_id] = {
        discount_amount: quoteItem.discount_amount,
        discount_percent: quoteItem.discount_percent,
      };
    }

    return {
      combinedItemWiseDiscount: itemWiseDiscount,
      existingItemWiseDiscount,
    };
  }

  calculateCouponRewardDiscountV2(
    quote: Quote,
    total_item_length: number,
    rewardAndMembershipInfo: RewardAndMembershipResponse,
    appliedPoints: number,
    cartAction: CartAction,
  ) {
    if (total_item_length === 0 || appliedPoints === 0)
      return { reward_discount: 0, reward_discount_row: [] };
    const discounts = quote?.discount?.discounts ?? [];
    const discount_rows = discounts?.filter((o) => o.reward_points);
    const reward_discount =
      +discount_rows?.find((obj) => obj.reward_points)?.discount_amount || 0;
    if (cartAction === CartAction.APPLY_REWARD || reward_discount > 0) {
      const { max_applicable_points, max_discount_amount } =
        rewardAndMembershipInfo;

      if (max_applicable_points && max_discount_amount) {
        return {
          reward_discount: max_discount_amount,
          reward_discount_row: [
            {
              reward_points: max_applicable_points,
              discount_amount: max_discount_amount,
            },
          ],
        };
      }
    }
    return { reward_discount: 0, reward_discount_row: [] };
  }

  async isFreeShippingV2(
    isMemberShipActive: boolean,
    subTotal: number,
    isVirtualCart: boolean,
    // couponShippingStatus: boolean,
  ) {
    let memeberShipFreeShipping = false,
      min_shipping_amount = 0;
    if (isMemberShipActive) {
      const config = await CarriersConfig.findOne({});
      if (config?.['carriers']?.['freeshipping']?.['free_shipping_subtotal']) {
        min_shipping_amount =
          +config['carriers']['freeshipping']['free_shipping_subtotal'];
        if (
          subTotal >
          +config['carriers']['freeshipping']['free_shipping_subtotal']
        ) {
          min_shipping_amount =
            +config['carriers']['freeshipping']['free_shipping_subtotal'];
          memeberShipFreeShipping = true;
        }
      }
    }
    return {
      is_free_shipping: isVirtualCart || memeberShipFreeShipping,
      min_shipping_amount,
    };
  }

  async getAllItemPromotions(skus: string[]) {
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    const itemPromotions = await ItemPromotion.findAll({
      attributes: [
        'product_sku',
        'free_product_sku',
        'parent_id',
        'parent_sku',
        'buy_qty',
        'free_product_qty',
        'is_multiply',
        'promotion_id',
        'start_date',
        'end_date',
      ],
      where: {
        product_sku: { [Op.in]: skus },
        start_date: {
          [Op.lte]: currentDate,
        },
        end_date: {
          [Op.gte]: currentDate,
        },
        is_free_product_in_stock: true,
      },
    });

    return { itemPromotions };
  }

  // async computeFreeProducts(
  //   quote: Quote,
  //   quoteItems: QuoteItem[],
  //   productDetails: ProductData[],
  //   itemPromotions: ItemPromotion[],
  //   skuWiseErrors: any,
  //   grandTotal: number,
  //   countryId: string,
  // ) {
  //   // ITEM PROMOTION LOGIC
  //   for (const item of quoteItems) {
  //     const itemPromotion = itemPromotions?.find(
  //       (o) => o?.product_sku === item?.sku,
  //     );
  //     if (!itemPromotion) {
  //       if (item?.quoteItemPromotions?.length > 0) {
  //         for (const quoteItemPromotion of item.quoteItemPromotions) {
  //           await this.removeQuoteItemPromotion(quoteItemPromotion?.id);
  //         }
  //       }
  //       continue;
  //     }
  //     const productDetail = productDetails.find(
  //       (o) => o?.sku === itemPromotion?.free_product_sku,
  //     );
  //     if (!productDetail) continue;
  //     this.validateProductAvailability({
  //       product: productDetail,
  //       throwError: false,
  //       skuWiseErrors: skuWiseErrors,
  //       country_id: countryId,
  //       qty: +itemPromotion.free_product_qty,
  //     });

  //     const freeProductQty = await this.getFreeProductQty(
  //       item?.qty,
  //       itemPromotion,
  //     );

  //     const productObj = {
  //       buy_sku: itemPromotion.product_sku,
  //       buy_qty: itemPromotion.buy_qty,
  //       free_sku: itemPromotion.free_product_sku,
  //       free_qty: itemPromotion.free_product_qty,
  //       qty: freeProductQty,
  //       is_multiply: itemPromotion.is_multiply,
  //       name: productDetail.name,
  //       product_id: productDetail.id,
  //       parent_id: productDetail.parent_id,
  //       product_type: productDetail.type_id,
  //       price: productDetail.price.regularPrice.amount.value,
  //       special_price: productDetail.price.minimalPrice.amount.value,
  //     };

  //     if (item?.quoteItemPromotions?.length === 0 && freeProductQty !== 0) {
  //       await QuoteItemPromotion.create({
  //         quote_item_id: item?.quote_item_id,
  //         promotion_id: itemPromotion?.promotion_id,
  //         promotion_data: productObj,
  //       });
  //     } else {
  //       // TRY TO REMOVE
  //       if (item?.quoteItemPromotions) {
  //         for (const quoteItemPromotion of item?.quoteItemPromotions) {
  //           if (
  //             quoteItemPromotion?.promotion_id ===
  //               itemPromotion?.promotion_id &&
  //             (freeProductQty !== quoteItemPromotion?.promotion_data?.qty ||
  //               quoteItemPromotion?.promotion_data?.free_sku !==
  //                 itemPromotion?.free_product_sku)
  //           ) {
  //             if (freeProductQty === 0) {
  //               await this.removeQuoteItemPromotion(quoteItemPromotion?.id);
  //             } else {
  //               await QuoteItemPromotion.update(
  //                 {
  //                   // quote_item_id: item?.quote_item_id,
  //                   // promotion_id: itemPromotion?.promotion_id,
  //                   promotion_data: productObj,
  //                 },
  //                 { where: { id: quoteItemPromotion?.id } },
  //               );
  //             }
  //           }
  //         }
  //       }
  //     }
  //   }

  //   // AMOUNT PROMOTION LOGIC
  //   const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
  //   const currentDate = new Date(updatedTime);
  //   const amountPromotion = await AmountPromotion.findOne({
  //     where: {
  //       min_order_amount: {
  //         [Op.lte]: grandTotal,
  //       },
  //       start_date: {
  //         [Op.lte]: currentDate,
  //       },
  //       end_date: {
  //         [Op.gte]: currentDate,
  //       },
  //     },
  //     order: [['min_order_amount', 'DESC']],
  //   });

  //   if (amountPromotion) {
  //     const amountPromotionProductDetail = (
  //       await this.externalApiHelper.getProductDataFromSku([
  //         amountPromotion?.product_sku,
  //       ])
  //     )[0];

  //     productDetails.push(amountPromotionProductDetail);
  //     const productObj: AmountPromotionProductData = {
  //       free_sku: amountPromotion.product_sku,
  //       qty: amountPromotion.buy_qty,
  //       name: amountPromotionProductDetail.name,
  //       product_id: amountPromotionProductDetail.id,
  //       parent_id: amountPromotionProductDetail.parent_id,
  //       product_type: amountPromotionProductDetail.type_id,
  //       price: amountPromotionProductDetail.price.regularPrice.amount.value,
  //       special_price:
  //         amountPromotionProductDetail.price.minimalPrice.amount.value,
  //     };

  //     if (quote?.quoteAmountPromotions?.length === 0) {
  //       await this.createAmountPromotionItem(
  //         quote.quote_id,
  //         amountPromotion.amount_promotion_id,
  //         productObj,
  //       );
  //       return;
  //     } else {
  //       if (quote?.quoteAmountPromotions?.length > 0) {
  //         for (const quoteAmountPromotion of quote?.quoteAmountPromotions) {
  //           if (
  //             quoteAmountPromotion?.promotion_data?.free_sku !==
  //               amountPromotion?.product_sku ||
  //             quoteAmountPromotion?.promotion_data?.qty !==
  //               amountPromotion?.buy_qty
  //           ) {
  //             await this.removeAmountPromotionItem(quoteAmountPromotion.id);
  //             await this.createAmountPromotionItem(
  //               quote.quote_id,
  //               amountPromotion.amount_promotion_id,
  //               productObj,
  //             );
  //           }
  //         }
  //       }
  //     }
  //   } else if (!amountPromotion && quote?.quoteAmountPromotions?.length > 0) {
  //     for (const quoteAmountPromotion of quote?.quoteAmountPromotions) {
  //       await this.removeAmountPromotionItem(quoteAmountPromotion.id);
  //     }
  //   }
  // }

  async clearItemPromotions(item: QuoteItem) {
    if (item?.quoteItemPromotions?.length > 0) {
      for (const quoteItemPromotion of item.quoteItemPromotions) {
        await this.removeQuoteItemPromotion(quoteItemPromotion?.id);
      }
    }
  }

  createProductObject(
    itemPromotion: ItemPromotion,
    productDetail: ProductData,
    freeProductQty: number,
    productTaxes: any,
  ) {
    const appliedTaxes = productTaxes[productDetail?.tax_class_id] || [];
    const productPrice = productDetail?.price?.minimalPrice?.amount?.value;
    const { taxPercent, taxAmount } = this.cartMapper.deriveProductTax(
      appliedTaxes,
      productPrice,
    );
    return {
      buy_sku: itemPromotion.product_sku,
      buy_qty: itemPromotion.buy_qty,
      free_sku: itemPromotion.free_product_sku,
      free_qty: itemPromotion.free_product_qty,
      qty: freeProductQty,
      is_multiply: itemPromotion.is_multiply,
      name: productDetail.name,
      product_id: productDetail.id,
      parent_id: productDetail.parent_id,
      product_type: productDetail.type_id,
      price: productDetail.price.minimalPrice.amount.value,
      // special_price: productDetail.price.minimalPrice.amount.value,
      start_date: itemPromotion.start_date,
      end_date: itemPromotion.end_date,
      image: productDetail.image_url,
      tax_amount: taxAmount,
      tax_percent: taxPercent,
      weight: productDetail.weight,
      url_key: productDetail.url_key,
      row_total_incl_tax: productPrice * freeProductQty,
    };
  }

  async createItemPromotion(
    item: QuoteItem,
    itemPromotion: ItemPromotion,
    productObj: ItemPromotionProductData,
  ) {
    await QuoteItemPromotion.create({
      quote_item_id: item?.quote_item_id,
      promotion_id: itemPromotion?.promotion_id,
      sku: itemPromotion?.free_product_sku,
      qty: productObj.qty,
      meta_info: productObj,
    });
  }

  async updateItemPromotions(
    item: QuoteItem,
    itemPromotion: ItemPromotion,
    freeProductQty: number,
    productObj: any,
  ) {
    if (!item?.quoteItemPromotions) return;
    const existingItemPromotionsMap = new Set<string>();
    const removeItemPromotionIds = [];
    for (const quoteItemPromotion of item?.quoteItemPromotions) {
      if (
        existingItemPromotionsMap.has(
          quoteItemPromotion.quote_item_id.toString(),
        )
      ) {
        removeItemPromotionIds.push(quoteItemPromotion.id);
        continue;
      }
      existingItemPromotionsMap.add(
        quoteItemPromotion.quote_item_id.toString(),
      );
      if (
        quoteItemPromotion?.promotion_id !== itemPromotion?.promotion_id ||
        (freeProductQty === 0 && quoteItemPromotion?.meta_info?.qty !== 0)
      ) {
        removeItemPromotionIds.push(quoteItemPromotion?.id);
        // await this.removeQuoteItemPromotion(quoteItemPromotion?.id);
      } else if (
        quoteItemPromotion?.meta_info?.free_sku !==
          itemPromotion?.free_product_sku ||
        freeProductQty !== quoteItemPromotion?.meta_info?.qty
      ) {
        await QuoteItemPromotion.update(
          {
            meta_info: productObj,
            sku: itemPromotion?.free_product_sku,
            qty: productObj.qty,
          },
          { where: { id: quoteItemPromotion?.id } },
        );
      }
    }
    if (removeItemPromotionIds.length > 0) {
      await QuoteItemPromotion.destroy({
        where: { id: removeItemPromotionIds },
      });
    }
  }

  async removeAllAmountPromotions(quote: Quote) {
    const promotions = quote?.quoteAmountPromotions;
    if (promotions?.length) {
      const amountPromotionIds = promotions.map((p) => p.id);
      await QuoteAmountPromotion.destroy({
        where: { id: amountPromotionIds },
      });
    }
  }

  async handleAmountPromotionUpdate(
    quote: Quote,
    amountPromotion: AmountPromotion,
    productObj: AmountPromotionProductData,
  ) {
    // Base case: If there are no amount promotions in the quote, create a new one and return early.
    if (!quote?.quoteAmountPromotions?.length && productObj?.qty !== 0) {
      await this.createAmountPromotionItem(
        quote.quote_id,
        amountPromotion.amount_promotion_id,
        productObj,
      );
      return;
    }

    const seenSkus = new Set<string>();
    const removedPromotionIds = [];
    for (const quoteAmountPromotion of quote.quoteAmountPromotions) {
      const currentFreeSku = quoteAmountPromotion?.meta_info?.free_sku;
      const currentQty = quoteAmountPromotion?.meta_info?.qty;
      if (seenSkus.has(currentFreeSku)) {
        removedPromotionIds.push(quoteAmountPromotion.id);
        continue;
      }
      seenSkus.add(currentFreeSku);
      if (
        currentFreeSku !== amountPromotion?.product_sku ||
        currentQty !== productObj?.qty
      ) {
        removedPromotionIds.push(quoteAmountPromotion.id);
        if (productObj?.qty !== 0) {
          await this.createAmountPromotionItem(
            quote.quote_id,
            amountPromotion.amount_promotion_id,
            productObj,
          );
        }
      }
    }
    if (removedPromotionIds.length > 0) {
      await QuoteAmountPromotion.destroy({
        where: { id: removedPromotionIds },
      });
    }
  }

  async createAmountPromotionItem(
    quoteId: number,
    amountPromotionId: number,
    productObj: AmountPromotionProductData,
  ) {
    await QuoteAmountPromotion.create({
      quote_id: quoteId,
      promotion_id: amountPromotionId,
      meta_info: productObj,
      qty: productObj?.qty,
      sku: productObj?.free_sku,
    });
  }

  async removeAmountPromotionItem(quoteAmountPromotionId: number) {
    await QuoteAmountPromotion.destroy({
      where: { id: quoteAmountPromotionId },
    });
  }

  getFreeProductQty(itemQty: number, promotion: ItemPromotion) {
    const qty = promotion?.is_multiply
      ? Math.floor(Number(itemQty ?? 0) / +promotion.buy_qty) *
        promotion.free_product_qty
      : +promotion.free_product_qty;
    return qty ?? 0;
  }

  async removeQuoteItemPromotion(quoteItemPromotionId: number) {
    await QuoteItemPromotion.destroy({
      where: { id: quoteItemPromotionId },
    });
  }

  validateFreeProductAvailability(
    product: ProductData,
    freeProductsMap: Map<string, number>,
    qtyToAdd: number,
  ) {
    // let isFreeError = false;

    //+95 freeQty = {"AAA":95}; Max qty = 100;
    //+10 freeQty = {"AAA":105}; Max qty = 100
    let qty = freeProductsMap.get(product.sku);
    if (!product?.is_in_stock) {
      qty = 0;
      return qty;
      // isFreeError = true;
      // return isFreeError;
    }

    if (qty > product.max_sale_qty) {
      const updatedQty = qtyToAdd - (qty - product.max_sale_qty);
      if (updatedQty <= 0) {
        return 0;
      }
      return updatedQty;
    }
    return qtyToAdd;
  }

  /*
    1.remove extra added membership product
    2. input cart items
    3. Remove extra added membership product
  */
  async handleMultipleMemberShipAddon(
    quoteItems: QuoteItem[],
  ): Promise<QuoteItem[]> {
    try {
      const membershipItems = quoteItems.filter((item) =>
        MEMBERSHIP_SKUS.includes(item.sku),
      );

      // No membership SKU, return as is
      if (membershipItems.length === 0) return quoteItems;

      // Find the allowed membership item (prioritize "XXXXX00001-837")
      const allowedMembershipItem =
        membershipItems.find((item) => item.sku === 'XXXXX00001-837') ||
        membershipItems[0];

      // Collect IDs of items to delete (bulk delete)
      const itemIdToDelete = [];
      let foundAllowed = false;

      // Collect IDs of items to delete (ensure only one instance remains)
      for (const item of membershipItems) {
        if (item.sku === allowedMembershipItem.sku && !foundAllowed) {
          foundAllowed = true; // Keep the first allowed SKU
        } else {
          itemIdToDelete.push(item.quote_item_id); // Mark for deletion
        }
      }

      // Bulk delete in one query if there are items to remove
      if (itemIdToDelete.length) {
        await QuoteItem.destroy({ where: { quote_item_id: itemIdToDelete } });
      }

      // Return updated quoteItems, excluding removed ones
      return quoteItems.filter(
        (item) => !itemIdToDelete.includes(item.quote_item_id),
      );
    } catch (e) {
      // Return original quoteItems if any error occurs
      return quoteItems;
    }
  }
}
