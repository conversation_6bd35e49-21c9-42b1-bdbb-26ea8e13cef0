import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  CartAction,
  ProductStatuses,
  ProductTypes,
  SERVER_ADDED_HOURS,
  OutputResponseType,
  AddressTypes,
} from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import {
  CartSkuQtyInterface,
  CartIdQtyInterface,
  AddProductToCartRequest,
  updateCartItemsRqequest,
} from 'src/interface/add-product-to-cart-request';
import { CartMapper } from 'src/mapper/cart.mapper';
import { ExternalApiHelper } from './external-api.helper';
import { logger } from './service-logger';
import {
  ProductDataWithQuantity,
  ProductTaxesResponse,
} from 'src/interface/product';
import { ProductData } from 'src/interface/graphql-response';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { QuoteShippingRate } from 'src/database/entities/quote_shipping_rate';
import * as sequelize from 'sequelize';
import { DirectCondition, SalesRuleCondition } from 'src/interface/sales-rules';
import {
  AddressDto,
  BillingAddressDto,
  CartAddress,
} from 'src/interface/set-billing-address-on-cart';
import { QuoteDiscountsObject } from 'src/interface/discount';
import { DiscountType } from '../config/constants';
import { ShippingMethodService } from '../shipping-method/shipping-method.service';
import { AvailableShippingMethod } from '../interface/graphql-response';
import * as _ from 'lodash';
import {
  UpdateQuoteParam,
  UpdateQuoteParamV2,
} from '../interface/update-quote-param';
import { CartUtilityFunctions } from './cart-utility-function';
import {
  DiscountValues,
  ItemDiscount,
  RuleDiscount,
} from '../interface/quote-discount-object';
import {
  ParentUnitPriceInterface,
  SkuWiseParentIdAndQTy,
} from '../interface/parent-mapped-price';
import { Op } from 'sequelize';
import {
  QuoteItemExtensionAttribute,
  discountTypeEnum,
} from 'src/database/entities/quote_item_extension_attribute';
import { BuyNowProdRequest } from '../interface/buy-now-add-request';
import { PostcodeRegion } from 'src/database/entities/postcode_region';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { CouponProductType } from 'src/interface/coupon-products';
import { AppliedCouponResponse } from 'src/interface/coupon-response';
import { AppliedAutoDiscountResponse } from 'src/interface/auto-discount-response';
import { RewardAndMembershipResponse } from 'src/interface/reward-and-membership-response';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import { AmountPromotionProductData } from 'src/interface/promotion-product-data';
import { AmountPromotion } from 'src/database/entities/amount_promotion';

@Injectable()
export class CartHelperFunctions {
  constructor(
    private readonly cartMapper: CartMapper,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly shippingMethodService: ShippingMethodService,
    private readonly cartUtilityFun: CartUtilityFunctions,
  ) {}

  async updateCartItemsWithoutTxn(
    itemIds: number[],
    quoteItems: QuoteItem[],
    requestedQtyAndIds: CartIdQtyInterface[],
    productDetails: ProductData[],
    productTaxes: ProductTaxesResponse,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    country_id: string,
    skuWiseErrors: { [key: string]: any },
  ) {
    const updatedItems = [];
    for (const id of itemIds) {
      const actualItemQty = {};
      const quoteItem = _.find(quoteItems, { quote_item_id: id });
      if (!quoteItem)
        throw new NotFoundException(`Item with id ${id}  does'nt exist`);
      const parent_id = skuwiseParentIdQty[quoteItem.sku]?.parent_id || 0;
      const reqQtyId = _.find(requestedQtyAndIds, { id: id });
      const productItem = _.find(productDetails, { sku: quoteItem.sku });
      let productPrice = productItem?.price?.minimalPrice?.amount?.value ?? 0;
      const regularPrice = productItem?.price?.regularPrice?.amount?.value ?? 0;
      this.cartUtilityFun.validateProductAvailability({
        product: productItem,
        throwError: true,
        qty: +reqQtyId.qty,
        country_id: country_id,
        actualItemQty,
        skuWiseErrors,
      });
      const appliedTaxes = productTaxes[productItem?.tax_class_id] || [];
      if (productItem?.tier_prices?.length) {
        productPrice = this.cartUtilityFun.computeTierPrice(
          productItem?.tier_prices,
          productItem,
          parent_id,
          parentMappedPrice,
        );
      }
      if (quoteItem) {
        const { row_total_savings } =
          this.cartUtilityFun.calculateItemsDiscount(
            regularPrice,
            productPrice,
            +productItem.qty || 0,
          );
        const { appliedRules, taxPercent, taxAmount, appliedRuleTitles } =
          this.cartMapper.deriveProductTax(appliedTaxes, productPrice);
        const total_tax_amount = taxAmount * productItem.qty;
        const priceExclTax = productPrice - taxAmount;
        const rowTotalExclTax =
          productPrice * productItem.qty - total_tax_amount;
        const obj = {
          is_virtual:
            productItem.type_id === ProductTypes.VIRTUAL ? true : false,
          weight: productItem.weight,
          qty: productItem.qty,
          price: priceExclTax,
          base_price: priceExclTax,
          row_total: rowTotalExclTax,
          base_row_total: rowTotalExclTax,
          row_total_with_discount: productPrice * productItem.qty,
          row_weight: productItem.weight * productItem.qty,
          product_type: productItem.type_id,
          price_incl_tax: productPrice,
          base_price_incl_tax: productPrice,
          row_total_incl_tax: productPrice * productItem.qty,
          base_row_total_incl_tax: productPrice * productItem.qty,
          product_tax_class_id: productItem.tax_class_id,
          reward_points_earned: productItem.reward_point_product,
          applied_tax_ids: appliedRules,
          tax_percent: taxPercent,
          tax_amount: total_tax_amount,
          base_tax_amount: total_tax_amount,
          base_tax_before_discount: total_tax_amount,
          tax_before_discount: total_tax_amount,
          applied_tax_titles: appliedRuleTitles,
          manufacturer: productItem.manufacturer,
          categories: productItem.categories?.map((o) => o.name).toString(),
          is_cod: productItem.is_cod === '1' ? true : false,
          item_handling_fee: productItem?.dentalkart_custom_fee ?? 0,
          row_total_savings: row_total_savings,
          parent_id: parent_id,
        };

        await QuoteItem.update(obj, {
          where: { quote_item_id: quoteItem.quote_item_id },
        });
        updatedItems.push({
          ...obj,
          quote_item_id: quoteItem.quote_item_id,
          discount_amount: +quoteItem.discount_amount,
          discount_percent: +quoteItem.discount_percent,
          sku: quoteItem.sku,
          product_id: +quoteItem.product_id,
          extension_attribute: quoteItem?.extension_attribute,
          quoteItemPromotions: quoteItem?.quoteItemPromotions,
        });
      }
      productItem['qty'] = (actualItemQty as any)?.qty ?? productItem?.qty;
    }
    return updatedItems;
  }
  /**
   * It prepares QuoteItem object to store in database
   * @param quoteId id of quote to add items into
   * @param requestedCartItems CartSkuQtyInterface[]
   * @param productData ProductData[]
   * @returns
   */
  async buildQuoteItemsObj(
    quoteId: number,
    requestedCartItems: CartSkuQtyInterface[],
    productData: ProductData[],
    productTaxes: ProductTaxesResponse,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    country_id?: string,
  ) {
    const quoteItemsObj = [];

    for (const obj of productData) {
      this.cartUtilityFun.validateProductAvailability({
        product: obj,
        throwError: true,
        requestCartItems: requestedCartItems,
        country_id: country_id,
      });
      quoteItemsObj.push(
        this.cartMapper.builditemsObj(
          quoteId,
          obj as ProductDataWithQuantity,
          productTaxes,
          skuwiseParentIdQty,
          parentMappedPrice,
          this.cartUtilityFun.calculateItemsDiscount,
          this.cartUtilityFun.computeTierPrice,
        ),
      );
    }
    return quoteItemsObj;
  }

  /**
   * It derives the automatic discount rules that are
   * applicable to the cart
   * @param quote
   * @param allItems
   * @param subTotal
   * @param countryId
   * @param productSkus
   * @param productManufacturers
   * @param customerGroupId
   * @returns
   */
  deriveDiscounts = async (
    quote: Quote,
    allItems: QuoteItem[],
    subTotal: number,
    countryId: string,
    productSkus: string[],
    productManufacturers: string[],
    customerGroupId: number,
    autoDiscounts: any,
    magentoCustomerId: number,
  ) => {
    let quoteDiscountAmount = 0;
    const discountRows = [];
    try {
      let customerUsages = null;
      if (magentoCustomerId) {
        customerUsages = await this.externalApiHelper.getCustomerRulesUsages(
          magentoCustomerId,
        );
      }
      for (const obj of autoDiscounts) {
        if (obj.customer_group_ids.indexOf(customerGroupId) !== -1) {
          if (obj.uses_per_customer > 0 && quote.customer_id) {
            if (
              +customerUsages?.rules?.find((o) => +o.rule_id === obj.rule_id)
                ?.times_used >= obj.uses_per_customer
            ) {
              continue;
            }
          }
          // when uses_per_customer is 0 (coupon can be applied any no. of times)
          const conditionType = obj.condition.condition_type;
          if (
            conditionType ===
            'Magento\\SalesRule\\Model\\Rule\\Condition\\Combine'
          ) {
            if (
              (obj.condition.conditions?.[0] as SalesRuleCondition)?.conditions
            ) {
              // return found type logic
              const result = [];
              for (const con of obj.condition.conditions) {
                const checkRes = this.cartUtilityFun.applyAggregationCheck(
                  con as SalesRuleCondition,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                );
                result.push(checkRes);
              }
              if (
                obj.condition.aggregator_type === 'all' &&
                result.every((o) => o === true)
              ) {
                if (obj.action_condition.conditions) {
                  if (
                    (obj.action_condition.conditions[0] as SalesRuleCondition)
                      ?.conditions
                  ) {
                    if (
                      (
                        obj.action_condition.conditions[0] as SalesRuleCondition
                      )?.conditions.every((o) =>
                        this.cartUtilityFun.applyAggregationCheck(
                          o as any,
                          subTotal,
                          countryId,
                          productSkus,
                          productManufacturers,
                        ),
                      )
                    ) {
                      const discountData =
                        await this.cartUtilityFun.applyDiscount(
                          obj,
                          quote,
                          allItems,
                          subTotal,
                          countryId,
                        );
                      quoteDiscountAmount += discountData.discountAmount;
                      discountRows.push(...discountData.discountRows);
                      continue; // true case
                    }
                    continue; //false case
                  } else {
                    if (
                      this.cartUtilityFun.applyAggregationCheck(
                        obj.action_condition as SalesRuleCondition,
                        subTotal,
                        countryId,
                        productSkus,
                        productManufacturers,
                      )
                    ) {
                      const discountData =
                        await this.cartUtilityFun.applyDiscount(
                          obj,
                          quote,
                          allItems,
                          subTotal,
                          countryId,
                        );
                      quoteDiscountAmount += discountData.discountAmount;
                      discountRows.push(...discountData.discountRows);
                      continue; // true case
                    }
                    continue; // false case
                  }
                }
                const discountData = await this.cartUtilityFun.applyDiscount(
                  obj,
                  quote,
                  allItems,
                  subTotal,
                  countryId,
                );
                quoteDiscountAmount += discountData.discountAmount;
                discountRows.push(...discountData.discountRows);
                continue; // true case
              }
              if (
                obj.condition.aggregator_type === 'any' &&
                result.some((o) => o === true)
              ) {
                if (obj.action_condition.conditions) {
                  if (
                    (obj.action_condition.conditions[0] as SalesRuleCondition)
                      ?.conditions
                  ) {
                    if (
                      (
                        obj.action_condition.conditions[0] as SalesRuleCondition
                      )?.conditions.every((o) =>
                        this.cartUtilityFun.applyAggregationCheck(
                          o as any,
                          subTotal,
                          countryId,
                          productSkus,
                          productManufacturers,
                        ),
                      )
                    ) {
                      const discountData =
                        await this.cartUtilityFun.applyDiscount(
                          obj,
                          quote,
                          allItems,
                          subTotal,
                          countryId,
                        );
                      quoteDiscountAmount += discountData.discountAmount;
                      discountRows.push(...discountData.discountRows);
                      continue; // true case
                    }
                    continue; // false case
                  } else {
                    if (
                      this.cartUtilityFun.applyAggregationCheck(
                        obj.action_condition as SalesRuleCondition,
                        subTotal,
                        countryId,
                        productSkus,
                        productManufacturers,
                      )
                    ) {
                      const discountData =
                        await this.cartUtilityFun.applyDiscount(
                          obj,
                          quote,
                          allItems,
                          subTotal,
                          countryId,
                        );
                      quoteDiscountAmount += discountData.discountAmount;
                      discountRows.push(...discountData.discountRows);
                      continue; // true case
                    }
                    continue; // false case
                  }
                }
                const discountData = await this.cartUtilityFun.applyDiscount(
                  obj,
                  quote,
                  allItems,
                  subTotal,
                  countryId,
                );
                quoteDiscountAmount += discountData.discountAmount;
                discountRows.push(...discountData.discountRows);
                continue; // true case
              }
            }
            // direct combine check
            if (
              obj.condition.conditions?.length === 2 &&
              !(obj.condition.conditions?.[0] as SalesRuleCondition)?.conditions
            ) {
              if (
                this.cartUtilityFun.performCombinedCheck(
                  obj.condition as any,
                  obj.action_condition as SalesRuleCondition,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                )
              ) {
                // return apply discounts
                const discountData = await this.cartUtilityFun.applyDiscount(
                  obj,
                  quote,
                  allItems,
                  subTotal,
                  countryId,
                );
                quoteDiscountAmount += discountData.discountAmount;
                discountRows.push(...discountData.discountRows);
              }
            }
            if (obj.condition.conditions?.length === 1) {
              if (
                this.cartUtilityFun.performSingleCheck(
                  obj.condition.conditions?.[0] as DirectCondition,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                )
              ) {
                const discountData = await this.cartUtilityFun.applyDiscount(
                  obj,
                  quote,
                  allItems,
                  subTotal,
                  countryId,
                );
                quoteDiscountAmount += discountData.discountAmount;
                discountRows.push(...discountData.discountRows);
              }
            }
            if (!obj.condition.conditions) {
              if (
                this.cartUtilityFun.applyAggregationCheck(
                  obj.action_condition as SalesRuleCondition,
                  subTotal,
                  countryId,
                  productSkus,
                  productManufacturers,
                )
              ) {
                // return apply discounts
                const discountData = await this.cartUtilityFun.applyDiscount(
                  obj,
                  quote,
                  allItems,
                  subTotal,
                  countryId,
                );
                quoteDiscountAmount += discountData.discountAmount;
                discountRows.push(...discountData.discountRows);
              }
            }
          }
        }
        if (obj.stop_rules_processing) break;
      }
      const discountItemObj = this.computeItemsDiscountForSave(
        discountRows,
        {},
      );
      return { quoteDiscountAmount, discountRows, discountItemObj };
    } catch (error) {
      logger.error('Error in applyDiscount', error);
      return { quoteDiscountAmount: 0, discountRows: [], discountItemObj: {} };
    }
  };

  /**
   * It will validate the coupon code condition based on the
   * updated cart items
   * @param discountRuleId
   * @param subTotal
   * @param countryId
   * @param skus
   * @param manufacturers
   * @returns
   */
  async validateCouponForCart(
    subTotal: number,
    countryId: string,
    skus: string[],
    manufacturers: string[],
    customerGroupId: number,
    coupon_id: number,
    customer_id: number,
    magentoCustomerId: number,
    couponRuleData: any,
  ) {
    try {
      const couponData = await this.externalApiHelper.getCustomerCouponDetails(
        coupon_id,
      );
      if (Array.isArray(couponData) && !couponData.length) return false;
      if (couponData.customer_id) {
        if (+couponData.customer_id !== magentoCustomerId) return false;
        if (couponData.status === '0') return false;
      } else {
        if (couponData?.status === '0') {
          return false;
        }
      }
      if (!couponRuleData) return false;
      if (!couponRuleData.is_active) return false;
      if (couponRuleData.customer_group_ids.indexOf(customerGroupId) === -1)
        return false;
      if (
        !this.cartUtilityFun.validateDate(
          couponRuleData.from_date,
          couponRuleData.to_date,
        )
      )
        return false;

      if (
        !couponRuleData.condition.conditions &&
        !couponRuleData?.action_condition?.conditions
      )
        return true;
      const conditionType = couponRuleData.condition.condition_type;
      let isValid = false;
      if (
        conditionType === 'Magento\\SalesRule\\Model\\Rule\\Condition\\Combine'
      ) {
        if (
          (couponRuleData.condition.conditions?.[0] as SalesRuleCondition)
            ?.conditions
        ) {
          // return found type logic
          const result = [];
          for (const con of couponRuleData.condition.conditions) {
            const checkRes = this.cartUtilityFun.applyAggregationCheck(
              con as SalesRuleCondition,
              subTotal,
              countryId,
              skus,
              manufacturers,
            );
            result.push(checkRes);
          }
          if (
            couponRuleData.condition.aggregator_type === 'all' &&
            result.every((o) => o === true)
          ) {
            if (couponRuleData.action_condition.conditions) {
              if (
                (
                  couponRuleData.action_condition
                    .conditions[0] as SalesRuleCondition
                )?.conditions
              ) {
                if (
                  (
                    couponRuleData.action_condition
                      .conditions[0] as SalesRuleCondition
                  )?.conditions.every((o) =>
                    this.cartUtilityFun.applyAggregationCheck(
                      o as any,
                      subTotal,
                      countryId,
                      skus,
                      manufacturers,
                    ),
                  )
                ) {
                  return true;
                } else {
                  return false;
                }
              } else {
                if (
                  this.cartUtilityFun.applyAggregationCheck(
                    couponRuleData.action_condition as SalesRuleCondition,
                    subTotal,
                    countryId,
                    skus,
                    manufacturers,
                  )
                ) {
                  return true;
                } else {
                  return false;
                }
              }
            } else {
              return true;
            }
          }
          if (
            couponRuleData.condition.aggregator_type === 'any' &&
            result.some((o) => o === true)
          ) {
            if (couponRuleData.action_condition.conditions) {
              if (
                (
                  couponRuleData.action_condition
                    .conditions[0] as SalesRuleCondition
                )?.conditions
              ) {
                if (
                  (
                    couponRuleData.action_condition
                      .conditions[0] as SalesRuleCondition
                  )?.conditions.every((o) =>
                    this.cartUtilityFun.applyAggregationCheck(
                      o as any,
                      subTotal,
                      countryId,
                      skus,
                      manufacturers,
                    ),
                  )
                ) {
                  return true;
                } else {
                  return false;
                }
              } else {
                return this.cartUtilityFun.applyAggregationCheck(
                  couponRuleData.action_condition as SalesRuleCondition,
                  subTotal,
                  countryId,
                  skus,
                  manufacturers,
                );
              }
            } else {
              return true;
            }
          }
        }
        // direct combine check
        if (
          couponRuleData.condition.conditions?.length === 2 &&
          !(couponRuleData.condition.conditions?.[0] as SalesRuleCondition)
            ?.conditions
        ) {
          isValid = this.cartUtilityFun.performCombinedCheck(
            couponRuleData.condition as any,
            couponRuleData.action_condition as SalesRuleCondition,
            subTotal,
            countryId,
            skus,
            manufacturers,
          );
        }
        if (couponRuleData.condition.conditions?.length === 1) {
          isValid = this.cartUtilityFun.performSingleCheck(
            couponRuleData.condition.conditions?.[0] as DirectCondition,
            subTotal,
            countryId,
            skus,
            manufacturers,
          );
        }
        if (
          !couponRuleData.condition.conditions &&
          couponRuleData.action_condition.conditions
        ) {
          if (
            this.cartUtilityFun.applyAggregationCheck(
              couponRuleData.action_condition as SalesRuleCondition,
              subTotal,
              countryId,
              skus,
              manufacturers,
            )
          ) {
            isValid = true;
          }
        }
      }
      return isValid;
    } catch (e) {
      console.log('Error in coupon validations', JSON.stringify(e));
      return false;
    }
  }

  /**
   * It retrieves available billing methods, shipping methods & calculate
   * automatic discounts for the cart
   * @param allItems QuoteItem[]
   * @param quote Quote
   * @param billingAddress QuoteAddress
   * @param shippingAddress QuoteAddress
   * @param productDetails ProductData[]
   * @param t1
   * @returns
   */
  async updateQuoteAccordingToItemsWithoutTxn(args: UpdateQuoteParam) {
    const {
      customerGroupId,
      quote,
      billingAddress,
      shippingAddress,
      autoDiscount,
      rewardDiscount,
      throwError,
      magentoCustomerId,
      skuwiseParentIdQty,
      parentMappedPrice,
      productDetails,
      quote_filter,
      outputResponseType,
    } = args;
    let { allItems } = args;
    const cartSkus = [],
      cartManufacturer = [],
      itemWiseDiscount = {};
    const quoteFilter = quote_filter ? quote_filter : {};
    let { couponDiscount } = args;
    const customer_country_id =
      shippingAddress?.customer_country_id ||
      billingAddress?.customer_country_id;

    let quoteFigures =
      this.cartUtilityFun.calculateQuoteFiguresFromItems(allItems);

    allItems.forEach((o) => {
      cartSkus.push(o.sku);
      cartManufacturer.push(o.manufacturer);
    });

    let coupon_discount_item_rows = couponDiscount?.discountItems ?? [];
    const isVirtualCart = allItems.every((o) => o.is_virtual === true);
    let coupon_expired = false;
    let delivery_charges = 0;
    const memberShipInfo =
      +quote.customer_id && customer_country_id === 'IN'
        ? await this.getCustomerMemberShipInfo(+quote.customer_id)
        : { is_active: false, spent_rate: null };
    if (
      quote.coupon_code &&
      couponDiscount?.discount_type !== DiscountType.COUPON_DISCOUNT &&
      couponDiscount?.discount_type !== DiscountType.REMOVE_COUPON_DISCOUNT
    ) {
      const isCouponApplied = quote.discount.discounts.find(
        (o) => o.coupon_code === quote.coupon_code,
      );

      if (isCouponApplied) {
        const couponRuleData =
          await this.externalApiHelper.getRuleDetailsFromId(
            isCouponApplied.discount_id,
          );
        const isApplicable = await this.validateCouponForCart(
          quoteFigures.grandTotal,
          customer_country_id,
          cartSkus,
          cartManufacturer,
          customerGroupId,
          isCouponApplied.coupon_id,
          +quote.customer_id,
          magentoCustomerId,
          couponRuleData,
        );
        if (!isApplicable) {
          coupon_expired = true;
        } else if (couponRuleData && isApplicable) {
          const discountObj = await this.cartUtilityFun.applyDiscount(
            couponRuleData,
            quote,
            allItems,
            quoteFigures.grandTotal,
            shippingAddress?.customer_country_id ||
              billingAddress?.customer_country_id,
          );
          coupon_discount_item_rows = discountObj.discountRows;
          if (discountObj.discountAmount !== isCouponApplied.discount_amount) {
            couponDiscount = {
              discount_amount: discountObj.discountAmount,
              coupon_code: isCouponApplied.coupon_code,
              discountRows: [
                {
                  ...isCouponApplied,
                  discount_amount: discountObj.discountAmount,
                  is_free_shipping:
                    couponRuleData.simple_free_shipping === '2' ||
                    couponRuleData.simple_free_shipping === '1'
                      ? true
                      : false,
                },
              ],
              discount_type: DiscountType.COUPON_DISCOUNT,
            };
          }
        }
      }
    }
    const { reward_discount, coupon_discount, discount_rows, coupon_code } =
      this.cartUtilityFun.calculateCouponRewardDiscount(
        quote,
        allItems.length,
        coupon_expired,
        couponDiscount,
        rewardDiscount,
        memberShipInfo,
      );
    const { quoteDiscountAmount, discountRows, discountItemObj } =
      await this.deriveDiscounts(
        quote,
        allItems,
        quoteFigures.grandTotal,
        shippingAddress?.customer_country_id ||
          billingAddress?.customer_country_id,
        cartSkus,
        cartManufacturer,
        customerGroupId,
        autoDiscount,
        magentoCustomerId,
      );

    if (coupon_discount_item_rows?.length > 0) {
      this.computeItemsDiscountForSave(
        coupon_discount_item_rows,
        discountItemObj,
      );
    }

    const { admin_discount } = await this.updateQuoteItemDiscountWithoutTxn(
      discountItemObj,
      itemWiseDiscount,
    );

    const { isShipping, ruleDiscount } =
      this.computeStoreDiscounts(discountRows);

    const allDiscounts = [...discount_rows, ...ruleDiscount];

    const { is_free_shipping, min_shipping_amount } =
      await this.cartUtilityFun.isFreeShipping(
        isShipping,
        memberShipInfo.is_active,
        +quoteFigures.grandTotal,
        isVirtualCart,
      );

    //function to update all quote discount
    await this.updateQuoteDiscountWithoutTxn(quote, allDiscounts);

    /**
       update shipping details
       */
    let availableShippingMethods = [];
    let availablePaymentMethods = [],
      delivery_fee = null;
    if (shippingAddress) {
      try {
        availableShippingMethods = !isVirtualCart
          ? await this.shippingMethodService.getShippingMethods({
              weight: quoteFigures.totalWeightExcludingVirtualProduct,
              subTotal: quoteFigures.grandTotalExcludingVirtualProduct,
              countryCode: shippingAddress.customer_country_id,
            })
          : [];
        delivery_fee = availableShippingMethods?.[0]?.charges ?? null;
        if (availableShippingMethods.length && is_free_shipping) {
          availableShippingMethods[0].charges = 0;
        }
      } catch (e) {
        availableShippingMethods = [];
        if (throwError) throw new InternalServerErrorException(e?.message);
      }
      delivery_charges = availableShippingMethods?.[0]?.charges || 0;
      await this.updateQuoteShippingRateWithoutTxn(
        availableShippingMethods,
        shippingAddress,
      );
    }

    const total_with_discount =
      quoteFigures.grandTotal +
      quoteFigures.overWeightDeliveryCharges +
      delivery_charges -
      quoteDiscountAmount -
      reward_discount -
      coupon_discount -
      admin_discount;
    await Quote.update(
      {
        items_count: allItems.length,
        items_qty: quoteFigures.itemsQty,
        grand_total: total_with_discount,
        base_grand_total: total_with_discount,
        subtotal: quoteFigures.grandTotal,
        base_subtotal: quoteFigures.grandTotal,
        subtotal_with_discount: quoteFigures.subtotalWithDiscount,
        base_subtotal_with_discount: quoteFigures.subtotalWithDiscount,
        total_weight: quoteFigures.totalWeight,
        subtotal_including_tax: quoteFigures.subtotalWithDiscount,
        is_virtual: isVirtualCart,
        overweight_delivery_charges: quoteFigures.overWeightDeliveryCharges,
        rewards_discount: reward_discount,
        discount_amount: quoteDiscountAmount + coupon_discount,
        coupon_code: coupon_code,
        total_savings:
          quoteFigures.total_items_savings +
          coupon_discount +
          reward_discount +
          quoteDiscountAmount +
          admin_discount,
      },
      {
        where: { quote_id: quote.quote_id, ...quoteFilter },
        // transaction: t1,
      },
    );
    if (
      allItems.length > 0 &&
      (shippingAddress || billingAddress) &&
      outputResponseType !== OutputResponseType.REST
    ) {
      availablePaymentMethods =
        await this.externalApiHelper.getAvailablePaymentMethodV4({
          postcode:
            +shippingAddress?.customer_postcode ||
            +billingAddress?.customer_postcode,
          country_code:
            shippingAddress?.customer_country_id ||
            billingAddress?.customer_country_id,
          cart_data: {
            is_cod_on_cart: allItems.every((o) => o.is_cod),
            cart_weight: +(quoteFigures.totalWeight * 1000).toFixed(2),
            cart_amount: total_with_discount,
          },
          products: {
            children: [
              +allItems.find((item) => item.product_id !== undefined)
                ?.product_id || 0,
            ],
          },
        });
    }

    return {
      availableShippingMethods,
      availablePaymentMethods,
      is_member_ship_active: memberShipInfo.is_active,
      min_shipping_amount,
      delivery_charges: delivery_fee,
    };
  }

  async mergeToExistingCartItemsWihoutTxn(
    skusToModify: string[],
    quoteItems: QuoteItem[],
    requestCartItems: CartSkuQtyInterface[],
    productDetails: ProductData[],
    productTaxes: ProductTaxesResponse,
    throwError: boolean,
    skuWiseErrors: any,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    country_id?: string,
    is_buying_guide?: boolean,
  ) {
    const updatedItems = [];
    for (const prod of skusToModify) {
      const actualItemQty = {};
      const productItem = _.find(productDetails, { sku: prod });
      const quoteItem = _.find(quoteItems, (item) => {
        if (prod === item?.sku) {
          return item;
        }
      });
      let productPrice = productItem.price?.minimalPrice?.amount?.value ?? 0;
      const regularPrice = productItem.price?.regularPrice?.amount?.value ?? 0;
      const parent_id = skuwiseParentIdQty[prod]?.parent_id || 0;
      const referralCode = _.find(requestCartItems, { sku: prod });

      await this.cartUtilityFun.updateReferralCode(
        referralCode?.referral_code,
        quoteItem,
      );
      this.cartUtilityFun.validateProductAvailability({
        product: productItem,
        throwError: throwError,
        requestCartItems: requestCartItems,
        country_id: country_id,
        qtyAddon: +quoteItem.qty,
        skuWiseErrors: skuWiseErrors,
        actualItemQty,
      });
      const updatedProdQty = +productItem.qty;
      if (productItem?.tier_prices?.length) {
        productPrice = this.cartUtilityFun.computeTierPrice(
          productItem.tier_prices,
          productItem,
          parent_id,
          parentMappedPrice,
        );
      }
      const appliedTaxes = productTaxes[productItem?.tax_class_id] || [];

      if (quoteItem) {
        const { row_total_savings } =
          this.cartUtilityFun.calculateItemsDiscount(
            regularPrice,
            productPrice,
            +updatedProdQty || 0,
          );
        if (productItem?.buying_guide_qty) {
          await this.updateBuyingGuideQty(
            quoteItem,
            productItem?.buying_guide_qty,
          );
        }
        const { appliedRules, taxPercent, taxAmount, appliedRuleTitles } =
          this.cartMapper.deriveProductTax(appliedTaxes, productPrice);
        const total_tax_amount = taxAmount * updatedProdQty;
        const priceExclTax = productPrice - taxAmount;
        const rowTotalExclTax =
          productPrice * updatedProdQty - total_tax_amount;
        const obj = {
          is_virtual:
            productItem.type_id === ProductTypes.VIRTUAL ? true : false,
          weight: productItem.weight,
          qty: updatedProdQty,
          price: priceExclTax,
          base_price: priceExclTax,
          row_total: rowTotalExclTax,
          base_row_total: rowTotalExclTax,
          row_total_with_discount: productPrice * updatedProdQty,
          row_weight: productItem.weight * updatedProdQty,
          product_type: productItem.type_id,
          price_incl_tax: productPrice,
          base_price_incl_tax: productPrice,
          row_total_incl_tax: productPrice * updatedProdQty,
          base_row_total_incl_tax: productPrice * updatedProdQty,
          product_tax_class_id: productItem.tax_class_id,
          reward_points_earned: productItem.reward_point_product,
          applied_tax_ids: appliedRules,
          tax_percent: taxPercent,
          tax_amount: total_tax_amount,
          base_tax_amount: total_tax_amount,
          base_tax_before_discount: total_tax_amount,
          tax_before_discount: total_tax_amount,
          applied_tax_titles: appliedRuleTitles,
          manufacturer: productItem.manufacturer,
          categories: productItem.categories?.map((o) => o.name).toString(),
          is_cod: productItem.is_cod === '1' ? true : false,
          item_handling_fee: productItem?.dentalkart_custom_fee ?? 0,
          row_total_savings: row_total_savings,
          parent_id: parent_id,
        };
        await QuoteItem.update(obj, {
          where: { quote_item_id: quoteItem.quote_item_id },
        });
        updatedItems.push({
          ...obj,
          quote_item_id: quoteItem.quote_item_id,
          discount_amount: +quoteItem.discount_amount,
          discount_percent: +quoteItem.discount_percent,
          sku: quoteItem.sku,
          extension_attribute: quoteItem?.extension_attribute,
          product_id: +quoteItem.product_id,
          quoteItemPromotions: quoteItem?.quoteItemPromotions,
        });
      }
      productItem['qty'] = (actualItemQty as any)?.qty ?? productItem?.qty;
    }

    return updatedItems;
  }

  /**
   * It validates requested address country & region
   * @param cartAddress
   * @returns
   */
  async validateCountryAndRegion(cartAddress: CartAddress) {
    const countryDetails = await this.externalApiHelper.getCoutryDetails(
      cartAddress.address.country_code,
    );
    if (countryDetails.country === null) {
      throw new BadRequestException("Requested country isn't supported");
    }
    const region = countryDetails.country.available_regions.find((region) => {
      return region.code === cartAddress.address.region;
    });
    if (!region) throw new BadRequestException("Region isn't available");
    return region;
  }

  /**
   * It validates the product availability and
   * update the quantity of existing product with
   * requested quantity
   * @param skusToModify product skus to update quantity for
   * @param quoteItems QuoteItem[] - QuoteItem of existing items
   * @param requestCartItems CartSkuQtyInterface[] - request data
   * @param productDetails ProductData[] - product informations of skus to update
   * @returns
   */
  async updatePreviousCartItems(
    quoteItems: QuoteItem[],
    productDetails: ProductData[],
    t1: sequelize.Transaction,
    productTaxes: ProductTaxesResponse,
    skuWiseErrors: any,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    country_id?: string,
  ) {
    const updatedItems = [];
    for (const quoteItem of quoteItems) {
      const actualItemQty = {};
      const productItem = _.find(productDetails, { sku: quoteItem.sku });
      if (!productItem) {
        await QuoteItem.destroy({
          where: { quote_item_id: quoteItem.quote_item_id },
          transaction: t1,
        });
        continue;
      }
      if (productItem?.status === ProductStatuses.DISABLE) {
        await QuoteItem.destroy({
          where: { quote_item_id: quoteItem.quote_item_id },
          transaction: t1,
        });
        continue;
      }
      const isCod = productItem.is_cod === '1' ? true : false;
      let productPrice = productItem?.price?.minimalPrice?.amount?.value ?? 0;
      const regularPrice = productItem?.price?.regularPrice?.amount?.value ?? 0;
      const itemPrice = +quoteItem.price_incl_tax;
      const item_handling_fee = +quoteItem.item_handling_fee;
      const dentalkart_custom_fee = +productItem?.dentalkart_custom_fee || 0;
      const item_tax_percentage = +quoteItem?.tax_percent;
      this.cartUtilityFun.validateProductAvailability({
        product: productItem,
        throwError: false,
        skuWiseErrors: skuWiseErrors,
        country_id: country_id,
        qty: +quoteItem.qty,
        actualItemQty,
      });

      const appliedTaxes = productTaxes[productItem?.tax_class_id] || [];
      if (productItem?.tier_prices?.length) {
        productPrice = this.cartUtilityFun.computeTierPrice(
          productItem?.tier_prices,
          productItem,
          +quoteItem.parent_id,
          parentMappedPrice,
        );
      }
      const productQty = productItem.qty;
      const { appliedRules, taxPercent, taxAmount, appliedRuleTitles } =
        this.cartMapper.deriveProductTax(appliedTaxes, productPrice);
      const { row_total_savings } = this.cartUtilityFun.calculateItemsDiscount(
        regularPrice,
        productPrice,
        +productQty || 0,
      );
      const total_tax_amount = taxAmount * productItem.qty;
      const priceExclTax = productPrice - taxAmount;
      const rowTotalExclTax = productPrice * productItem.qty - total_tax_amount;
      const rewardPointOnProduct = productItem.reward_point_product;
      if (
        itemPrice !== productPrice ||
        item_handling_fee !== dentalkart_custom_fee ||
        item_tax_percentage !== taxPercent ||
        row_total_savings !== +quoteItem.row_total_savings ||
        isCod !== quoteItem.is_cod
      ) {
        const obj = {
          price: priceExclTax,
          base_price: priceExclTax,
          row_total: rowTotalExclTax,
          base_row_total: rowTotalExclTax,
          row_total_with_discount: productPrice * productQty,
          price_incl_tax: productPrice,
          base_price_incl_tax: productPrice,
          row_total_incl_tax: productPrice * productQty,
          base_row_total_incl_tax: productPrice * productQty,
          product_tax_class_id: productItem.tax_class_id,
          reward_points_earned: rewardPointOnProduct,
          applied_tax_ids: appliedRules,
          tax_percent: taxPercent,
          tax_amount: taxAmount * productItem.qty,
          base_tax_amount: taxAmount * productItem.qty,
          base_tax_before_discount: taxAmount * productItem.qty,
          tax_before_discount: taxAmount * productItem.qty,
          applied_tax_titles: appliedRuleTitles,
          is_cod: productItem.is_cod === '1' ? true : false,
          item_handling_fee: productItem?.dentalkart_custom_fee ?? 0,
          row_weight: productItem.weight * productQty,
          qty: productQty,
          weight: productItem.weight,
          row_total_savings: row_total_savings,
        };
        await QuoteItem.update(obj, {
          where: { quote_item_id: quoteItem.quote_item_id },
          transaction: t1,
        });
        updatedItems.push({
          ...obj,
          quote_item_id: quoteItem.quote_item_id,
          discount_amount: +quoteItem.discount_amount,
          discount_percent: +quoteItem.discount_percent,
          extension_attribute: quoteItem?.extension_attribute,
          sku: quoteItem.sku,
        });
      } else if (quoteItem) {
        updatedItems.push(quoteItem);
      }
      productItem['qty'] = (actualItemQty as any)?.qty ?? productItem?.qty;
    }
    return updatedItems;
  }
  async updatePreviousCartItemsWithoutTxn(
    quoteItems: QuoteItem[],
    productDetails: ProductData[],
    productTaxes: ProductTaxesResponse,
    skuWiseErrors: any,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    country_id: string,
  ) {
    if (productDetails?.length === 0) return quoteItems;
    const updatedItems = [];
    for (const quoteItem of quoteItems) {
      const productItem = _.find(productDetails, { sku: quoteItem.sku });
      if (!productItem) {
        await QuoteItem.destroy({
          where: { quote_item_id: quoteItem.quote_item_id },
        });
        continue;
      }
      if (
        productItem.status === ProductStatuses.DISABLE ||
        quoteItem?.extension_attribute?.is_free_product
      ) {
        await QuoteItem.destroy({
          where: { quote_item_id: quoteItem.quote_item_id },
        });
        continue;
      }

      // checking if discount quantity has changed
      // updating percentage discount
      if (
        quoteItem.extension_attribute &&
        quoteItem.extension_attribute?.discount_type ===
          discountTypeEnum.PERCENTAGE &&
        (quoteItem.extension_attribute?.admin_discount_percent / 100) *
          quoteItem.row_total_incl_tax !==
          Number(quoteItem.extension_attribute?.admin_discount_amount)
      ) {
        await QuoteItemExtensionAttribute.update(
          {
            admin_discount_amount:
              quoteItem.qty *
              quoteItem.price_incl_tax *
              (quoteItem.extension_attribute.admin_discount_percent / 100),
          },
          { where: { quote_item_id: quoteItem.quote_item_id } },
        );
        await quoteItem.extension_attribute.reload();
      }
      const isCod = productItem.is_cod === '1' ? true : false;
      let productPrice = productItem?.price?.minimalPrice?.amount?.value ?? 0;
      const regularPrice = productItem?.price?.regularPrice?.amount?.value ?? 0;
      const itemPrice = +quoteItem.price_incl_tax;
      const item_handling_fee = +quoteItem.item_handling_fee;
      const dentalkart_custom_fee = +productItem?.dentalkart_custom_fee || 0;
      const item_tax_percentage = +quoteItem?.tax_percent;
      this.cartUtilityFun.validateProductAvailability({
        product: productItem,
        throwError: false,
        skuWiseErrors: skuWiseErrors,
        country_id: country_id,
        qty: +quoteItem.qty,
      });
      const appliedTaxes = productTaxes[productItem?.tax_class_id] || [];
      if (productItem?.tier_prices?.length) {
        productPrice = this.cartUtilityFun.computeTierPrice(
          productItem?.tier_prices,
          productItem,
          +quoteItem.parent_id,
          parentMappedPrice,
        );
      }
      const productQty = productItem.qty;
      const { appliedRules, taxPercent, taxAmount, appliedRuleTitles } =
        this.cartMapper.deriveProductTax(appliedTaxes, productPrice);
      const total_tax_amount = taxAmount * productItem.qty;
      const priceExclTax = productPrice - taxAmount;
      const rowTotalExclTax = productPrice * productItem.qty - total_tax_amount;
      const { row_total_savings } = this.cartUtilityFun.calculateItemsDiscount(
        regularPrice,
        productPrice,
        +productQty || 0,
      );
      const rewardPointOnProduct = productItem.reward_point_product;
      if (
        itemPrice !== +productPrice.toFixed(2) ||
        item_handling_fee !== +dentalkart_custom_fee.toFixed(2) ||
        item_tax_percentage !== +taxPercent.toFixed(2) ||
        +quoteItem.row_total_savings !== +row_total_savings.toFixed(2) ||
        +quoteItem.tax_amount != +total_tax_amount.toFixed(2) ||
        isCod !== quoteItem.is_cod
      ) {
        const obj = {
          price: priceExclTax,
          base_price: priceExclTax,
          row_total: rowTotalExclTax,
          base_row_total: rowTotalExclTax,
          row_total_with_discount: productPrice * productQty,
          price_incl_tax: productPrice,
          base_price_incl_tax: productPrice,
          row_total_incl_tax: productPrice * productQty,
          base_row_total_incl_tax: productPrice * productQty,
          product_tax_class_id: productItem.tax_class_id,
          reward_points_earned: rewardPointOnProduct,
          applied_tax_ids: appliedRules,
          tax_percent: taxPercent,
          tax_amount: total_tax_amount,
          base_tax_amount: total_tax_amount,
          base_tax_before_discount: total_tax_amount,
          tax_before_discount: total_tax_amount,
          applied_tax_titles: appliedRuleTitles,
          is_cod: productItem.is_cod === '1' ? true : false,
          item_handling_fee: productItem?.dentalkart_custom_fee ?? 0,
          row_weight: productItem.weight * productQty,
          qty: productQty,
          weight: productItem.weight,
          row_total_savings: row_total_savings,
        };

        await QuoteItem.update(obj, {
          where: { quote_item_id: quoteItem.quote_item_id },
        });
        updatedItems.push({
          ...obj,
          quote_item_id: quoteItem.quote_item_id,
          discount_amount: +quoteItem.discount_amount,
          discount_percent: +quoteItem.discount_percent,
          extension_attribute: quoteItem?.extension_attribute,
          sku: quoteItem.sku,
          product_id: +quoteItem.product_id,
          manufacturer: quoteItem.manufacturer,
          categories: quoteItem.categories,
          quoteItemPromotions: quoteItem?.quoteItemPromotions,
        });
      } else if (quoteItem) {
        updatedItems.push(quoteItem);
      }
      // productItem['qty'] = (actualItemQty as any)?.qty ?? productItem?.qty;
    }
    return updatedItems;
  }

  async updateQuoteDiscountWithoutTxn(
    quote: Quote,
    discountRows: QuoteDiscountsObject[],
  ) {
    const quoteDiscountExists = await QuoteDiscount.findOne({
      where: { quote_id: quote.quote_id },
    });
    if (quoteDiscountExists) {
      await QuoteDiscount.update(
        { discounts: discountRows },
        {
          where: { quote_discount_id: quoteDiscountExists.quote_discount_id },
        },
      );
    } else
      await QuoteDiscount.create({
        quote_id: quote.quote_id,
        discounts: discountRows,
      });
  }

  async updateQuoteShippingRate(
    availableShippingMethods: AvailableShippingMethod[],
    shippingAddress: QuoteAddress,
    t1: any,
  ) {
    const shippingRateExists = await QuoteShippingRate.findOne({
      where: {
        quote_address_id: shippingAddress.quote_address_id,
      },
    });

    if (!availableShippingMethods.length && shippingRateExists) {
      await QuoteShippingRate.destroy({
        where: {
          quote_shipping_rate_id: shippingRateExists.quote_shipping_rate_id,
        },
        force: true,
        transaction: t1,
      });
    }
    if (availableShippingMethods.length) {
      if (shippingRateExists) {
        await QuoteShippingRate.update(
          {
            price: availableShippingMethods[0]?.charges || 0,
          },
          {
            where: {
              quote_shipping_rate_id: shippingRateExists.quote_shipping_rate_id,
            },
            transaction: t1,
          },
        );
      } else {
        const shippingRateObj = this.cartMapper.buildQuoteShippingRateObj(
          availableShippingMethods[0],
          shippingAddress.quote_address_id,
        );
        await QuoteShippingRate.create(
          {
            ...shippingRateObj,
            quote_address_id: shippingAddress.quote_address_id,
          },
          { transaction: t1 },
        );
      }
    }
  }

  async updateQuoteShippingRateWithoutTxn(
    availableShippingMethods: AvailableShippingMethod[],
    shippingAddress: QuoteAddress,
  ) {
    const shippingRateExists = await QuoteShippingRate.findOne({
      where: {
        quote_address_id: shippingAddress.quote_address_id,
      },
    });
    if (!availableShippingMethods.length && shippingRateExists) {
      await QuoteShippingRate.destroy({
        where: {
          quote_shipping_rate_id: shippingRateExists.quote_shipping_rate_id,
        },
        force: true,
      });
    }
    if (availableShippingMethods.length) {
      if (shippingRateExists) {
        await QuoteShippingRate.update(
          {
            price: availableShippingMethods[0]?.charges || 0,
          },
          {
            where: {
              quote_shipping_rate_id: shippingRateExists.quote_shipping_rate_id,
            },
          },
        );
      } else {
        const shippingRateObj = this.cartMapper.buildQuoteShippingRateObj(
          availableShippingMethods[0],
          shippingAddress.quote_address_id,
        );
        await QuoteShippingRate.create({
          ...shippingRateObj,
          quote_address_id: shippingAddress.quote_address_id,
        });
      }
    }
  }

  async fetchApiData(
    skus: string[],
    country_id: string,
    region_id: string,
    masked_id?: string,
  ) {
    try {
      if (skus.length && country_id && region_id) {
        return Promise.all([
          this.externalApiHelper.getProductDataFromSku(skus),
          this.externalApiHelper.getAutomaticDiscounts(masked_id),
          this.externalApiHelper.getTaxRatesForCart(country_id, region_id),
        ]);
      } else if (skus.length) {
        return Promise.all([
          this.externalApiHelper.getProductDataFromSku(skus),
          this.externalApiHelper.getAutomaticDiscounts(masked_id),
          {},
        ]);
      }
      return [];
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  async getCustomerMemberShipInfo(customer_id: number) {
    try {
      let spent_rate = null;
      const info = await this.externalApiHelper.getCustomerMemberShipDetails(
        customer_id,
      );
      const active = this.cartUtilityFun.isMemberShipActive(info);
      if (active) {
        const spentConfig = await this.externalApiHelper.getMemberShipConfig();
        spent_rate =
          spentConfig.reward_rate_point / spentConfig.reward_rate_amount;
      }
      return {
        is_active: active,
        spent_rate: spent_rate,
      };
    } catch (e) {
      return {
        is_active: false,
        spent_rate: null,
      };
    }
  }

  async updateQuoteItemDiscountWithoutTxn(
    discountItem: ItemDiscount,
    item: ItemDiscount,
  ) {
    let admin_discount = 0;
    for (const key in item) {
      if (key in discountItem) {
        if (discountItem[key].discount_amount != item[key].discount_amount) {
          await QuoteItem.update(
            {
              discount_amount:
                discountItem[key].discount_amount +
                  (item[key]?.admin_discount_amount ?? 0) || 0,
              discount_percent:
                discountItem[key].discount_percent +
                  (item[key]?.admin_discount_percent ?? 0) || 0,
            },
            {
              where: { quote_item_id: key },
            },
          );
        }
      } else if (
        item[key].discount_amount > 0 &&
        !item[key].admin_discount_amount
      ) {
        await QuoteItem.update(
          {
            discount_amount: 0,
            discount_percent: 0,
          },
          {
            where: { quote_item_id: key },
          },
        );
      } else if (
        item[key].admin_discount_amount !== undefined &&
        item[key].admin_discount_amount >= 0
      ) {
        admin_discount += item[key].admin_discount_amount;
        await QuoteItem.update(
          {
            discount_amount: item[key]?.admin_discount_amount ?? 0,
            discount_percent: item[key]?.admin_discount_percent ?? 0,
          },
          {
            where: { quote_item_id: key },
          },
        );
      }
    }

    return { admin_discount };
  }

  computeStoreDiscounts(discountRows: RuleDiscount[]) {
    let isShipping = false;
    const discountIdObj = {},
      ruleDiscount = [];
    if (discountRows.length === 0) return { isShipping, ruleDiscount };
    discountRows.forEach((discount: any) => {
      if (discount.is_free_shipping && !isShipping) {
        isShipping = true;
      }
      if (discount.discount_id in discountIdObj) {
        discountIdObj[discount.discount_id] = {
          ...discountIdObj[discount.discount_id],
          discount_amount:
            discountIdObj[discount.discount_id].discount_amount +
            discount.discount_amount,
        };
      } else {
        discountIdObj[discount.discount_id] = {
          is_free_shipping: discount.is_free_shipping,
          discount_label: discount.discount_label,
          discount_amount: discount.discount_amount,
          discount_id: discount.discount_id,
          item_product_id: discount.item_product_id,
        };
      }
    });
    return {
      isShipping,
      ruleDiscount: Object.values(discountIdObj),
    };
  }

  computeItemsDiscountForSave(
    discountRows: RuleDiscount[],
    discountItemObj: ItemDiscount,
  ) {
    if (discountRows.length > 0) {
      for (const discountObj of discountRows) {
        if (discountObj.quote_item_id) {
          if (discountObj.quote_item_id in discountItemObj) {
            discountItemObj[discountObj.quote_item_id] = {
              discount_amount:
                discountItemObj[discountObj.quote_item_id].discount_amount +
                discountObj.discount_amount,
              discount_percent:
                discountObj?.discount_percent > 0
                  ? discountItemObj[discountObj.quote_item_id]
                      .discount_percent + discountObj.discount_percent
                  : 0,
            };
          } else {
            discountItemObj[discountObj.quote_item_id] = {
              discount_amount: discountObj.discount_amount,
              discount_percent: discountObj?.discount_percent || 0,
            };
          }
        }
      }
    }
    return discountItemObj;
  }

  // async updateIfFreeProductAdded(remaining_item_details, requestedQtyAndSku) {
  //   console.log(requestedQtyAndSku, 'REQ QTY AND SKIU');
  //   if (remaining_item_details) {
  //     const skuMap = new Map(
  //       requestedQtyAndSku.map((item) => [item.sku, item]),
  //     );
  //     console.log(skuMap, 'SKU MAP');
  //     return remaining_item_details?.reduce((result, item) => {
  //       const a1Item: any = skuMap.get(item.sku);
  //       if (a1Item && a1Item?.is_free_product) {
  //         if (item?.is_in_stock) {
  //           item.reward_point_product = 0;
  //           result.push({
  //             ...item,
  //             is_free_product: a1Item?.is_free_product,
  //           });
  //         }
  //       } else {
  //         result.push(item);
  //       }
  //       return result;
  //     }, []);
  //   }
  // }

  async getRegionAndRegionCode(cartAddress: CartAddress) {
    try {
      if (cartAddress.address.country_code !== 'IN') return;
      if (!cartAddress.address.postcode) return;
      if (
        !cartAddress.address.region ||
        !cartAddress.address.region_code ||
        !cartAddress.address.region_id
      ) {
        const regionData = await PostcodeRegion.findOne({
          where: {
            postcode: cartAddress.address.postcode,
          },
        });

        if (
          regionData?.region &&
          regionData?.region_code &&
          regionData?.region_id
        ) {
          cartAddress.address.region = regionData.region;
          cartAddress.address.region_code = regionData.region_code;
          cartAddress.address.region_id = regionData.region_id;
        }
      }
    } catch (e) {
      logger.error(
        JSON.stringify({
          getRegionAndRegionCodeLog: {
            shipping_address: cartAddress,
            info: 'error in getRegionAndRegionCode',
            error: e?.message,
          },
        }),
      );
    }
  }

  // async getUpdatedProductQtys(
  //   quoteItems: QuoteItem[],
  //   actionType: CartAction,
  //   incomingItems?: any,
  // ) {
  //   const skusExcludingFreeProducts: string[] = [];
  //   const updatedProductQtys = [];
  //   const updatedProductQtysMap: { [sku: string]: number } = {};
  //   const processedSkus = new Set();

  //   if (actionType === CartAction.ADD_TO_CART) {
  //     for (const incomingItem of incomingItems) {
  //       const item = quoteItems.find((o) => o.sku === incomingItem.data.sku);
  //       if (!item?.extension_attribute?.is_free_product) {
  //         const qty = item
  //           ? Number(item.qty) + Number(incomingItem.data.quantity)
  //           : Number(incomingItem.data.quantity);
  //         updatedProductQtys.push({ sku: incomingItem.data.sku, qty });
  //         updatedProductQtysMap[incomingItem.data.sku] = qty;
  //         processedSkus.add(incomingItem.data.sku);
  //         skusExcludingFreeProducts.push(incomingItem.data.sku);
  //       }
  //     }
  //   } else if (
  //     actionType === CartAction.UPDATE_CART ||
  //     actionType === CartAction.SET_SHIPPING
  //   ) {
  //     for (const incomingItem of incomingItems) {
  //       const item = quoteItems.find(
  //         (o) => o.quote_item_id === incomingItem.id,
  //       );
  //       if (!item?.extension_attribute?.is_free_product) {
  //         const qty = incomingItem.qty;
  //         updatedProductQtys.push({ sku: item.sku, qty });
  //         updatedProductQtysMap[item.sku] = qty;
  //         processedSkus.add(item.sku);
  //         skusExcludingFreeProducts.push(item.sku);
  //       }
  //     }
  //   } else if (actionType === CartAction.REMOVE_FROM_CART) {
  //     processedSkus.add(incomingItems.sku);
  //     updatedProductQtysMap[incomingItems.sku] = 0;
  //     skusExcludingFreeProducts.push(incomingItems.sku);
  //   } else if (actionType === CartAction.MERGE_CART) {
  //     for (const incomingItem of incomingItems) {
  //       const item = quoteItems.find((o) => o.sku === incomingItem.sku);
  //       if (!item?.extension_attribute?.is_free_product) {
  //         const qty = item
  //           ? Number(item.qty) + Number(incomingItem.quantity)
  //           : Number(incomingItem.quantity);
  //         updatedProductQtys.push({ sku: incomingItem.sku, qty });
  //         updatedProductQtysMap[incomingItem.sku] = qty;
  //         processedSkus.add(incomingItem.sku);
  //         skusExcludingFreeProducts.push(incomingItem.sku);
  //       }
  //     }
  //   }

  //  // Adding items that are not iterated above
  //   for (const item of quoteItems) {
  //     if (
  //       !processedSkus.has(item.sku) &&
  //       !item?.extension_attribute?.is_free_product
  //     ) {
  //       const qty = Number(item.qty);
  //       updatedProductQtys.push({ sku: item.sku, qty });
  //       updatedProductQtysMap[item.sku] = qty;
  //       skusExcludingFreeProducts.push(item.sku);
  //     }
  //   }

  //   return {
  //     updatedProductQtys,
  //     skusExcludingFreeProducts,
  //     updatedProductQtysMap,
  //   };
  // }

  /**
   * It retrieves available billing methods, shipping methods & calculate
   * automatic discounts for the cart
   * @param allItems QuoteItem[]
   * @param quote Quote
   * @param billingAddress QuoteAddress
   * @param shippingAddress QuoteAddress
   * @param productDetails ProductData[]
   * @param t1
   * @returns
   */
  async updateQuoteAccordingToItemsWithoutTxnV2(args: UpdateQuoteParamV2) {
    const {
      customerGroupId,
      quote,
      billingAddress,
      shippingAddress,
      rewardDiscount,
      throwError,
      skuwiseParentIdQty,
      parentMappedPrice,
      productDetails,
      quote_filter,
      couponCode,
      countryId,
      regionId,
      customerId,
      cartAction,
      outputResponseType,
      appliedPoints,
      itemPromotions,
      skuWiseErrors,
      productTaxes,
    } = args;
    let { allItems } = args;
    const quoteFilter = quote_filter ? quote_filter : {};
    const customer_country_id =
      shippingAddress?.customer_country_id ||
      billingAddress?.customer_country_id;
    let {
      quoteFigures,
      reward_discount_row,
      availableShippingMethods,
      isVirtualCart,
      appliedAutoDiscount,
      formattedProductsForCoupon,
      min_shipping_amount,
      is_member_ship_active,
    } = await this.getAllQuoteCharges(
      allItems,
      quote,
      appliedPoints,
      countryId,
      shippingAddress,
      throwError,
      productDetails,
      customerId,
      customerGroupId,
      cartAction,
    );
    const {
      totalCouponDiscount: totalForCouponAndAutoDiscount,
      appliedCouponRow,
      appliedCouponCode,
      appliedRuleIds,
      couponShippingStatus,
      couponAndAutoDiscountMap,
    } = await this.applicableCouponDiscount(
      allItems,
      quote.discount,
      couponCode,
      customerId,
      customerGroupId,
      countryId,
      cartAction,
      appliedAutoDiscount,
      formattedProductsForCoupon,
      quoteFigures,
      shippingAddress,
    );

    let {
      admin_discount,
      delivery_charges,
      reward_discount,
      adminItemWiseDiscount,
    } = quoteFigures;
    const { combinedItemWiseDiscount, existingItemWiseDiscount } =
      this.cartUtilityFun.combinedCouponAndAdminDiscount(
        allItems,
        couponAndAutoDiscountMap,
        adminItemWiseDiscount,
      );
    const deliveryFee = delivery_charges;
    // If coupon gives free shipping and delivery charges applied previously
    if (couponShippingStatus && delivery_charges) {
      if (availableShippingMethods.length) {
        availableShippingMethods[0].charges = 0;
      }
      delivery_charges = 0;
    }

    await this.updateQuoteItemDiscountWithoutTxnV2(
      combinedItemWiseDiscount,
      existingItemWiseDiscount,
    );
    const allDiscounts = [...reward_discount_row, ...appliedCouponRow];

    //function to update all quote discount
    await this.updateQuoteDiscountWithoutTxn(quote, allDiscounts);

    // function to update shipping charges
    if (shippingAddress) {
      await this.updateQuoteShippingRateWithoutTxn(
        availableShippingMethods,
        shippingAddress,
      );
    }

    const totalForRewardCheck =
      quoteFigures.grandTotal -
      totalForCouponAndAutoDiscount -
      admin_discount -
      reward_discount;

    if (cartAction === CartAction.APPLY_REWARD && totalForRewardCheck <= 0) {
      throw new BadRequestException('Cannot apply reward point.');
    }

    const total_with_discount =
      quoteFigures.grandTotal +
      quoteFigures.overWeightDeliveryCharges +
      delivery_charges -
      totalForCouponAndAutoDiscount -
      reward_discount -
      admin_discount;

    await Quote.update(
      {
        items_count: allItems.length,
        items_qty: quoteFigures.itemsQty,
        grand_total: total_with_discount,
        base_grand_total: total_with_discount,
        subtotal: quoteFigures.grandTotal,
        applied_rule_ids: appliedRuleIds,
        base_subtotal: quoteFigures.grandTotal,
        subtotal_with_discount: quoteFigures.subtotalWithDiscount,
        base_subtotal_with_discount: quoteFigures.subtotalWithDiscount,
        total_weight: quoteFigures.totalWeight,
        subtotal_including_tax: quoteFigures.subtotalWithDiscount,
        is_virtual: isVirtualCart,
        overweight_delivery_charges: quoteFigures.overWeightDeliveryCharges,
        rewards_discount: reward_discount,
        discount_amount: totalForCouponAndAutoDiscount + admin_discount,
        coupon_code: appliedCouponCode,
        is_active_membership: is_member_ship_active,
        total_savings:
          quoteFigures.total_items_savings +
          totalForCouponAndAutoDiscount +
          reward_discount +
          admin_discount,
      },
      {
        where: { quote_id: quote.quote_id, ...quoteFilter },
        // transaction: t1,
      },
    );
    let availablePaymentMethods = [];
    if (
      allItems.length > 0 &&
      (shippingAddress || billingAddress) &&
      outputResponseType !== OutputResponseType.REST
    ) {
      availablePaymentMethods =
        await this.externalApiHelper.getAvailablePaymentMethodV4({
          postcode:
            +shippingAddress?.customer_postcode ||
            +billingAddress?.customer_postcode,
          country_code:
            shippingAddress?.customer_country_id ||
            billingAddress?.customer_country_id,
          cart_data: {
            is_cod_on_cart: allItems.every((o) => o.is_cod),
            cart_weight: +(quoteFigures.totalWeight * 1000).toFixed(2),
            cart_amount: total_with_discount,
          },
          products: {
            children: [
              +allItems.find((item) => item.product_id !== undefined)
                ?.product_id || 0,
            ],
          },
        });
    }

    await this.computeFreeProducts(
      quote,
      allItems,
      productDetails,
      itemPromotions,
      skuWiseErrors,
      total_with_discount,
      countryId,
      regionId,
      productTaxes,
    );

    // console.log(JSON.stringify(allItems, null, 4), 'ALL ITEMS');

    return {
      availableShippingMethods,
      availablePaymentMethods,
      min_shipping_amount,
      is_member_ship_active,
      delivery_charges: deliveryFee,
    };
  }

  async applicableCouponDiscount(
    quoteItems: QuoteItem[],
    quoteDiscounts: QuoteDiscount,
    couponCode: string,
    customerId: number,
    customerGroupId: number,
    countryId: string,
    cartAction: CartAction,
    appliedAutoDiscount,
    formattedProductsForCoupon: CouponProductType[],
    quoteFigures: QuoteFigures,
    shippingAddress: QuoteAddress,
  ) {
    // const { grandTotal: sub_total, grandTotalForCoupon } = quoteFigures;
    // const grandTotalForCoupon =
    //   sub_total - reward_discount - admin_discount - auto_discount;

    // Remove coupon from quote discount
    const singleCouponIndex = quoteDiscounts?.discounts.findIndex(
      (coupon) => coupon?.coupon_code,
    );
    if (singleCouponIndex !== -1) {
      quoteDiscounts?.discounts.splice(singleCouponIndex, 1);
    }
    // Initialize combined discounts with auto discounts if any
    const couponAndAutoDiscountMap =
      appliedAutoDiscount?.product_discount ?? {};
    // Apply coupon if applicable
    let appliedCoupon: AppliedCouponResponse = null;
    if (
      cartAction !== CartAction.REMOVE_COUPON &&
      couponCode &&
      formattedProductsForCoupon?.length > 0
    ) {
      if (appliedAutoDiscount) {
        for (const product of formattedProductsForCoupon) {
          const discountedAmount =
            couponAndAutoDiscountMap[product.product_id]?.discount_amount ?? 0;
          product.applied_discount += discountedAmount;
        }
      }
      appliedCoupon = await this.externalApiHelper.applyCoupon(
        formattedProductsForCoupon,
        couponCode,
        customerId,
        customerGroupId,
        countryId,
        shippingAddress,
        quoteFigures,
        // grandTotalForCoupon,
        // sub_total,
        cartAction === CartAction.APPLY_COUPON ||
          cartAction === CartAction.APPLY_REWARD,
      );
      if (appliedCoupon?.items_discount) {
        this.cartUtilityFun.accumulateDiscounts(
          appliedCoupon?.items_discount,
          couponAndAutoDiscountMap,
        );
      }
    }

    // Prepare final applied coupon and auto discount rows
    const appliedCouponRow = appliedCoupon
      ? [
          {
            discount_id: appliedCoupon.rule_id,
            coupon_id: appliedCoupon.coupon_id,
            coupon_code: appliedCoupon.coupon_code,
            discount_amount: appliedCoupon.discount_amount,
            discount_percentage: appliedCoupon.discount_percentage,
            is_free_shipping: appliedCoupon.is_free_shipping,
          },
        ]
      : [];

    const appliedAutoDiscountRow =
      appliedAutoDiscount?.rule_discount?.map((o) => {
        return {
          rule_id: o.rule_id,
          discount_lable: o.discount_lable,
          is_free_shipping: o.is_free_shipping,
          discount_amount: o.discount_amount,
        };
      }) ?? [];

    const finalAppliedCouponRow = [
      ...appliedCouponRow,
      ...appliedAutoDiscountRow,
    ];

    const appliedRuleIds = appliedAutoDiscount?.rule_discount?.map(
      (o) => o.rule_id,
    );
    return {
      couponAndAutoDiscountMap,
      totalCouponDiscount:
        (appliedCoupon?.discount_amount ?? 0) +
        (appliedAutoDiscount?.total_discount ?? 0),
      appliedCouponCode: appliedCoupon?.coupon_code ?? null,
      appliedCouponRow: finalAppliedCouponRow,
      appliedRuleIds:
        appliedRuleIds?.length > 0 ? appliedRuleIds.join(',') : null,
      couponShippingStatus:
        (appliedCoupon?.is_free_shipping ?? false) ||
        (appliedAutoDiscount?.is_free_shipping ?? false),
    };
  }

  async getAllQuoteCharges(
    allItems: QuoteItem[],
    quote: Quote,
    // rewardDiscount: DiscountValues,
    appliedPoints: number,
    countryId: string,
    shippingAddress: QuoteAddress,
    throwError: boolean,
    productDetails: ProductData[],
    customerId: number,
    customerGroupId: number,
    cartAction: CartAction,
  ) {
    let quoteFigures =
      this.cartUtilityFun.calculateQuoteFiguresFromItems(allItems);
    let rewardAndMembershipInfo: RewardAndMembershipResponse;
    if (customerId) {
      rewardAndMembershipInfo =
        await this.externalApiHelper.getRewardAndMembershipInfo(
          customerId,
          quoteFigures.grandTotal,
        );
    }
    const memberShipInfo = {
      is_active: rewardAndMembershipInfo?.is_membership_active ?? false,
      spent_rate: rewardAndMembershipInfo?.reward_spending_rate ?? null,
    };

    const { reward_discount, reward_discount_row } =
      this.cartUtilityFun.calculateCouponRewardDiscountV2(
        quote,
        allItems.length,
        rewardAndMembershipInfo,
        appliedPoints,
        cartAction,
      );

    const isVirtualCart = allItems.every((o) => o.is_virtual === true);
    let { delivery_charges, availableShippingMethods, min_shipping_amount } =
      await this.getDeliveryCharges(
        shippingAddress,
        quoteFigures,
        isVirtualCart,
        memberShipInfo?.is_active,
        throwError,
      );

    const formattedProductsForCoupon =
      this.cartUtilityFun.getFormattedProductsForCoupon(
        allItems,
        productDetails,
      );
    quoteFigures.grandTotalForCoupon =
      quoteFigures.grandTotal - reward_discount - quoteFigures.admin_discount;
    // Apply auto discount if applicable
    const appliedAutoDiscount: AppliedAutoDiscountResponse =
      formattedProductsForCoupon?.length > 0
        ? await this.externalApiHelper.applyAutoDiscount(
            formattedProductsForCoupon,
            customerId,
            customerGroupId,
            countryId,
            quoteFigures,
            shippingAddress,
          )
        : null;
    const autoDiscount = appliedAutoDiscount?.total_discount ?? 0;
    quoteFigures.grandTotalForCoupon =
      quoteFigures.grandTotal -
      reward_discount -
      quoteFigures.admin_discount -
      autoDiscount;
    quoteFigures.reward_discount = reward_discount;
    quoteFigures.auto_discount = autoDiscount;
    quoteFigures.delivery_charges = delivery_charges;
    // const otherQuoteCharges = {
    //   reward_discount,
    //   delivery_charges,
    //   auto_discount: appliedAutoDiscount?.total_discount ?? 0,
    // };
    // quoteFigures = {
    //   ...quoteFigures,
    //   ...otherQuoteCharges,
    // };
    return {
      quoteFigures,
      is_member_ship_active: memberShipInfo?.is_active,
      min_shipping_amount,
      reward_discount_row,
      availableShippingMethods,
      isVirtualCart,
      appliedAutoDiscount,
      formattedProductsForCoupon,
    };
  }

  async getDeliveryCharges(
    // allItems: QuoteItem[],
    shippingAddress: QuoteAddress,
    quoteFigures: any,
    isVirtualCart: boolean,
    memberShipStatus: boolean,
    throwError,
  ) {
    const { is_free_shipping, min_shipping_amount } =
      await this.cartUtilityFun.isFreeShippingV2(
        memberShipStatus,
        +quoteFigures.grandTotal,
        isVirtualCart,
      );
    let delivery_charges = 0;
    /**
        update shipping details
        */
    let availableShippingMethods = [];
    // let availablePaymentMethods = [];
    if (shippingAddress) {
      try {
        availableShippingMethods = !isVirtualCart
          ? await this.shippingMethodService.getShippingMethods({
              weight: quoteFigures.totalWeightExcludingVirtualProduct,
              subTotal: quoteFigures.grandTotalExcludingVirtualProduct,
              countryCode: shippingAddress.customer_country_id,
            })
          : [];
        if (availableShippingMethods.length && is_free_shipping) {
          availableShippingMethods[0].charges = 0;
        }
      } catch (e) {
        availableShippingMethods = [];
        if (throwError) throw new InternalServerErrorException(e?.message);
      }
      delivery_charges = availableShippingMethods?.[0]?.charges || 0;
    }

    return { delivery_charges, availableShippingMethods, min_shipping_amount };
  }

  async updateQuoteItemDiscountWithoutTxnV2(
    itemWiseDiscount: ItemDiscount,
    existingItemWiseDiscount: ItemDiscount,
  ) {
    for (const key in itemWiseDiscount) {
      if (
        itemWiseDiscount[key].discount_amount !=
        existingItemWiseDiscount[key]?.discount_amount
      ) {
        await QuoteItem.update(
          {
            discount_amount: itemWiseDiscount[key].discount_amount,
            discount_percent: itemWiseDiscount[key].discount_percent,
          },
          {
            where: { quote_item_id: key },
          },
        );
      }
    }
  }

  async computeFreeProducts(
    quote: Quote,
    quoteItems: QuoteItem[],
    productDetails: ProductData[],
    itemPromotions: ItemPromotion[],
    skuWiseErrors: any,
    grandTotal: number,
    countryId: string,
    regionId: string,
    productTaxes: any,
  ) {
    const freeProductMap: Map<string, number> = new Map();
    // ITEM PROMOTION LOGIC
    for (const item of quoteItems) {
      const itemPromotion = itemPromotions?.find(
        (o) => o?.product_sku === item?.sku,
      );
      if (!itemPromotion) {
        await this.cartUtilityFun.clearItemPromotions(item);
        continue;
      }

      const productDetail = productDetails.find(
        (o) => o?.sku === itemPromotion?.free_product_sku,
      );
      if (!productDetail) continue;

      const freeQty = this.cartUtilityFun.getFreeProductQty(
        item?.qty,
        itemPromotion,
      );

      if (freeProductMap.has(itemPromotion.free_product_sku)) {
        const currentQty =
          freeProductMap.get(itemPromotion.free_product_sku) || 0;
        freeProductMap.set(
          itemPromotion.free_product_sku,
          currentQty + freeQty,
        );
      } else {
        freeProductMap.set(itemPromotion.free_product_sku, freeQty);
      }

      const freeProductQty =
        this.cartUtilityFun.validateFreeProductAvailability(
          productDetail,
          // freeProductMap.get(itemPromotion.free_product_sku),
          freeProductMap,
          freeQty,
        );
      const productObj = this.cartUtilityFun.createProductObject(
        itemPromotion,
        productDetail,
        freeProductQty,
        productTaxes,
      );

      if (!item?.quoteItemPromotions?.length && freeProductQty !== 0) {
        await this.cartUtilityFun.createItemPromotion(
          item,
          itemPromotion,
          productObj,
        );
      } else {
        await this.cartUtilityFun.updateItemPromotions(
          item,
          itemPromotion,
          freeProductQty,
          productObj,
        );
      }
    }

    // AMOUNT PROMOTION LOGIC
    await this.handleAmountPromotion(
      quote,
      grandTotal,
      productDetails,
      skuWiseErrors,
      countryId,
      regionId,
      freeProductMap,
    );
    // return freeProductMap;
  }

  getCurrentTimeHHMMSSMS() {
    const now = new Date();

    // Get hours, minutes, seconds
    const hh = String(now.getHours()).padStart(2, '0');
    const mm = String(now.getMinutes()).padStart(2, '0');
    const ss = String(now.getSeconds()).padStart(2, '0');

    // Get milliseconds and pad to 3 digits
    const ms = String(now.getMilliseconds()).padStart(3, '0');

    return `${hh}:${mm}:${ss}:${ms}`;
  }

  async handleAmountPromotion(
    quote: Quote,
    grandTotal: number,
    productDetails: ProductData[],
    skuWiseErrors: any,
    countryId: string,
    regionId: string,
    freeProductMap: Map<string, number>,
  ) {
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    const amountPromotion = await AmountPromotion.findOne({
      where: {
        min_order_amount: { [Op.lte]: grandTotal },
        start_date: { [Op.lte]: currentDate },
        end_date: { [Op.gte]: currentDate },
      },
      order: [['min_order_amount', 'DESC']],
    });
    // console.log(amountPromotion, 'AMOUN KA PROMOTINN S');
    if (!amountPromotion) {
      await this.cartUtilityFun.removeAllAmountPromotions(quote);
      return;
    }
    // const amountPromotionProductDetail = (
    //   await this.externalApiHelper.getProductDataFromSku([
    //     amountPromotion?.product_sku,
    //   ])
    // )[0];
    const [prodDetail, amountPromotionProductTax] = await this.fetchApiDataV2(
      [amountPromotion?.product_sku],
      countryId,
      regionId,
    );
    const amountPromotionProductDetail = prodDetail[0];
    // this.validateProductAvailability({
    //   product: amountPromotionProductDetail,
    //   throwError: false,
    //   skuWiseErrors: skuWiseErrors,
    //   country_id: countryId,
    //   qty: +amountPromotion.buy_qty,
    // });
    const freeQty = amountPromotion.buy_qty;
    if (freeProductMap.has(amountPromotion.product_sku)) {
      const currentQty = freeProductMap.get(amountPromotion.product_sku) || 0;
      freeProductMap.set(amountPromotion.product_sku, currentQty + freeQty);
    } else {
      freeProductMap.set(amountPromotion.product_sku, freeQty);
    }
    const freeProductQty = this.cartUtilityFun.validateFreeProductAvailability(
      amountPromotionProductDetail,
      freeProductMap,
      freeQty,
    );

    productDetails.push(amountPromotionProductDetail);
    const appliedTaxes =
      amountPromotionProductTax[amountPromotionProductDetail?.tax_class_id] ||
      [];
    const productPrice =
      amountPromotionProductDetail?.price?.minimalPrice?.amount?.value;
    const { taxPercent, taxAmount } = this.cartMapper.deriveProductTax(
      appliedTaxes,
      productPrice,
    );
    const productObj: AmountPromotionProductData = {
      free_sku: amountPromotion.product_sku,
      qty: freeProductQty,
      // qty: amountPromotion.buy_qty,
      name: amountPromotionProductDetail.name,
      product_id: amountPromotionProductDetail.id,
      parent_id: amountPromotionProductDetail.parent_id,
      // product_type: amountPromotionProductDetail.type_id,
      price: productPrice,
      // special_price:
      //   amountPromotionProductDetail.price.minimalPrice.amount.value,
      start_date: amountPromotion.start_date,
      end_date: amountPromotion.end_date,
      tax_amount: taxAmount,
      tax_percent: taxPercent,
      weight: amountPromotionProductDetail.weight,
      url_key: amountPromotionProductDetail.url_key,
      row_total_incl_tax: productPrice * freeProductQty,
      image: amountPromotionProductDetail.image_url,
    };
    await this.cartUtilityFun.handleAmountPromotionUpdate(
      quote,
      amountPromotion,
      productObj,
    );
  }

  async fetchApiDataV2(skus: string[], country_id: string, region_id: string) {
    try {
      if (skus.length && country_id && region_id) {
        return Promise.all([
          this.externalApiHelper.getProductDataFromSku(skus),
          this.externalApiHelper.getTaxRatesForCart(country_id, region_id),
        ]);
      } else if (skus.length) {
        return Promise.all([
          this.externalApiHelper.getProductDataFromSku(skus),
          {},
        ]);
      }
      return [];
    } catch (e) {
      throw new InternalServerErrorException(e);
    }
  }

  async upsertAddressOnCart(
    quoteId: number,
    customerEmail: string,
    customerId: number,
    addressType: AddressTypes,
    quoteAddress: QuoteAddress,
    addressPayload: BillingAddressDto | AddressDto,
    customerAddressId?: number,
  ): Promise<QuoteAddress> {
    const mappedAddress = this.cartMapper.mapQuoteAddressObj(
      { address: addressPayload, customer_address_id: customerAddressId },
      customerEmail,
      customerId,
      addressType,
    );

    if (
      'same_as_billing' in addressPayload &&
      addressPayload.same_as_billing === false
    ) {
      mappedAddress['same_as_billing'] = false;
    }

    if (quoteAddress) {
      await QuoteAddress.update(mappedAddress, {
        where: { quote_address_id: quoteAddress.quote_address_id },
      });
      return QuoteAddress.findByPk(quoteAddress.quote_address_id);
    }

    return QuoteAddress.create({
      quote_id: quoteId,
      customer_email: customerEmail,
      ...mappedAddress,
    });
  }

  async updateBuyingGuideQty(quoteItem: QuoteItem, qty: number) {
    if (quoteItem?.extension_attribute) {
      await QuoteItemExtensionAttribute.update(
        {
          buying_guide_qty:
            (Number(quoteItem?.extension_attribute?.buying_guide_qty) || 0) +
            (Number(qty) || 0),
        },
        {
          where: {
            extension_attribute_id:
              quoteItem?.extension_attribute.extension_attribute_id,
            quote_item_id: quoteItem.quote_item_id,
          },
        },
      );
    } else {
      await QuoteItemExtensionAttribute.create({
        quote_item_id: quoteItem.quote_item_id,
        buying_guide_qty: qty,
      });
    }
  }
}
