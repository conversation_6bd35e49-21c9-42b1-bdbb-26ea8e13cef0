import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { AddressTypes, CartAction } from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { CartMapper } from 'src/mapper/cart.mapper';
import { logger } from './service-logger';
import * as _ from 'lodash';
import { QuoteDiscount } from 'src/database/entities/quote_discount';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { Sequelize } from 'sequelize-typescript';
import { CartAddress } from 'src/interface/set-billing-address-on-cart';
import { SetShiipingAddressOnCartRequest } from 'src/interface/set-shipping-address-on-cart-request';
import { CartHelperFunctions } from './cart-service-helper';
import { CartUtilityFunctions } from './cart-utility-function';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import { AmountPromotion } from 'src/database/entities/amount_promotion';
import { OutputResponseType } from 'src/config/constants';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';

@Injectable()
export class CartAddressService {
  constructor(
    private readonly cartMapper: CartMapper,
    private readonly cartHelperFunctions: CartHelperFunctions,
    private readonly cartUtilityFun: CartUtilityFunctions,
    @Inject('SEQUELIZE') private readonly sequelize: Sequelize,
  ) {}

  /**
   * It build data to be stored in QuoteAddress table
   * @param request    SetBillingAddressOnCartInput
   * @param cartId     String
   * @param customerId Registered user's customer-id
   * @returns
   **/
  async setBillingAddressOnCart(
    request: CartAddress,
    cartId: string,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
  ) {
    const skuWiseErrors = {},
      parentMappedPrice = {};
    const cartIDExists = await Quote.findOne({
      where: { masked_id: cartId, customer_id: customerId || null },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });
    if (!cartIDExists) {
      throw new NotFoundException('Cart not found');
    }
    const fetchRegion = await this.cartHelperFunctions.validateCountryAndRegion(
      request,
    );
    let addressBilling = cartIDExists.addresses.find(
      (addressType) => addressType.address_type === AddressTypes.BILLING,
    );
    try {
      const cartObj = this.cartMapper.mapQuoteAddressObj(
        request,
        cartIDExists.customer_email,
        cartIDExists.customer_id,
        AddressTypes.BILLING,
      );
      if (addressBilling) {
        await QuoteAddress.update(cartObj, {
          where: {
            quote_address_id: addressBilling.quote_address_id,
          },
        });
        addressBilling = await QuoteAddress.findByPk(
          addressBilling.quote_address_id,
        );
      }
      addressBilling = await QuoteAddress.create({
        quote_id: cartIDExists.quote_id,
        ...cartObj,
      });
      const shippingAddress = cartIDExists.addresses.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        addressBilling,
      );

      const skus = cartIDExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartIDExists.items,
        [],
        'set_billing_address',
      );
      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      cartIDExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartIDExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          country_id,
        );

      const { availableShippingMethods, availablePaymentMethods } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems: cartIDExists.items,
          quote: cartIDExists,
          billingAddress: addressBilling,
          shippingAddress,
          customerId,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartIDExists),
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.SET_BILLING,
          itemPromotions,
          productTaxes,
        });
      const updatedQuote = await Quote.findByPk(cartIDExists.quote_id, {
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteDiscount,
        ],
      });
      return this.cartMapper.buildCartResponse(
        updatedQuote,
        productDetailsClone ?? [],
        // productDetails ?? [],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
      );
    } catch (error) {
      logger.error('Error in addBillingAddress to cart', error);
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It sets shipping address on cart
   * @param request
   * @param customerId
   * @returns
   */
  async setShippingAddressOnCart(
    request: SetShiipingAddressOnCartRequest,
    customerId?: number,
    customerGroupId?: number,
    magentoCustomerId?: number,
    outputResponseType?: OutputResponseType,
  ) {
    const skuWiseErrors = {},
      parentMappedPrice = {};
    const cartExists = await Quote.findOne({
      where: { masked_id: request.cart_id, customer_id: customerId || null },
      include: [
        {
          model: QuoteItem,
          include: [
            {
              model: QuoteItemExtensionAttribute,
            },
            {
              model: QuoteItemPromotion,
            },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
        QuoteDiscount,
      ],
    });
    if (!cartExists) {
      throw new BadRequestException('Cart not found');
    }

    let shippingAddressExists = cartExists.addresses.find(
      (addressType) => addressType.address_type === AddressTypes.SHIPPING,
    );

    let billingAddress = cartExists.addresses.find(
      (o) => o.address_type === AddressTypes.BILLING,
    );

    //apppend region and region_code in case null for country india
    await this.cartHelperFunctions.getRegionAndRegionCode(
      request.shipping_addresses[0],
    );

    try {
      shippingAddressExists =
        await this.cartHelperFunctions.upsertAddressOnCart(
          cartExists.quote_id,
          cartExists.customer_email,
          cartExists.customer_id,
          AddressTypes.SHIPPING,
          shippingAddressExists,
          request.shipping_addresses[0].address,
          request.shipping_addresses[0]?.customer_address_id || null,
        );

      if (request.billing_address) {
        billingAddress = await this.cartHelperFunctions.upsertAddressOnCart(
          cartExists.quote_id,
          cartExists.customer_email,
          cartExists.customer_id,
          AddressTypes.BILLING,
          billingAddress,
          request.billing_address,
        );
      } else {
        if (billingAddress) {
          await billingAddress.destroy();
          billingAddress = null;
        }
      }

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddressExists,
        billingAddress,
      );
      const skus = cartExists.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartExists.items,
        [],
        CartAction.SET_SHIPPING,
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      cartExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          shippingAddressExists?.customer_country_id,
        );

      let allItems = cartExists?.items;

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerGroupId || 0,
          allItems,
          quote: cartExists,
          billingAddress,
          shippingAddress: shippingAddressExists,
          customerId,
          countryId: country_id,
          regionId: region_id,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartExists),
          throwError: true,
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.SET_SHIPPING,
          outputResponseType,
          itemPromotions,
          productTaxes,
        });
      const updatedQuote = await Quote.findByPk(cartExists.quote_id, {
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteDiscount,
        ],
      });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        // [...(productDetails || [])],
        shippingAddressExists,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
        billingAddress,
      );
    } catch (error) {
      logger.error('Error in setShippingAddressOnCart', error);
      this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It will set emailId in guest cart(where customerId is null)
   * @param cartId To set email on
   * @param email email-id
   * @returns
   **/
  async setGuestEmail(
    cartId: string,
    email: string,
    magentoCustomerId?: number,
  ) {
    if (cartId) {
      const cartIDExists = await Quote.findOne({
        where: { masked_id: cartId, customer_id: null },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });
      if (!cartIDExists) {
        throw new BadRequestException(`Invalid cart`);
      }
      try {
        const skuWiseErrors = {},
          parentMappedPrice = {};
        const shippingAddress = cartIDExists.addresses.find(
          (o) => o.address_type === AddressTypes.SHIPPING,
        );
        const billingAddress = cartIDExists.addresses.find(
          (o) => o.address_type === AddressTypes.BILLING,
        );
        await Quote.update(
          { customer_email: email },
          {
            where: { quote_id: cartIDExists.quote_id },
          },
        );
        const { country_id, region_id } =
          this.cartUtilityFun.getCountryAndRegion(
            shippingAddress,
            billingAddress,
          );

        const skus = cartIDExists?.items?.map((o) => o.sku);
        const { itemPromotions } =
          await this.cartUtilityFun.getAllItemPromotions(skus);
        const freeSkus =
          itemPromotions?.length > 0
            ? itemPromotions.map((item) => item.free_product_sku)
            : [];

        const [productDetails, productTaxes] =
          await this.cartHelperFunctions.fetchApiDataV2(
            [...skus, ...freeSkus],
            country_id,
            region_id,
          );
        const productDetailsClone = productDetails
          ? _.cloneDeep(productDetails)
          : null;

        const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
          cartIDExists.items,
          [],
          'set_guest_email',
        );
        productDetails &&
          this.cartUtilityFun.computeGroupChildPrice(
            productDetails,
            skuwiseParentIdQty,
            parentMappedPrice,
          );

        cartIDExists.customer_email = email;
        cartIDExists.items =
          await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
            cartIDExists.items,
            productDetails ?? [],
            productTaxes ?? {},
            skuWiseErrors,
            skuwiseParentIdQty,
            parentMappedPrice,
            shippingAddress?.customer_country_id,
          );

        const { availableShippingMethods, availablePaymentMethods } =
          await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2(
            {
              customerGroupId: 0,
              allItems: cartIDExists.items,
              quote: cartIDExists,
              billingAddress,
              shippingAddress,
              customerId: 0, // need to change this
              countryId: country_id,
              regionId: region_id,
              couponCode:
                this.cartUtilityFun.getCustomerCouponCode(cartIDExists),
              skuwiseParentIdQty,
              parentMappedPrice,
              productDetails: productDetailsClone,
              cartAction: CartAction.SET_GUEST_EMAIL,
              itemPromotions,
              productTaxes,
            },
          );
        const updatedQuote = await Quote.findByPk(cartIDExists.quote_id, {
          include: [
            {
              model: QuoteItem,
              include: [
                {
                  model: QuoteItemExtensionAttribute,
                },
                {
                  model: QuoteItemPromotion,
                },
              ],
            },
            QuoteAmountPromotion,
            QuoteDiscount,
          ],
        });
        return this.cartMapper.buildCartResponse(
          updatedQuote,
          productDetailsClone ?? [],
          // productDetails ?? [],
          shippingAddress,
          availableShippingMethods,
          availablePaymentMethods,
          skuWiseErrors,
        );
      } catch (e) {
        logger.error('Error in setGuestEmail', e);
        this.cartUtilityFun.throwError(e);
      }
    }
  }

  async setEmailOnGuestCart(cart_id: string, email: string) {
    if (cart_id) {
      const cartIDExists = await Quote.findOne({
        where: { masked_id: cart_id, customer_id: null },
      });
      if (!cartIDExists) {
        throw new BadRequestException(`Invalid cart`);
      }
      try {
        await Quote.update(
          { customer_email: email },
          {
            where: { quote_id: cartIDExists.quote_id },
          },
        );

        const updatedQuote = await Quote.findByPk(cartIDExists.quote_id);
        return { cart: updatedQuote };
      } catch (e) {
        logger.error('Error in setEmailOnGuestCart rest endpoint', e);
        this.cartUtilityFun.throwError(e);
      }
    }
  }
}
