import { BadRequestException, Injectable } from '@nestjs/common';
import config from 'src/config/env';
import { Request } from 'express';
import { SalesRule } from 'src/interface/sales-rules';
import {
  AvailablePaymentMethodsRequest,
  AvailableShippingMethodsRequest,
  AvailableShippingMethodsV4Request,
  AvailableShippingMethodsV5Request,
  CustomerCouponResponse,
} from '../interface/graphql-request';
import {
  GetShippingmethodsResponse,
  GlobalCurrencyConfiguration,
  PaymentMethod,
  ProductData,
  getAvailablePaymentMethodV4Response,
} from '../interface/graphql-response';
import { ExternalApiCaller } from './external-api-caller';
import { MaskHelper } from './mask.helper';
import { logger } from './service-logger';
import * as AWSXRay from 'aws-xray-sdk';
import { AppliedAutoDiscountResponse } from 'src/interface/auto-discount-response';
import { CouponProductType } from 'src/interface/coupon-products';
import { AppliedCouponResponse } from 'src/interface/coupon-response';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { AddItemsToCartBody } from 'src/interface/add-product-to-cart-request';
import { RewardAndMembershipResponse } from 'src/interface/reward-and-membership-response';
@Injectable()
export class ExternalApiHelper {
  constructor(
    private readonly externalApiCaller: ExternalApiCaller,
    private readonly maskHelper: MaskHelper,
  ) {}
  /**
   * It fetches customer details by making a call
   * to external API
   * @param authToken user's session token
   * @returns
   */
  async getCustomerDetails(authToken: string, cartId?: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerDetails:API',
    );
    try {
      const url = config.dk.new_customer_details_url;
      const headers = { Authorization: `Bearer ${authToken}` };

      // logger.info({
      //   message: 'customer get request started',
      //   url: url,
      //   headers: JSON.stringify(headers),
      //   cartId: cartId,
      // });

      const response = await this.externalApiCaller.get(url, headers, {});

      if (!response) {
        // logger.info({
        //   message: 'No response from customer get',
        //   url: config.dk.new_customer_details_url,
        //   cartId: cartId,
        // });
        throw new Error('Invalid token');
      }

      // logger.info({
      //   message: 'customer get request completed',
      //   url: config.dk.new_customer_details_url,
      //   customerId: response?.id,
      //   magentoId: response?.magento_customer_id,
      //   cartId: cartId,
      // });

      return response;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches customer details by making a call
   * to external API
   * @param customer_id user's customer id
   * @returns
   */
  async getCustomerDetailsFromId(customer_id: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerDetailsFromId:API',
    );
    try {
      const url = `${config.dk.customer_details_from_id}/${customer_id}`;
      const headers = { 'x-api-key': config.dk.customer_service_api_key };
      const response = await this.externalApiCaller.get(url, headers);
      if (!response) throw new Error('unable to get customer');
      return response;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches global currency configuration that
   * is set at Magento end via Admin portal
   * @returns
   */
  async getCurrencyConfiguration(): Promise<GlobalCurrencyConfiguration> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCurrencyConfiguration:API',
    );
    try {
      const query = `query Query {
      currency {
          base_currency_code
          base_currency_symbol
      }}`;
      const url = config.dk.graphql_base_url;
      const response = await this.externalApiCaller.post(url, {}, { query });

      if (!response) throw new Error('Internal server error');
      return response?.data?.currency;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetched customer's tax-class-id from customer group-id
   * @param customer_group_id group-id to fetch tax-class-id for
   * @returns
   */
  async getCustomerTaxClassFromGroup(customer_group_id: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerTaxClassFromGroup:API',
    );
    try {
      const url = config.dk.customer_groups_url + '/' + customer_group_id;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const response = await this.externalApiCaller.get(url, headers);
      if (!response) throw new Error('Internal server error');
      return response?.tax_class_id;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch product details for given skus
   * @param skus string[]
   * @returns
   */
  async getProductDataFromSku(skus: string[]): Promise<ProductData[] | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getProductDataFromSku:API',
    );
    try {
      const url = config.dk.product_for_service_gql_url;

      // logger.info({
      //   message: ' productForService request started',
      //   url: url,
      //   skus: JSON.stringify(skus),
      // });

      const query = `query Query {
        productForService(sku: ${JSON.stringify(skus)}) {
          id
          thumbnail_url
          sku
          url_key
          weight
          reward_point_product
          pd_expiry_date
          type_id
          tax_class_id
          name
          is_in_stock
          max_sale_qty
          price {
            minimalPrice {
              adjustments
              amount {
                currency
                currency_symbol
                value
              }
            }
            regularPrice {
              adjustments
              amount {
                currency
                currency_symbol
                value
              }
            }
          }
          
          average_rating
          demo_available
          dentalkart_custom_fee
          dispatch_days
          image_url
          is_cod
          manufacturer
          meta_description
          meta_keyword
          meta_title
          msrp
          rating_count
          special_price
          tier_prices{
            customer_group_id
            percentage_value
            qty
            value
            website_id
          }
          qty
          min_sale_qty
          status
          categories{
            id
            level
            name
            position
            url_path
          }
          backorders
          international_active
          parent_id
        }
      }`;
      //const headers = { 'x-api-key': config.dk.x_api_key };
      const products = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
        true,
      );

      // logger.info({
      //   message: ' productForService request completed',
      //   products: JSON.stringify(products),
      // });

      return products?.data?.productForService;
    } catch (error) {
      logger.error('Error in productForService', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches all available shipping methods for requested cart
   * with respect to address & cart details
   * @param payload AvailableShippingMethodsRequest
   * @returns
   */
  async getAvailableShippingMethods(payload: AvailableShippingMethodsRequest) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getAvailableShippingMethods:API',
    );
    try {
      const url = config.dk.methods_graphql_url;
      const headers = { 'x-api-key': config.dk.methods_x_api_key };
      const query = `query Query{
          GetShippingMethod(
            customer_id: "${payload.customerId}"
            pincode: ${payload.pincode}
            total_weight: ${payload.weight}
            total_amount: ${payload.grandTotal}
            country_code: "${payload.countryCode}"
          ) {
            method_code
            carrier_code
            carrier_title
            method_title
            charges
            currency
          }
        }`;
      const shippingMethods: { data: GetShippingmethodsResponse } =
        await this.externalApiCaller.post(url, headers, {
          query,
        });
      return shippingMethods?.data?.GetShippingMethod || null;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * Fetch available payment methods for a cart
   * with respect to address, cart total & product details
   * @param payload AvailablePaymentMethodsRequest
   * @returns
   */
  async getAvailablePaymentMethods(
    payload: AvailablePaymentMethodsRequest,
  ): Promise<PaymentMethod[]> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getAvailablePaymentMethods:API',
    );
    try {
      const url = config.dk.get_payment_methods_url;
      const query = `query Query {
          GetAvailableMethodsV2(
            pincode: "${payload.pincode}"
            is_cod: ${payload.isCod}
            total_weight: ${payload.totalWeight}
            total_amount: ${payload.totalAmount}
            country_code: "${payload.countryCode}"
          ) {
            payment_methods {
              code
              title
            }
          }
        }`;

      const paymentMethods = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
      );
      return (
        paymentMethods?.data?.GetAvailableMethodsV2?.payment_methods || null
      );
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch product taxes from REST API
   * @param countryId from customer's shipping address
   * @param regionId from customer's shipping address
   * @returns
   */
  async getTaxRatesForCart(countryId: string, region: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getTaxRatesForCart:API',
    );
    try {
      const url = config.dk.tax_details_url;
      const headers = { 'x-api-key': config.dk.tax_x_api_key };

      // logger.info({
      //   message: 'tax Rate api fetch started',
      //   url: url,
      //   headers: JSON.stringify(headers),
      // });

      const response = await this.externalApiCaller.post(url, headers, {
        country_id: countryId,
        region: region,
      });

      // logger.info({
      //   message: 'tax Rate api fetch completed',
      //   response: JSON.stringify(response),
      // });

      return response ?? {};
    } catch (error) {
      logger.error('Error in getTaxRatesForCart', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches all available automatic discounts
   * @returns
   */
  async getAutomaticDiscounts(cartId?: string): Promise<SalesRule[] | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getAutomaticDiscounts:API',
    );
    try {
      const url = config.dk.salesrules_details_url;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const today = this.maskHelper.maskDateInYYYYMMDD(new Date());

      const params = {
        'searchCriteria[currentPage]': 1,
        'searchCriteria[pageSize]': 50,
        'searchCriteria[filterGroups][0][filters][0][field]': 'coupon_type',
        'searchCriteria[filterGroups][0][filters][0][value]': 1,
        'searchCriteria[sortOrders][0][field]': 'sort_order',
        'searchCriteria[sortOrders][0][direction]': 'asc',
        'searchCriteria[filterGroups][1][filters][0][field]': 'is_active',
        'searchCriteria[filterGroups][1][filters][0][value]': 1,
        'searchCriteria[filterGroups][2][filters][0][field]': 'to_date',
        'searchCriteria[filterGroups][2][filters][0][value]': today,
        'searchCriteria[filterGroups][2][filters][0][conditionType]': 'gteq',
        'searchCriteria[filterGroups][2][filters][1][field]': 'to_date',
        'searchCriteria[filterGroups][2][filters][1][conditionType]': 'null',
        'searchCriteria[filterGroups][3][filters][0][field]': 'from_date',
        'searchCriteria[filterGroups][3][filters][0][value]': today,
        'searchCriteria[filterGroups][3][filters][0][conditionType]': 'lteq',
      };

      // logger.info({
      //   message: `magento sales rule fetch started-${cartId}`,
      //   url: url,
      //   headers: JSON.stringify(headers),
      //   params: JSON.stringify(params),
      // });

      const response = await this.externalApiCaller.get(url, headers, params);

      // logger.info({
      //   message: 'magento sales rule fetch completed',
      //   response: JSON.stringify(response),
      // });

      return response?.items ?? [];
    } catch (error) {
      logger.error('Error in magento sales rule fetch', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It retrieves the details of rules and coupons used by
   * requested customer
   * @param customerId Unique customer-id to get details for
   * @returns
   */
  async getCustomerRulesUsages(magentoCustomerId: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerRulesUsages:API',
    );
    try {
      const url =
        config.dk.customer_rules_usage_details_url +
        `/${magentoCustomerId}/usages`;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };

      // logger.info({
      //   message: 'magento customer rules_usage rule fetch started',
      //   url: url,
      //   headers: JSON.stringify(headers),
      // });

      const response = await this.externalApiCaller.get(url, headers);

      // logger.info({
      //   message: 'magento customer_rules_usage rule fetch completed',
      //   response: JSON.stringify(response),
      // });

      return response?.usages;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It will fetch the details of the country
   * @returns
   */
  async getCoutryDetails(countryId: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCoutryDetails:API',
    );
    try {
      const url = config.dk.graphql_base_url;
      const query = `query Query {
          country(id: "${countryId}") {
            id
            full_name_english
            available_regions {
              id
              code
              name
            }
          }
        }`;
      const countryDetails = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
      );
      return countryDetails?.data || null;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It retrieves the details of coupon
   * @param couponCode CouponCode to get coupon
   * @returns
   */
  async getCouponDetails(couponCode: string): Promise<any[]> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCouponDetails:API',
    );
    try {
      const url = config.dk.coupons_search_url;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const today = this.maskHelper.maskDateInYYYYMMDD(new Date());
      const params = {
        'searchCriteria[currentPage]': 1,
        'searchCriteria[pageSize]': 10,
        'searchCriteria[filterGroups][0][filters][0][field]': 'code',
        'searchCriteria[filterGroups][0][filters][0][value]': couponCode,
        'searchCriteria[filterGroups][1][filters][0][field]': 'expiration_date',
        'searchCriteria[filterGroups][1][filters][0][value]': today,
        'searchCriteria[filterGroups][1][filters][0][conditionType]': 'gteq',
        'searchCriteria[filterGroups][1][filters][1][field]': 'expiration_date',
        'searchCriteria[filterGroups][1][filters][1][conditionType]': 'null',
      };

      // logger.info({
      //   message: 'getCouponDetails fetch started',
      //   url: url,
      //   headers: JSON.stringify(headers),
      //   params: JSON.stringify(params),
      // });

      const response = await this.externalApiCaller.get(url, headers, params);

      // logger.info({
      //   message: 'getCouponDetails fetch completed',
      //   response: JSON.stringify(response),
      // });

      return response?.items;
    } catch (error) {
      logger.error('Error in getCouponDetails', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It will return coupon details by following rule_id
   * @param ruleId ruleId to get details about coupon
   * @returns
   */
  async getRuleDetailsFromId(ruleId: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getRuleDetailsFromId:API',
    );
    try {
      const url = config.dk.coupons_sales_url + `/${ruleId}`;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const response = await this.externalApiCaller.get(url, headers, {});
      return response?.message ? null : response;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To get registered customer's reward-points details
   * @param customerId
   * @returns
   */
  async getUserRewardPointsDetails(customerId: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getUserRewardPointsDetails:API',
    );
    try {
      const url = config.dk.customer_rewards_details_url + `/${customerId}`;
      const rewardsAdminToken = await this.getRewardsSystemAdminToken();
      const headers = {
        Authorization: `Bearer ${rewardsAdminToken}`,
      };
      const response = await this.externalApiCaller.get(url, headers, {});
      return response?.detail
        ? null
        : { userRewardsDetails: response, rewardsAdminToken };
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches global reward-points configurations that are set
   * at the DK Rewards system
   * @returns
   */
  async getRewardPointsGlobalConfig(rewardsAdminToken?: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getRewardPointsGlobalConfig:API',
    );
    try {
      const url = config.dk.rewards_global_config_url;
      const response = await this.externalApiCaller.get(url, {}, {});
      return !response || !response.length ? null : response[0];
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It retrieves admin token for DK Rewards system
   * NOTE: This later will be replaced by a static admin token
   * @returns
   */
  async getRewardsSystemAdminToken() {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getRewardsSystemAdminToken:API',
    );
    try {
      const url = config.dk.rewards_admin_token_url;
      const body = {
        username: 'admin',
        password: 'admin@123',
      };
      const response = await this.externalApiCaller.post(url, {}, body);
      return response?.access;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches getMemberShipConfig
   * return membershipProduct and rate details
   * @returns
   */
  async getMemberShipConfig() {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getMemberShipConfig:API',
    );
    try {
      const url = config.dk.membership_listing_url;
      // logger.info({
      //   message: 'getMemberShipConfig fetch started',
      //   url: url,
      // });
      const response = await this.externalApiCaller.get(url, {}, {});

      // logger.info({
      //   message: 'getMemberShipConfig fetch completed',
      //   response: JSON.stringify(response),
      // });

      return !response || !response.length ? null : response[0];
    } catch (error) {
      logger.error('Error in getMemberShipConfig', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It fetches getMemberShipConfig
   * return membershipProduct and rate details
   * @returns
   */
  async getCustomerMemberShipDetails(customerId: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerMemberShipDetails:API',
    );
    try {
      const url =
        config.dk.customer_membership_lsting_url +
        `/?customer_id=${customerId}`;

      // logger.info({
      //   message: 'getCustomerMemberShipDetails fetch started',
      //   url: url,
      //   customerId: customerId,
      // });

      const response = await this.externalApiCaller.get(url, {}, {});

      // logger.info({
      //   message: 'getCustomerMemberShipDetails fetch completed',
      //   response: JSON.stringify(response),
      // });
      return !response ? null : response;
    } catch (error) {
      logger.error('Error in getCustomerMemberShipDetails', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * It will return coupon details by following couponId
   * @param couponId couponId to get details about coupon
   * @returns
   */
  async getCustomerCouponDetails(couponId: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCustomerCouponDetails:API',
    );
    try {
      const url = config.dk.customer_coupon_url + `/${couponId}`;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const response = await this.externalApiCaller.get(url, headers, {});
      return !response ? null : response;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * Call external service to make cart inactive
   * @param cartId unique CartId
   * @returns
   */
  async inactiveCart(cartId: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment('inactiveCart:API');
    try {
      const url = config.dk.magento_inactive_cart_url + `/${cartId}/inactive`;
      const headers = { Authorization: `Bearer ${config.dk.admin_token}` };
      const response = await this.externalApiCaller.put(url, headers);
      return !response ? null : response;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }
  /**
   * It fetches all available shipping methods for requested cart
   * with respect to address & cart details
   * @param payload AvailableShippingMethodsV4Request
   * @returns
   */
  async getAvailablePaymentMethodV4(
    payload: AvailableShippingMethodsV4Request,
  ) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getAvailablePaymentMethodV4:API',
    );
    try {
      const url = config.dk.fetch_payment_methods_url;

      // logger.info({
      //   message: 'getAvailablePaymentMethodV4 fetch started',
      //   url: url,
      //   payload: JSON.stringify(payload),
      // });

      const query = `query getAvailablePaymentMethodV4(
      $postcode: String
      $countryCode: String
      $products: ProductsInput
      $cartData: CartInput
      ) {
        getAvailablePaymentMethodV4(
          postcode: $postcode
          country_code: $countryCode
          products: $products
          cart_data: $cartData
        ) {
          payment_methods {
            code
            title
          }
        }
    }`;

      const variables = {
        postcode: payload.postcode,
        countryCode: payload.country_code,
        products: payload.products,
        cartData: payload.cart_data,
      };

      const shippingMethods: { data: getAvailablePaymentMethodV4Response } =
        await this.externalApiCaller.post(
          url,
          {},
          {
            query,
            variables,
          },
        );

      // logger.info({
      //   message: 'getAvailablePaymentMethodV4 fetch completed',
      //   response: JSON.stringify(shippingMethods),
      //   variables: JSON.stringify(variables),
      // });

      return (
        shippingMethods?.data?.getAvailablePaymentMethodV4?.payment_methods ||
        []
      );
    } catch (error) {
      logger.error('Error in getAvailablePaymentMethodV4', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch product details for given skus
   * @param skus string[]
   * @returns
   */
  async getGroupProductsData(product_id: number) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getGroupProductsData:API',
    );
    try {
      const url = config.dk.product_for_service_gql_url;
      const query = `query Query {
        childProductV2(id: ${product_id}) {
          items {
            id
            image_url
            name
            sku
            special_price
            url_key
            thumbnail_url
            short_description
            type_id
            manufacturer
            average_rating
            rating_count
            is_in_stock
          }
        }
      }`;
      //const headers = { 'x-api-key': config.dk.x_api_key };
      const products = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
        true,
      );
      return products?.data?.childProductV2?.items;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch product details for given skus
   * @param skus string[]
   * @returns
   */
  async getProductDataFromSkuForPromotion(
    skus: string[],
  ): Promise<ProductData[] | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getProductDataFromSkuForPromotion:API',
    );
    try {
      const url = config.dk.product_for_service_gql_url;

      // logger.info({
      //   message: 'productForService request started',
      //   url: url,
      //   skus: JSON.stringify(skus),
      // });

      const query = `query Query {
        productForService(sku: ${JSON.stringify(skus)}) {
          name
          sku
          is_in_stock
        }
      }`;
      //const headers = { 'x-api-key': config.dk.x_api_key };
      const products = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
        true,
      );

      // logger.info({
      //   message: 'productForService request completed',
      //   response: JSON.stringify(products),
      // });
      return products?.data?.productForService;
    } catch (error) {
      logger.error('Error in getProductDataFromSkuForPromotion', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch product details for given skus
   * @param skus string[]
   * @returns
   */
  async getProductMetadata(skus: string[]): Promise<ProductData[] | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getProductMetadata:API',
    );
    try {
      const url = config.dk.product_for_service_gql_url;
      const query = `query Query {
        productForService(sku: ${JSON.stringify(skus)}) {
          thumbnail_url
          sku
          name
          url_key
        }
      }`;
      //const headers = { 'x-api-key': config.dk.x_api_key };
      const products = await this.externalApiCaller.post(
        url,
        {},
        {
          query,
        },
        true,
      );
      return products?.data?.productForService;
    } catch (error) {
      logger.error('Error in getProductMetadata', error);
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  async fetchPaymentMethods(payload: AvailableShippingMethodsV5Request) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'fetchPaymentMethods:API',
    );
    try {
      const url =
        config.dk.utility_service_base_url +
        `/utility-py/api/v1/delivery/cart-payment-methods?country_code=${payload.country_code}&postcode=${payload.postcode}&cart_amount=${payload.cart_data.cart_amount}&cart_weight=${payload.cart_data.cart_weight}&is_cod_eligible=${payload.cart_data.is_cod_on_cart}`;
      const data = await this.externalApiCaller.get(url, {}, {});
      if (!data?.response?.payment_methods) {
        throw new Error('No payment methods available.');
      }
      return data.response.payment_methods;
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  async getCustomerCoupons(token: string) {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'getCusotmerCoupons:API',
    );
    try {
      const url = `${config.dk.magento_base_url}/graphql`;

      const query = `
        query {
          CustomerCoupons {
            coupon_code
            description
            expiry_date
          }
        }
      `;

      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      };

      const coupons = await this.externalApiCaller.post(url, headers, {
        query,
      });

      return coupons?.data?.CustomerCoupons || [];
    } catch (error) {
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch coupons list for customer
   * @param customerId number
   * @param customerGroupId number
   * @returns
   */
  async getCustomerCouponsNew(
    customerId: number,
    customerGroupId: number,
    countryId?: string,
    products?: CouponProductType[],
  ): Promise<CustomerCouponResponse[] | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment('getCoupons:API');
    try {
      const url = `${config.dk.new_coupon_url}${
        products?.length
          ? `/sales-rules/customers/${customerId}/cart-coupons?customer_group_id=${customerGroupId}`
          : `/sales-rules/customers/${customerId}/coupons?customer_group_id=${customerGroupId}`
      }`;
      const body = products?.length
        ? {
            products,
            customer_id: +customerId,
            customer_group_id: +customerGroupId,
            country_id: countryId,
          }
        : {
            customer_id: +customerId,
            customer_group_id: +customerGroupId,
            country_id: countryId,
          };

      const headers = { 'x-api-key': config.dk.new_coupon_url_api_key };
      const couponsList = products?.length
        ? await this.externalApiCaller.post(url, headers, body, true)
        : await this.externalApiCaller.get(url, headers);
      return couponsList;
    } catch (error) {
      console.log(error, 'ERROR');
      segment.addError(error);
      return null;
    } finally {
      segment.close();
    }
  }
  /**
   * To fetch coupons list for customer
   * @param customerId number
   * @param customerGroupId number
   * @returns
   */
  async applyCoupon(
    products: CouponProductType[],
    couponCode: string,
    customerId: number,
    customerGroupId: number,
    countryId: string,
    shippingAddress: QuoteAddress,
    quoteFigures: QuoteFigures,
    throwError?: boolean,
  ): Promise<AppliedCouponResponse | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment('applyCoupon:API');
    try {
      const url = `${config.dk.new_coupon_url}/sales-rules/apply-coupon`;
      const couponsBody = {
        products,
        coupon_code: couponCode,
        customer_id: customerId,
        customer_group_id: customerGroupId,
        country_id: countryId,
        grand_total: quoteFigures.grandTotalForCoupon,
        subtotal: quoteFigures.grandTotal,
        base_subtotal: quoteFigures.subtotalExcludingTax,
        total_qty: quoteFigures.itemsQty,
        total_weight: quoteFigures.totalWeight,
        postcode: shippingAddress.customer_postcode ?? 'NOT_AVAILABLE',
        region: shippingAddress.customer_region ?? 'NOT_AVAILABLE',
        city: shippingAddress?.customer_city ?? 'NOT_AVAILABLE',
      };

      // console.log(JSON.stringify(couponsBody, null, 4), 'CHECK');
      const timeoutInMs = 1 * 60 * 1000;
      const headers = { 'x-api-key': config.dk.new_coupon_url_api_key };
      const appliedCoupon = await this.externalApiCaller.postV2(
        url,
        headers,
        couponsBody,
        timeoutInMs,
        throwError,
      );
      return appliedCoupon;
    } catch (error) {
      segment.addError(error);
      if (throwError) {
        throw new BadRequestException(error?.response?.message);
      } else {
        return error?.response?.message;
      }
    } finally {
      segment.close();
    }
  }

  /**
   * To fetch applicableAutoDiscount
   * @param products formatter products array for coupon body
   * @param customerId number
   * @param customerGroupId number
   * @param countryId string
   * @returns
   */
  async applyAutoDiscount(
    products: CouponProductType[],
    customerId: number,
    customerGroupId: number,
    countryId: string,
    quoteFigures: QuoteFigures,
    shippingAddress: QuoteAddress,
    throwError?: boolean,
  ): Promise<AppliedAutoDiscountResponse | null> {
    const segment = AWSXRay.getSegment().addNewSubsegment(
      'applyAutoDiscount:API',
    );
    try {
      const url = `${config.dk.new_coupon_url}/sales-rules/auto-discount`;
      const couponsBody = {
        products,
        customer_id: customerId,
        customer_group_id: customerGroupId,
        country_id: countryId,
        grand_total: +quoteFigures.grandTotalForCoupon,
        subtotal: +quoteFigures.grandTotal,
        base_subtotal: +quoteFigures.subtotalExcludingTax,
        total_qty: +quoteFigures.itemsQty,
        total_weight: quoteFigures.totalWeight,
        postcode: shippingAddress.customer_postcode ?? 'NOT_AVAILABLE',
        region: shippingAddress.customer_region ?? 'NOT_AVAILABLE',
        city: shippingAddress?.customer_city ?? 'NOT_AVAILABLE',
      };

      // console.log(JSON.stringify(couponsBody, null, 4), 'AUTO');
      const timeoutInMs = 2 * 60 * 1000;
      const headers = { 'x-api-key': config.dk.new_coupon_url_api_key };
      const appliedCoupon = await this.externalApiCaller.postV2(
        url,
        headers,
        couponsBody,
        timeoutInMs,
        throwError,
      );

      return appliedCoupon;
    } catch (error) {
      segment.addError(error);
      if (throwError) {
        throw new BadRequestException(error?.response?.message);
      } else {
        return error?.response?.message;
      }
    } finally {
      segment.close();
    }
  }

  async sendEasyInsightsData(
    request: Request,
    body: AddItemsToCartBody,
    userId: number,
    eventName: string,
  ) {
    const easyInsightUrl = `${config.easy_insights_url}/backend/`;
    try {
      const protocol = request.headers['x-forwarded-proto'] || request.protocol;
      const host = request.headers['x-forwarded-host'] || request.get('host');
      const url = `${protocol}://${host}${request.originalUrl}`;

      const payload = {
        url: url,
        user_id: userId
          ? Buffer.from(userId.toString()).toString('base64')
          : null,
        fbp: request.headers['fbp'] || '',
        fbc: request.headers['fbc'] || '',
        gclid: request.headers['gclid'] || '',
        cid: request.headers['cid'] || '',
        api_payload: JSON.stringify(body),
        ip: request.ip || request.headers['x-forwarded-for'] || '',
        user_agent: request.headers['user-agent'] || '',
        event_name: eventName,
      };

      // console.log(
      //   JSON.stringify({
      //     message: '+++easyInsightUrl payload+++++',
      //     payload,
      //   }),
      // );
      await this.externalApiCaller.post(easyInsightUrl, {}, payload);
    } catch (e) {
      logger.error(
        JSON.stringify({
          message: '+++Error in sendEasyInsightsData+++',
          url: easyInsightUrl,
          body: JSON.stringify(body),
          error: e?.message,
        }),
      );
    }
  }

  async updateProductExtensionAttributes(payload: any) {
    try {
      const headers = { 'x-api-key': config.dk.svlss_api_key };
      // console.log(
      //   JSON.stringify(payload),
      //   '----updateProductExtensionAttributes----',
      // );
      const response = await this.externalApiCaller.post(
        `${config.dk.svlss_url}/api/v1/webhook/product-extension-attributes`,
        headers,
        payload,
      );
    } catch (err) {
      logger.error(
        JSON.stringify({
          message: '+++Error in updateProductExtensionAttributes+++',
          error: err,
        }),
      );
    }
  }

  async getRewardAndMembershipInfo(customer_id: number, cart_subtotal: number) {
    try {
      const response = await this.externalApiCaller.post(
        `${config.dk.rewards_url}/reward/api/v1/cart/reward-membership-details/`,
        {},
        { customer_id, cart_subtotal },
      );
      return response as RewardAndMembershipResponse;
    } catch (err) {
      console.log(
        JSON.stringify({
          message: '+++Error in getRewardAndMembershipInfo+++',
          error: err,
        }),
      );
    }
  }
}
