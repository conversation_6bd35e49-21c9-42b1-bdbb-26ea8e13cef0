import { HttpService } from '@nestjs/axios';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { logger } from './service-logger';

@Injectable()
export class ExternalApiCaller {
  constructor(private readonly httpService: HttpService) {}

  /**
   * Make a call to any GET method type API with specified headers
   * @param url API end url
   * @param headers header object
   * @returns
   */
  get = async (
    url: string,
    headers: { [key: string]: string },
    params?: { [key: string]: string | number },
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.get(url, { headers, params }),
      );
      // logger.info('get call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in get call', error);
      return null;
    }
  };

  /**
   * Make a call to any POST method type API with specified headers
   * and requested payload data
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  post = async (
    url: string,
    headers: { [key: string]: string },
    payload: any,
    throwError?: boolean,
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payload, { headers }),
      );
      // logger.info('post call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in post call', error);
      if (throwError) throw new InternalServerErrorException(error);
    }
  };

  /**
   * Make a call to any POST method type API with specified headers
   * and requested payload data
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  postV2 = async (
    url: string,
    headers: { [key: string]: string },
    payload: any,
    timeout: number,
    throwError?: boolean,
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.post(url, payload, { headers, timeout }),
      );
      // logger.info('post call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in post call', error);
      if (throwError) {
        throw new InternalServerErrorException(error?.response?.data?.message);
      } else {
        return error?.response?.data?.message;
      }
    }
  };

  /**
   * It makes a PUT method type call to an external API
   * @param url API end url
   * @param headers header object
   * @param payload request body
   * @returns
   */
  put = async (
    url: string,
    headers: { [key: string]: string },
    payload?: any,
  ): Promise<any> => {
    try {
      const response = await firstValueFrom(
        this.httpService.put(url, payload || {}, { headers }),
      );
      // logger.info('put call response', response?.data);
      return [200, 201].indexOf(response.status) === -1 ? null : response?.data;
    } catch (error) {
      logger.error('Error in put call', error);
    }
  };
}
