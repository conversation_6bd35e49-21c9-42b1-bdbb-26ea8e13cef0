interface PricingDetails {
  discounts: Discount[];
  grand_total: {
    amount: PriceDetail;
  };
  overweight_delivery_charges: {
    amount: PriceDetail;
  };
  shipping_charges: {
    amount: PriceDetail;
  };
  subtotal_including_tax: {
    amount: PriceDetail;
  };
  subtotal_excluding_tax: {
    amount: PriceDetail;
  };
  subtotal_with_discount_excluding_tax: {
    amount: PriceDetail;
  };
  total_savings: {
    amount: PriceDetail;
  };
  applied_taxes: PriceDetail[];
}

interface Discount {
  code: string;
  amount: PriceDetail;
}

interface PriceDetail {
  label: string;
  value: number;
}

interface ErrorMessage {
  code: string;
  message: string;
}

interface ProductDetails {
  average_rating: number;
  dentalkart_custom_fee: number;
  description: string;
  dispatch_days: number;
  expiry: string;
  id: number;
  image: {
    url: string;
    label: string;
  };
  is_cod: number;
  manufacturer: string;
  max_sale_qty: number;
  msrp: number;
  name: string;
  pd_expiry_date: string;
  price: {
    minimalPrice: {
      amount: {
        currency: string;
        currency_symbol: string;
        value: number;
      };
    };
    regularPrice: {
      amount: {
        currency: string;
        currency_symbol: string;
        value: number;
      };
    };
  };
  rating_count: number; // Could be number if provided
  reward_point_product: number;
  sku: string;
  small_image: string;
  special_price: number;
  stock_status: string;
  thumbnail: {
    url: string;
    label: string;
  };
  tier_prices: TierPrice[];
  type_id: string;
  url_key: string;
  url_path: string;
  weight: number;
}

interface TierPrice {
  customer_group_id: number;
  percentage_value: number; // Specific type unknown
  qty: number;
  value: number;
}

export interface CartItem {
  is_free_product: boolean;
  error_messages: ErrorMessage[];
  item_id: number;
  item_pricing_details: {
    discounts: Discount[];
    price: {
      amount: PriceDetail;
    };
    row_total: {
      amount: PriceDetail;
    };
    row_total_including_tax: {
      amount: PriceDetail;
    };
  };
  product: ProductDetails;
  qty_increments: number;
  quantity: string;
  reward_point_product: number;
  stock_status: number;
  is_member_ship_product: boolean;
}

interface Address {
  city: string;
  firstname: string;
  lastname: string;
  postcode: string;
  street: string[];
  telephone: string;
  customer_address_id: number;
  region: {
    id: number;
    code: string;
    name: string;
  };
  country: {
    name: string;
    isoCode2: string;
    isoCode3: string;
  };
}

export interface CartResponse {
  global_errors: ErrorMessage[];
  cart_id: string;
  is_virtual: boolean;
  total_quantity: number;
  total_weight: string;
  coupon_code: string | null;
  cart_currency: {
    code: string;
    currency_symbol: string;
  };
  pricing_details: PricingDetails;
  rewards: {
    total_coins: number;
    monetary_value: string;
  };
  addresses: Address; // Could be more specific if address details are available
  items: CartItem[];
}
