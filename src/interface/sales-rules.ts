export interface SalesRulesResponse {
  items: SalesRule[];
  search_criteria: object;
  total_count: number;
}

export interface SalesRule {
  rule_id: number;
  code: string;
  name: string;
  store_labels: string[];
  description: string;
  website_ids: number[];
  customer_group_ids: number[];
  from_date: string;
  uses_per_customer: number;
  is_active: true;
  condition: CombineRuleCondition;
  action_condition: CombineRuleCondition;
  stop_rules_processing: boolean;
  is_advanced: boolean;
  sort_order: number;
  simple_action: string;
  discount_amount: number;
  discount_qty?: number;
  discount_step: number;
  apply_to_shipping: boolean;
  times_used: number;
  is_rss: boolean;
  coupon_type: string; // 'NO_COUPON'
  use_auto_generation: boolean;
  uses_per_coupon: number;
  simple_free_shipping: string;
}

interface SalesRuleActionCondition {
  condition_type: string; // 'Magento\\SalesRule\\Model\\Rule\\Condition\\Combine'
  aggregator_type: string;
  operator: string;
  value: string;
}

export interface SalesRuleCondition extends SalesRuleActionCondition {
  conditions: DirectCondition[];
}

export interface DirectCondition {
  condition_type: string;
  operator: string;
  attribute_name: string;
  value: string;
}

export interface CombineRuleCondition extends SalesRuleActionCondition {
  conditions: DirectCondition[] | SalesRuleCondition[];
}
