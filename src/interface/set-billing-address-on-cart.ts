import { Type } from 'class-transformer';

import {
  IsString,
  IsBoolean,
  IsArray,
  ValidateNested,
  IsNumber,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
export interface SetBillingAddressOnCartInput {
  billing_address: CartAddress;
  cart_id: string;
}

export class AddressDto {
  @IsString()
  @IsOptional()
  alternate_mobile: string;

  @IsString()
  @IsOptional()
  city: string;

  @IsString()
  @IsOptional()
  company: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;

  @IsString()
  @IsOptional()
  firstname: string;

  @IsString()
  @IsOptional()
  lastname: string;

  @IsString()
  @IsOptional()
  postcode: string;

  @IsString()
  @IsOptional()
  region: string;

  @IsBoolean()
  @IsOptional()
  save_in_address_book: boolean;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  street: string[];

  @IsString()
  @IsOptional()
  telephone: string;

  @IsNumber()
  @IsOptional()
  region_id: number;

  @IsString()
  @IsOptional()
  region_code: string;

  @IsString()
  @IsOptional()
  gst_id: string;

  @IsNumber()
  @IsOptional()
  latitude: number;

  @IsNumber()
  @IsOptional()
  longitude: number;

  @IsString()
  @IsOptional()
  tag: string;

  @IsString()
  @IsOptional()
  map_address: string;

  @IsString()
  @IsOptional()
  customer_street_2: string;
}

export class BillingAddressDto {
  @IsString()
  city: string;

  @IsString()
  @IsNotEmpty()
  country_code: string;

  @IsString()
  firstname: string;

  @IsString()
  @IsOptional()
  lastname: string;

  @IsString()
  postcode: string;

  @IsString()
  region: string;

  @IsString()
  region_code: string;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  street: string[];

  @IsString()
  telephone: string;

  @IsNumber()
  @IsOptional()
  region_id: number;

  @IsString()
  @IsOptional()
  gst_id: string;
}

export class CartAddress {
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => AddressDto)
  address: AddressDto;

  @IsNumber()
  @IsOptional()
  customer_address_id: number;

  @IsOptional()
  @IsBoolean()
  same_as_shipping: boolean;

  @IsOptional()
  @IsBoolean()
  user_for_shipping: boolean;
}
