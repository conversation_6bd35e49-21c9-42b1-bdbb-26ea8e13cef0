type ItemDiscountDetails = {
  product_id: number;
  discount_amount: number;
  price: number;
  qty: number;
};

type ItemDiscount = {
  product_id: number;
  discount_amount: number;
  discount_percent: number | null;
};

export type RuleDiscount = {
  rule_id: number;
  discount_lable: string;
  discount_amount: number;
  is_free_shipping: boolean;
  item_discount: {
    [key: string]: ItemDiscountDetails;
  };
};

export type AppliedAutoDiscountResponse = {
  total_discount: number;
  is_free_shipping: boolean;
  rule_discount: RuleDiscount[];
  product_discount: {
    [key: string]: ItemDiscount;
  };
};
