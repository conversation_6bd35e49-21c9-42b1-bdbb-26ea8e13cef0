export interface BasePromotionProductData {
  free_sku: string;
  qty: number;
  product_id: string | number;
  parent_id: string | number | null;
  price: number;
  start_date: Date;
  end_date: Date;
  name: string;
  tax_amount: number;
  tax_percent: number;
  weight: number;
  url_key: string;
  row_total_incl_tax: number;
  image: string;
}

export interface ItemPromotionProductData extends BasePromotionProductData {
  buy_sku: string;
  buy_qty: number;
  free_qty: number;
  is_multiply: boolean;
}

export interface AmountPromotionProductData extends BasePromotionProductData {}
