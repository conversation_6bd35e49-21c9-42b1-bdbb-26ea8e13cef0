export interface AvailableShippingMethodsRequest {
  customerId: number;
  pincode: number;
  weight: number;
  grandTotal: number;
  countryCode: string;
}

export interface AvailablePaymentMethodsRequest {
  pincode: number;
  isCod: boolean;
  totalWeight: number;
  totalAmount: number;
  countryCode: string;
}

export interface AvailableShippingMethodsV4Request {
  postcode: number;
  country_code: string;
  products: ProductsIdInput;
  cart_data: CartDataInput;
}
interface CartDataInput {
  is_cod_on_cart: boolean;
  cart_weight: number;
  cart_amount: number;
}

interface ProductsIdInput {
  children: number[];
}
export interface AvailableShippingMethodsV5Request {
  postcode: number;
  country_code: string;
  cart_data: CartDataInput;
}

export interface CustomerCouponResponse {
  code: string;
  title: string;
  expiry: string;
}
