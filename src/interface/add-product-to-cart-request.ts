import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayMinSize,
  Min,
  IsBoolean,
} from 'class-validator';
import { discountTypeEnum } from 'src/database/entities/quote_item_extension_attribute';

export class CartSkuQtyInterface {
  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: 'Quantity must be greater than 0' })
  quantity: number;

  @IsNotEmpty()
  @IsString()
  sku: string;

  @IsOptional()
  referral_code?: string;

  @IsOptional()
  @IsNumber()
  parent_id?: number;
}

export class CartItemWrapper {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CartSkuQtyInterface)
  data: CartSkuQtyInterface;
}
export class CartItemsInterface {
  [key: string]: CartSkuQtyInterface;
}

export class AddProductToCartRequest {
  @IsNotEmpty()
  cart_id: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CartItemWrapper)
  cart_items: CartItemWrapper[];
}

export class CartIdQtyInterface {
  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: 'Quantity must be number and greater than 0' })
  quantity: number;

  @IsNotEmpty()
  @IsNumber()
  id: number;
}

export class CartItemUpdateInput {
  @IsNotEmpty()
  @IsNumber()
  cart_item_id: number;

  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: 'Quantity must be number and greater than 0' })
  quantity: number;
}

export class updateCartItemsRqequest {
  @IsNotEmpty()
  cart_id: string;

  @IsNotEmpty()
  @ValidateNested() // Decorator for validating nested objects
  cart_items: CartItemUpdateInput[];
}

export class AddDiscount {
  @IsNotEmpty()
  cart_item_id: number;

  @IsEnum(discountTypeEnum)
  @IsNotEmpty()
  discount_type: discountTypeEnum;

  @IsNotEmpty()
  discount_value: number;

  @IsOptional()
  reason: string;
}

export class UpdateCartItemBody {
  @IsNotEmpty()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CartItemUpdateInput)
  cart_items: CartItemUpdateInput[];

  @IsOptional()
  @IsBoolean()
  buy_now: boolean;
}

export class AddItemsToCartBody {
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CartItemWrapper)
  cart_items: CartItemWrapper[];
}

export class MergeCartsRequestBody {
  @IsNotEmpty()
  @IsString()
  source_cart_id: string;
  @IsNotEmpty()
  @IsString()
  destination_cart_id: string;
}
