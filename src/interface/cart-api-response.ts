import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { CustomerDetails } from './customer';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
export interface CartResponse {
  id: number;
  masked_id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  is_virtual: boolean;
  items_count: number;
  items_qty: number;
  customer:
    | CustomerDetails
    | { email: string; firstname: string; lastname: string };
  items: CartItem[];
  quote_amount_promotions: QuoteAmountPromotion[];
  billing_address: {
    id: number;
    region: string;
    region_id: number;
    region_code: string;
    country_id: string;
    street: string[];
    telephone: string;
    postcode: string;
    city: string;
    firstname: string;
    lastname: string;
    email: string;
    same_as_billing: number;
    save_in_address_book: number;
  };
  orig_order_id: number;

  currency: {
    global_currency_code: string;
    base_currency_code: string;
    store_currency_code: string;
    quote_currency_code: string;
    store_to_base_rate: number;
    store_to_quote_rate: number;
    base_to_global_rate: number;
    base_to_quote_rate: number;
  };
  customer_is_guest: boolean;
  customer_note_notify: boolean;
  customer_tax_class_id: number;
  store_id: number;
  applied_rule_ids: string;
  is_active_membership: boolean;
  extension_attributes: { shipping_assignments: ShippingAssignment[] };
}

// interface QuoteAmountPromotion {
//   id: number;
//   promotion_id: number;
//   qty: number;
//   free_sku: string;
//   end_date: Date;
//   price: number;
// }

export interface CartItem {
  item_id: number;
  sku: string;
  qty: number;
  name: string;
  price: number;
  product_type: string;
  quote_id: string;
  quote_item_promotions: QuoteItemPromotion[];
}

// interface QuoteItemPromotion {
//   id: number;
//   promotion_id: number;
//   qty: number;
//   free_sku: string;
//   end_date: Date;
//   price: number;
// }

export interface ShippingAssignment {
  shipping: {
    address: {
      id: number;
      region: string;
      region_id: number;
      region_code: string;
      country_id: string;
      street: string[];
      telephone: string;
      postcode: string;
      city: string;
      firstname: string;
      lastname: string;
      email: string;
      same_as_billing: number;
      save_in_address_book: number;
    };
    method: string;
  };
  items: CartItem[];
}

interface paymentObjectType {
  code: string;
  title: string;
}

export interface CartPaymentInfoResponse {
  payment_methods: paymentObjectType[];
  totals: {
    coupon_code: string;
    grand_total: number;
    base_grand_total: number;
    subtotal: number;
    base_subtotal: number;
    discount_amount: number;
    base_discount_amount: number;
    subtotal_with_discount: number;
    base_subtotal_with_discount: number;
    shipping_amount: number;
    base_shipping_amount: number;
    shipping_discount_amount: number;
    base_shipping_discount_amount: number;
    tax_amount: number;
    base_tax_amount: number;
    weee_tax_applied_amount: number;
    shipping_tax_amount: number;
    base_shipping_tax_amount: number;
    subtotal_incl_tax: number;
    shipping_incl_tax: number;
    base_shipping_incl_tax: number;
    base_currency_code: string;
    quote_currency_code: string;
    items_qty: number;
    items: PyamentInfoItem[];
    total_segments: PaymentInfoTotalSegments[];
    extension_attributes: {
      mp_membership: string;
    };
  };
}

export interface PyamentInfoItem {
  item_id: number;
  price: number;
  base_price: number;
  qty: number;
  row_total: number;
  base_row_total: number;
  row_total_with_discount: number;
  tax_amount: number;
  base_tax_amount: number;
  tax_percent: number;
  discount_amount: number;
  base_discount_amount: number;
  discount_percent: number;
  price_incl_tax: number;
  base_price_incl_tax: number;
  row_total_incl_tax: number;
  base_row_total_incl_tax: number;
  options: string;
  weee_tax_applied_amount: number;
  weee_tax_applied: number;
  name: string;
}

export interface PaymentInfoTotalSegments {
  code: string;
  title: string;
  value: number;
  area?: string;
}
