import {
  <PERSON>dress<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Billing<PERSON>ddressDto,
} from './set-billing-address-on-cart';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  ValidateNested,
  ArrayMinSize,
  IsString,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export interface SetShiipingAddressOnCartRequest {
  cart_id: string;
  shipping_addresses: CartAddress[];
  billing_address?: BillingAddressDto;
}

export class SetShiipingAddressOnCartBody {
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CartAddress)
  shipping_addresses: CartAddress[];

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  billing_address?: BillingAddressDto;

  @IsOptional()
  @IsBoolean()
  buy_now: boolean;
}

export class SetEmailOnGuestCartBody {
  @IsNotEmpty()
  @IsString()
  email: string;
}
