import {
  Cart<PERSON>temsInterface,
  CartItemWrapper,
} from './add-product-to-cart-request';
import { CartAddress } from './set-billing-address-on-cart';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  ValidateNested,
  IsString,
} from 'class-validator';

export interface BuyNowProdRequest {
  cart_items: CartItemWrapper[];
  shipping_address: CartAddress;
}

export class BuyNowProdRequestBody {
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CartItemWrapper)
  cart_items: CartItemWrapper[];

  @IsString()
  country_code: boolean;
}

export class BuyNowBody {
  @IsOptional()
  @IsBoolean()
  buy_now: boolean;
}
