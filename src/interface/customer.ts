export interface CustomerDetails {
  id: number;
  group_id: number;
  default_billing: string;
  default_shipping: string;
  created_at: string;
  updated_at: string;
  created_in: string;
  email: string;
  firstname: string;
  lastname: string;
  magento_customer_id: number;
  gender: number;
  store_id: number;
  taxvat: string;
  website_id: 1;
  addresses: CustomerAddress[];
  disable_auto_group_change: number;
  extension_attributes: {
    is_subscribed: boolean;
  };
  custom_attributes: CustomerCustomAttribute[];
}

export interface CustomerCustomAttribute {
  attribute_code: 'register_platform' | 'register_type';
  value: 'email' | 'web';
}

export interface CustomerAddress {
  id: number;
  customer_id: number;
  region: {
    region_code: string;
    region: string;
    region_id: number;
  };
  region_id: number;
  country_id: string;
  street: string;
  telephone: string;
  postcode: string;
  latitude: number;
  longitude: number;
  tag: string;
  map_address: string;
  customer_street_2:string;
  city: string;
  firstname: string;
  lastname: string;
  default_shipping?: boolean;
  default_billing?: boolean;
}
