import { QuoteDiscountsObject } from './discount';

export interface DiscountValues {
  discount_amount: number;
  discount_type: string;
  coupon_code?: string;
  discountRows: QuoteDiscountsObject[];
  discountItems?: any;
}
export interface RuleDiscount extends QuoteDiscountsObject {
  quote_item_id: number;
  discount_percent: number;
}

export interface ItemDiscount {
  [key: string]: QuoteItemDiscount;
}

export interface QuoteItemDiscount {
  discount_amount: number;
  discount_percent: number;
  admin_discount_amount?: number;
  admin_discount_percent?: number;
}
