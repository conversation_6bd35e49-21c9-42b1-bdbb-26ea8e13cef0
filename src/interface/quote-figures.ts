type QuoteFigures = {
  grandTotal: number;
  totalWeight: number;
  itemsQty: number;
  subtotalWithDiscount: number;
  grandTotalExcludingVirtualProduct: number;
  totalWeightExcludingVirtualProduct: number;
  overWeightDeliveryCharges: number;
  total_items_savings: number;
  admin_discount: number;
  reward_discount: number;
  auto_discount: number;
  delivery_charges: number;
  subtotalExcludingTax: number;
  grandTotalForCoupon: number;
  adminItemWiseDiscount: Record<
    number,
    {
      admin_discount_amount: number;
      admin_discount_percent: number;
    }
  >;
};
