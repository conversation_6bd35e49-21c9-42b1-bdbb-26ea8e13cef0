export interface GlobalCurrencyConfiguration {
  base_currency_code: string;
  base_currency_symbol: string;
  default_display_currency_code: string;
  default_display_currency_symbol: string;
  available_currency_codes: string;
  exchange_rates: {
    currency_to: string;
    rate: number;
  };
}

export interface ProductData {
  id: number;
  thumbnail_url: string;
  sku: string;
  url_key: string;
  weight: number;
  reward_point_product: number;
  pd_expiry_date: string;
  type_id: string;
  tax_class_id: string;
  name: string;
  short_description: string;
  is_in_stock: boolean;
  max_sale_qty: number;
  price: {
    minimalPrice: {
      adjustments: string[];
      amount: {
        currency: string;
        currency_symbol: string;
        value: number;
      };
    };
    regularPrice: {
      adjustments: string[];
      amount: {
        currency: string;
        currency_symbol: string;
        value: number;
      };
    };
  };
  media_gallery_entries: {
    disabled: boolean;
    file: string;
    id: number;
    label: string;
    media_type: string;
    position: number;
    types: string[];
    video_content: ProductMediaGalleryEntriesVideoContent[];
  };
  average_rating: string;
  categories: CategoryInterface[];
  demo_available: string;
  dentalkart_custom_fee: number;
  dispatch_days: number;
  image_url: string;
  is_cod: string;
  manufacturer: string;
  meta_description: string;
  meta_keyword: string;
  meta_title: string;
  msrp: number;
  rating_count: string;
  special_price: number;
  tier_prices: ProductTierPrices[];
  min_sale_qty: number;
  qty: number;
  status: number;
  international_active: number;
  backorders: number;
  is_free_product?: boolean;
  parent_id?: string;
}

export interface productsGQLResponse {
  productData: ProductData[];
}

export interface ProductMediaGalleryEntriesVideoContent {
  media_type: string;
  video_description: string;
  video_metadata: string;
  video_provider: string;
  video_title: string;
  video_url: string;
}

export interface ProductTierPrices {
  customer_group_id: string;
  percentage_value: number;
  qty: number;
  value: number;
  website_id: number;
}

export interface CategoryInterface {
  app_link: string;
  id: number;
  level: number;
  name: string;
  position: number;
  url_path: string;
}

export interface AvailableShippingMethod {
  method_code: string;
  carrier_code: string;
  carrier_title: string;
  method_title: string;
  charges: number;
  currency: string;
  sort_order: number;
}

export interface GetShippingmethodsResponse {
  GetShippingMethod: AvailableShippingMethod[];
}

export interface getAvailablePaymentMethodV4Response {
  getAvailablePaymentMethodV4: {
    payment_methods: PaymentMethod[];
  };
}

export interface PaymentMethod {
  code: string;
  title: string;
}
