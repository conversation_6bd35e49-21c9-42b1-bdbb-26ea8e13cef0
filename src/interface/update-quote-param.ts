import { Quote } from 'src/database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { DiscountValues } from './quote-discount-object';
import { ProductData } from './graphql-response';
import {
  ParentUnitPriceInterface,
  SkuWiseParentIdAndQTy,
} from './parent-mapped-price';
import { CartAction, OutputResponseType } from 'src/config/constants';
import { ItemPromotion } from 'src/database/entities/item_promotion';

type Filter = {
  is_active: boolean;
};

export interface UpdateQuoteParam {
  customerGroupId: number;
  allItems: QuoteItem[];
  quote: Quote;
  billingAddress: QuoteAddress;
  shippingAddress: QuoteAddress;
  t1?: any;
  autoDiscount: any;
  throwError?: boolean;
  couponDiscount?: DiscountValues;
  rewardDiscount?: DiscountValues;
  magentoCustomerId?: number;
  newAmountPromFreeProduct: QuoteItem[];
  newAmountPromFreeProductDetails: ProductData[];
  skuwiseParentIdQty?: SkuWiseParentIdAndQTy;
  parentMappedPrice?: ParentUnitPriceInterface;
  productDetails: ProductData[];
  quote_filter?: Filter;
  outputResponseType?: OutputResponseType;
}

export interface UpdateQuoteParamV2 {
  customerGroupId: number;
  allItems: QuoteItem[];
  quote: Quote;
  billingAddress: QuoteAddress;
  shippingAddress: QuoteAddress;
  throwError?: boolean;
  rewardDiscount?: DiscountValues;
  skuwiseParentIdQty?: SkuWiseParentIdAndQTy;
  parentMappedPrice?: ParentUnitPriceInterface;
  productDetails: ProductData[];
  quote_filter?: Filter;
  couponCode: string;
  customerId: number;
  countryId: string;
  regionId: string;
  isCouponRemove?: boolean;
  isCouponApply?: boolean;
  cartAction: CartAction;
  outputResponseType?: OutputResponseType;
  appliedPoints?: number;
  itemPromotions?: ItemPromotion[];
  skuWiseErrors?: any;
  productTaxes: any;
}
