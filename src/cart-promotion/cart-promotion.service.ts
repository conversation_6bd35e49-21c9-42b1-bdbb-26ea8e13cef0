import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import {
  promotionsError,
  promotionsMsg,
  SERVER_ADDED_HOURS,
} from 'src/config/constants';
import { AmountPromotion } from 'src/database/entities/amount_promotion';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import { PromotionListForReturn } from 'src/interface/promotion-list-for-return';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { logger } from 'src/utils/service-logger';
import { BulkUpdatePromotion } from './cart-promotion.dto';
import { PromotionHelper } from 'src/utils/promotions.helper';

@Injectable()
export class CartPromotionService {
  constructor(
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly promotionsHelper: PromotionHelper,
    @Inject('SEQUELIZE') private sequelize: Sequelize,
  ) {}
  async getAllCartPromotions(
    page: number,
    size: number,
    promotion_type: 'item_promotion' | 'amount_promotion' | undefined,
    start_date: string,
    end_date: string,
    status: string | undefined,
    searchQuery: string,
  ) {
    const offset = page !== undefined ? page * (size || 20) : 0;

    const { query, count } = await this.getQuery(
      promotion_type,
      offset,
      size,
      status,
      start_date,
      end_date,
      searchQuery,
    );
    const [promotions] = await this.sequelize.query(query);

    const skuList: string[] = [];

    promotions.forEach((promotion: ItemPromotion | AmountPromotion) => {
      if ('product_sku' in promotion) {
        skuList.push(promotion.product_sku);
      }
      if ('free_product_sku' in promotion) {
        skuList.push(promotion.free_product_sku);
      }
    });

    const prodData = await this.externalApiHelper.getProductMetadata(skuList);
    const skuMap = prodData.reduce(
      (prodMap, prod) => ({ ...prodMap, [prod.sku]: prod }),
      {},
    );

    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const todayDate = new Date(updatedTime);

    const data = promotions.map(
      (promotion: ItemPromotion | AmountPromotion) => {
        const isItemPromotion = (promotion as any).type === 'item_promotion';
        const promotionEndDate = new Date(promotion.end_date);
        promotionEndDate.setUTCHours(23, 59, 59, 999);
        const expired = promotionEndDate < todayDate;
        return {
          ...promotion,
          parent_product: isItemPromotion
            ? skuMap[promotion.product_sku]
            : null,
          free_product: isItemPromotion
            ? skuMap[(promotion as any).free_product_sku]
            : null,
          product: !isItemPromotion ? skuMap[promotion.product_sku] : null,
          expired: expired,
          is_item_promotion: isItemPromotion,
        };
      },
    );

    return {
      page: page,
      totalPages: Math.ceil(Math.max(count) / size),
      count,
      rows: data,
    };
  }
  async getQuery(
    promotion_type: 'item_promotion' | 'amount_promotion' | undefined,
    offset: number,
    size: number,
    status: string | undefined,
    startDate: string,
    endDate: string,
    searchQuery: string,
  ) {
    let query;
    let itemPromotionsCount = 0;
    let amountPromotionsCount = 0;

    const getDateFilter = (
      startDate: string | null,
      endDate: string | null,
      isStatusActive: string | undefined,
    ) => {
      let filter = '';

      if (isStatusActive !== undefined) {
        const currentDate = new Date().toISOString().slice(0, 10);
        if (isStatusActive === 'active') {
          filter += `end_date >= '${currentDate}'`;
          if (startDate || endDate) filter += ' AND ';
        } else {
          filter += `end_date < '${currentDate}'`;
          if (startDate || endDate) filter += ' OR ';
        }
      }

      if (startDate && endDate) {
        filter += `(start_date >= '${startDate}' AND end_date <= '${endDate}')`;
      } else if (startDate) {
        filter += `(start_date >= '${startDate}')`;
      } else if (endDate) {
        filter += `(end_date <= '${endDate}')`;
      }

      return filter;
    };
    const dateFilter = getDateFilter(startDate, endDate, status);
    const deletedFilter = 'deleted_at IS NULL';

    const getItemSearchFilter = (searchQuery: string) => {
      return searchQuery
        ? `(promotion_name LIKE '%${searchQuery}%' OR product_sku LIKE '%${searchQuery}%' OR free_product_sku LIKE '%${searchQuery}%')`
        : '';
    };

    const getAmountSearchFilter = (searchQuery: string) => {
      return searchQuery
        ? `(promotion_name LIKE '%${searchQuery}%' OR product_sku LIKE '%${searchQuery}%')`
        : '';
    };

    const itemSearchFilter = getItemSearchFilter(searchQuery);
    const amountSearchFilter = getAmountSearchFilter(searchQuery);

    // ITEM PROMOTION QUERY
    if (promotion_type === 'item_promotion') {
      query = `
        SELECT
            *
        FROM (
            SELECT
                promotion_id,
                promotion_name,
                product_sku,
                NULL AS parent_sku,
                parent_id,
                NULL AS min_order_amount,
                buy_qty,
                free_product_qty,
                free_product_sku,
                is_multiply,
                is_free_product_in_stock,
                start_date,
                end_date,
                created_by,
                created_at,
                updated_at,
                deleted_at,
                'item_promotion' AS type
            FROM
                item_promotions
            WHERE
            ${dateFilter ? `${dateFilter} AND ` : ''}${itemSearchFilter}${
        itemSearchFilter && (dateFilter || itemSearchFilter) ? ' AND ' : ''
      }${deletedFilter}
        ) AS combined_promotions
        ORDER BY
            created_at DESC
        LIMIT ${offset}, ${size || 10};
        `;
      itemPromotionsCount = await ItemPromotion.count({
        paranoid: false,
        where: {
          [Op.and]: dateFilter
            ? [{ deleted_at: null }, Sequelize.literal(dateFilter)]
            : { deleted_at: null },
          [Op.or]: {
            promotion_name: { [Op.like]: `%${searchQuery ?? ''}%` },
            product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
            free_product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
          },
        },
      });
    }
    // AMOUNT PROMOTIONS QUERY
    else if (promotion_type === 'amount_promotion') {
      query = `
        SELECT
            *
        FROM (
            SELECT
                amount_promotion_id AS promotion_id,
                promotion_name,
                product_sku,
                NULL AS parent_sku,
                NULL AS parent_id,
                min_order_amount,
                buy_qty,
                NULL AS free_product_qty,
                NULL AS free_product_sku,
                NULL AS is_multiply,
                NULL AS is_free_product_in_stock,
                start_date,
                end_date,
                created_by,
                created_at,
                updated_at,
                deleted_at,
                'amount_promotion' AS type
            FROM
                amount_promotions
            WHERE
            ${dateFilter ? `${dateFilter} AND ` : ''}${amountSearchFilter}${
        amountSearchFilter && (dateFilter || amountSearchFilter) ? ' AND ' : ''
      }${deletedFilter}
        ) AS combined_promotions
        ORDER BY
            created_at DESC
        LIMIT ${offset}, ${size || 10};
        `;
      amountPromotionsCount = await AmountPromotion.count({
        paranoid: false,
        where: {
          [Op.and]: dateFilter
            ? [{ deleted_at: null }, Sequelize.literal(dateFilter)]
            : { deleted_at: null },
          [Op.or]: {
            promotion_name: { [Op.like]: `%${searchQuery ?? ''}%` },
            product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
          },
        },
      });
    }
    // ELSE, COMBINED QUERY
    else {
      query = `
        SELECT
            *
        FROM (
            SELECT
                promotion_id,
                promotion_name,
                product_sku,
                NULL AS parent_sku,
                parent_id,
                NULL AS min_order_amount,
                buy_qty,
                free_product_qty,
                free_product_sku,
                is_multiply,
                is_free_product_in_stock,
                start_date,
                end_date,
                created_by,
                created_at,
                updated_at,
                deleted_at,
                'item_promotion' AS type
            FROM
                item_promotions
            WHERE
            ${dateFilter ? `${dateFilter} AND ` : ''}${itemSearchFilter}${
        itemSearchFilter && (dateFilter || itemSearchFilter) ? ' AND ' : ''
      }${deletedFilter}
            UNION ALL
            SELECT
                amount_promotion_id AS promotion_id,
                promotion_name,
                product_sku,
                NULL AS parent_sku,
                NULL AS parent_id,
                min_order_amount,
                buy_qty,
                NULL AS free_product_qty,
                NULL AS free_product_sku,
                NULL AS is_multiply,
                NULL AS is_free_product_in_stock,
                start_date,
                end_date,
                created_by,
                created_at,
                updated_at,
                deleted_at,
                'amount_promotion' AS type
            FROM
                amount_promotions
            WHERE
            ${dateFilter ? `${dateFilter} AND ` : ''}${amountSearchFilter}${
        amountSearchFilter && (dateFilter || amountSearchFilter) ? ' AND ' : ''
      }${deletedFilter}
        ) AS combined_promotions
        ORDER BY
            created_at DESC
        LIMIT ${offset}, ${size || 10};
        `;
      amountPromotionsCount = await AmountPromotion.count({
        paranoid: false,
        where: {
          [Op.and]: dateFilter
            ? [{ deleted_at: null }, Sequelize.literal(dateFilter)]
            : { deleted_at: null },
          [Op.or]: {
            promotion_name: { [Op.like]: `%${searchQuery ?? ''}%` },
            product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
          },
        },
      });
      itemPromotionsCount = await ItemPromotion.count({
        paranoid: false,
        where: {
          [Op.and]: dateFilter
            ? [{ deleted_at: null }, Sequelize.literal(dateFilter)]
            : { deleted_at: null },
          [Op.or]: {
            promotion_name: { [Op.like]: `%${searchQuery ?? ''}%` },
            product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
            free_product_sku: { [Op.like]: `%${searchQuery ?? ''}%` },
          },
        },
      });
    }

    return { query, count: itemPromotionsCount + amountPromotionsCount };
  }

  async getPromotionListForReturnValidation(data: PromotionListForReturn) {
    const orderDate = new Date(data.order_date);
    const amountPromotions = await AmountPromotion.findOne({
      where: {
        start_date: {
          [Op.lte]: orderDate,
        },
        end_date: {
          [Op.gte]: orderDate,
        },
        min_order_amount: {
          [Op.lte]: data.order_amount,
        },
      },
      order: [['min_order_amount', 'DESC']],
      paranoid: false,
    });

    const itemPromotions = await ItemPromotion.findAll({
      where: {
        [Op.or]: {
          product_sku: {
            [Op.in]: data.skus,
          },
          free_product_sku: {
            [Op.in]: data.skus,
          },
        },
        start_date: {
          [Op.lte]: orderDate,
        },
        end_date: {
          [Op.gte]: orderDate,
        },
      },
      paranoid: false,
    });

    return {
      item_promotions: itemPromotions,
      amount_promotion: amountPromotions,
    };
  }

  async bulkUpdatePromotion(data: BulkUpdatePromotion, isDelete: boolean) {
    const { item_promotion_ids, amount_promotion_ids, end_date, created_by } =
      data;

    if (!item_promotion_ids.length && !amount_promotion_ids.length)
      return { message: promotionsError.NO_PROMOTION_IDS_PROVIDED };

    try {
      if (isDelete) {
        if (amount_promotion_ids.length > 0) {
          await AmountPromotion.update(
            { created_by },
            {
              where: { amount_promotion_id: { [Op.in]: amount_promotion_ids } },
            },
          );
          await AmountPromotion.destroy({
            where: { amount_promotion_id: { [Op.in]: amount_promotion_ids } },
          });
        }

        if (item_promotion_ids.length > 0) {
          const itemPromotions = await ItemPromotion.findAll({
            where: { promotion_id: { [Op.in]: item_promotion_ids } },
          });

          const payload = this.promotionsHelper.extensionAttributePayload(
            itemPromotions,
            true,
          );

          this.externalApiHelper.updateProductExtensionAttributes(payload);

          await ItemPromotion.update(
            { created_by },
            {
              where: { promotion_id: { [Op.in]: item_promotion_ids } },
            },
          );

          await ItemPromotion.destroy({
            where: { promotion_id: { [Op.in]: item_promotion_ids } },
          });
        }

        return { message: promotionsMsg.PROMOTION_BULK_DELETE };
      }

      // Bulk update for Item Promotions
      if (item_promotion_ids?.length > 0) {
        const itemPromotions = await ItemPromotion.findAll({
          where: { promotion_id: { [Op.in]: item_promotion_ids } },
        });
        const payload = await this.promotionsHelper.extensionAttributePayload(
          itemPromotions,
          false,
          end_date,
        );

        this.externalApiHelper.updateProductExtensionAttributes(payload);

        await ItemPromotion.update(
          { end_date, created_by },
          {
            where: { promotion_id: { [Op.in]: item_promotion_ids } },
          },
        );
      }

      // Bulk update for Amount Promotions
      if (amount_promotion_ids?.length > 0) {
        await AmountPromotion.update(
          { end_date, created_by },
          {
            where: { amount_promotion_id: { [Op.in]: amount_promotion_ids } },
          },
        );
      }

      return { message: promotionsMsg.PROMOTION_BULK_EXPIRY };
    } catch (error) {
      logger.error('Error updating promotion expiry:', error);
      throw new BadRequestException(
        promotionsError.PROMOTION_BULK_EXPIRY_FAILED,
      );
    }
  }
}
