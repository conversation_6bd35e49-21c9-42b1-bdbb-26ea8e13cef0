import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ParseDatePipe, ParseQueryPipe } from 'src/pipes/query-params.pipe';
import { CartPromotionService } from './cart-promotion.service';
import { PromotionListForReturn } from 'src/interface/promotion-list-for-return';
import { ApiKeyGuard } from 'src/guards/api-key-guard';
import { BulkUpdatePromotion } from './cart-promotion.dto';

@Controller('cart-promotions')
@UseGuards(ApiKeyGuard)
export class CartPromotionController {
  constructor(private readonly cartPromotionService: CartPromotionService) {}

  @Get()
  async getAllCartPromotion(
    @Query('page', new ParseQueryPipe(0)) page: number,
    @Query('size', new ParseQueryPipe(20)) size: number,
    @Query('promotion_type')
    promotion_type: 'item_promotion' | 'amount_promotion' | undefined,
    @Query('status')
    status: string | undefined,
    @Query('start_date', new ParseDatePipe()) start_date: string,
    @Query('end_date', new ParseDatePipe()) end_date: string,
    @Query('query') query: string,
  ) {
    return await this.cartPromotionService.getAllCartPromotions(
      page,
      size,
      promotion_type,
      start_date,
      end_date,
      status,
      query,
    );
  }

  @Post('rules')
  async getPromotionListForReturnValidation(
    @Body() body: PromotionListForReturn,
  ) {
    return await this.cartPromotionService.getPromotionListForReturnValidation(
      body,
    );
  }

  @Post('bulk-update')
  async bulkUpdatePromotion(
    @Body() input: BulkUpdatePromotion,
    @Query('is_delete') is_delete: string,
  ) {
    return await this.cartPromotionService.bulkUpdatePromotion(
      input,
      is_delete === 'true',
    );
  }
}
