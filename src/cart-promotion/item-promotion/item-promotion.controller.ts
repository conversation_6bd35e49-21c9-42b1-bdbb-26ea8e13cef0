import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Query,
  Body,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  CreateItemPromotionDto,
  UpdateItemPromotionDto,
} from './item-promotion.dto';
import { ItemPromotionService } from './item-promotion.service';
import { ParseQueryPipe } from 'src/pipes/query-params.pipe';
import { ApiKeyGuard } from 'src/guards/api-key-guard';
@Controller('item_promotion')
@UseGuards(ApiKeyGuard)
export class ItemPromotionController {
  constructor(private readonly cartPromtion: ItemPromotionService) {}

  @Post()
  async createCartPromotion(@Body() input: CreateItemPromotionDto) {
    return await this.cartPromtion.createCartPromotion(input);
  }

  @Patch(':promotion_id')
  async updateCartPromotion(
    @Param('promotion_id', ParseIntPipe) promotionId: number,
    @Body() input: UpdateItemPromotionDto,
  ) {
    return await this.cartPromtion.updateCartPromotion(promotionId, input);
  }

  @Delete(':promotion_id')
  async deleteCartPromotion(
    @Param('promotion_id', ParseIntPipe) promotionId: number,
  ) {
    return await this.cartPromtion.deleteCartPromotion(promotionId);
  }

  @Get(':promotion_id')
  async getCartPromotion(
    @Param('promotion_id', ParseIntPipe) promotionId: number,
  ) {
    return await this.cartPromtion.getCartPromotion(promotionId);
  }

  @Get()
  async getAllCartPromotion(
    @Query('page', new ParseQueryPipe(0)) page: number,
    @Query('size', new ParseQueryPipe(10)) size: number,
  ) {
    return await this.cartPromtion.getAllCartPromotion(page, size);
  }
}
