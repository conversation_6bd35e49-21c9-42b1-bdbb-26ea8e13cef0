import { Modu<PERSON> } from '@nestjs/common';
import { ItemPromotionController } from './item-promotion.controller';
import { ItemPromotionService } from './item-promotion.service';
import { HttpModule } from '@nestjs/axios';
import { ItemPromotionResolver } from './item-promotion.resolver';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { UtilsModule } from 'src/utils/utils.module';
import { PromotionHelper } from 'src/utils/promotions.helper';

@Module({
  imports: [HttpModule, UtilsModule],
  controllers: [ItemPromotionController],
  providers: [
    ItemPromotionService,
    ItemPromotionResolver,
    ExternalApiHelper,
    PromotionHelper,
  ],
  exports: [ItemPromotionService],
})
export class ItemPromotionModule {}
