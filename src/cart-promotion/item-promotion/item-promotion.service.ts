import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ItemPromotion } from 'src/database/entities/item_promotion';
import {
  CreateItemPromotionDto,
  UpdateItemPromotionDto,
} from 'src/cart-promotion/item-promotion/item-promotion.dto';
import { Op } from 'sequelize';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { logger } from 'src/utils/service-logger';
import { promotionsError, SERVER_ADDED_HOURS } from 'src/config/constants';
import { PromotionHelper } from 'src/utils/promotions.helper';

@Injectable()
export class ItemPromotionService {
  constructor(
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly promotionsHelper: PromotionHelper,
  ) {}

  async createCartPromotion(data: CreateItemPromotionDto) {
    const {
      promotion_name,
      product_sku,
      parent_id,
      buy_qty,
      free_product_sku,
      free_product_qty,
      start_date,
      end_date,
      is_multiply,
      created_by,
    } = data;

    // if (product_sku === free_product_sku)
    //   throw new BadRequestException(promotionsError.SKU_MATCH);

    const itemPromotion = await ItemPromotion.findOne({
      where: { product_sku: product_sku },
    });

    if (itemPromotion) {
      throw new BadRequestException(promotionsError.SKU_EXIST);
    }

    const createdCartPromotion = await ItemPromotion.create({
      promotion_name,
      product_sku,
      parent_id,
      buy_qty,
      free_product_sku,
      free_product_qty,
      start_date,
      end_date,
      is_multiply,
      created_by,
    });

    const payload = this.promotionsHelper.extensionAttributePayload(
      [createdCartPromotion],
      false,
    );

    this.externalApiHelper.updateProductExtensionAttributes(payload);

    if (!createdCartPromotion.promotion_name) {
      await this.updatePromotionName(createdCartPromotion);
      await createdCartPromotion.save();
    }
    console.log(JSON.stringify(data), 'CREATE_ITEM_PROMOTION');
    return createdCartPromotion;
  }

  async updateCartPromotion(
    promotion_id: number,
    data: UpdateItemPromotionDto,
  ) {
    const {
      promotion_name,
      product_sku,
      free_product_sku,
      free_product_qty,
      buy_qty,
      end_date,
      start_date,
      is_multiply,
      is_free_product_in_stock,
      parent_sku,
      parent_id,
      created_by,
    } = data;

    const cartPromotion = await ItemPromotion.findByPk(promotion_id);

    // if (product_sku === free_product_sku)
    //   throw new BadRequestException(promotionsError.SKU_MATCH);

    if (!cartPromotion) {
      throw new NotFoundException(
        `Cart promotion not found with promotion_id ${promotion_id}`,
      );
    }

    const updatedData = cartPromotion.set({
      promotion_name,
      product_sku,
      free_product_sku,
      free_product_qty,
      buy_qty,
      end_date,
      start_date,
      is_multiply,
      is_free_product_in_stock,
      parent_id,
      parent_sku,
      created_by,
    });

    const payload = this.promotionsHelper.extensionAttributePayload(
      [updatedData],
      false,
    );

    this.externalApiHelper.updateProductExtensionAttributes(payload);

    await this.updatePromotionName(cartPromotion);
    await cartPromotion.save();
    console.log(JSON.stringify(data), 'UPDATE_ITEM_PROMOTION');

    return cartPromotion;
  }

  async deleteCartPromotion(promotion_id: number) {
    const cartPromotion = await ItemPromotion.findOne({
      where: { promotion_id },
    });

    if (!cartPromotion) {
      throw new NotFoundException(
        `No cart promotions found with id ${promotion_id}`,
      );
    }

    const payload = this.promotionsHelper.extensionAttributePayload(
      [cartPromotion],
      true,
    );

    this.externalApiHelper.updateProductExtensionAttributes(payload);

    await cartPromotion.destroy();

    return {
      status: 'Ok',
      message: 'Deleted Successfully',
    };
  }

  async getCartPromotion(promotion_id: number) {
    const cartPromotion = await ItemPromotion.findOne({
      where: { promotion_id },
    });

    if (!cartPromotion) {
      throw new NotFoundException(
        `No cart promotions found with id ${promotion_id}`,
      );
    }

    return cartPromotion;
  }

  async getAllCartPromotion(page: number, size: number) {
    let options = {};
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    if (page && size) {
      options = {
        offset:
          page !== undefined && page !== 0 ? (page - 1) * (size || 10) : 0,
        limit: size !== undefined && size !== 0 ? size : undefined,
      };
    }

    const allCartPromotions = await ItemPromotion.findAndCountAll(options);

    if (!allCartPromotions) {
      throw new NotFoundException('No cart promotions data found.');
    }

    const skuList = allCartPromotions.rows.reduce(
      (acc, i) => [...acc, i.free_product_sku, i.product_sku],
      [],
    );

    const prodData = await this.externalApiHelper.getProductMetadata(skuList);

    const skuMap = prodData.reduce(
      (prodMap, prod) => ({ ...prodMap, [prod.sku]: prod }),
      {},
    );

    return {
      page: page,
      totalPages: Math.ceil(allCartPromotions.count / size),
      count: allCartPromotions.count,
      rows: allCartPromotions.rows.map((r) => ({
        ...r.dataValues,
        parent_product: skuMap[r.product_sku],
        free_product: skuMap[r.free_product_sku],
        expired: r.end_date < currentDate,
      })),
    };
  }

  async getAllItemPromotionProductsGQL() {
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    currentDate.setUTCHours(0, 0, 0, 0);
    try {
      const allCartPromotions = await ItemPromotion.findAll({
        attributes: [
          'product_sku',
          'free_product_sku',
          'parent_id',
          'parent_sku',
          'is_free_product_in_stock',
        ],
        where: {
          end_date: {
            [Op.gte]: currentDate,
          },
        },
      });
      const freeProductSkus = allCartPromotions.map((o) => o.free_product_sku);
      const productData =
        await this.externalApiHelper.getProductDataFromSkuForPromotion(
          freeProductSkus,
        );

      if (!productData) {
        return null;
      }
      const filteredProducts = [];
      const updatePromises = [];
      for (const promotion of allCartPromotions) {
        const productDetail = productData.find(
          (p) => p.sku === promotion.free_product_sku,
        );
        if (!productDetail) continue;
        if (promotion.is_free_product_in_stock !== productDetail.is_in_stock) {
          updatePromises.push(
            ItemPromotion.update(
              { is_free_product_in_stock: productDetail.is_in_stock },
              {
                where: { free_product_sku: promotion.free_product_sku },
              },
            ),
          );
        }
        if (promotion.is_free_product_in_stock) {
          const parentIds = promotion.parent_id
            ? promotion.parent_id.split(',')
            : ['0'];
          parentIds.forEach((id) => {
            filteredProducts.push({
              product_sku: promotion.product_sku,
              parent_id: +id,
            });
          });
        }
      }
      if (updatePromises.length) {
        await Promise.all(updatePromises);
      }
      return filteredProducts;
    } catch (error) {
      logger.error('Error in get all item promotion products GQL', error);
      throw new BadRequestException(error);
    }
  }

  async getItemPromotionOffer(sku: string, parent_id: number) {
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    currentDate.setUTCHours(0, 0, 0, 0);
    try {
      if (parent_id) {
        const itemPromotions = await ItemPromotion.findAll({
          where: {
            parent_id: {
              [Op.like]: `%${parent_id}%`,
            },
            is_free_product_in_stock: true,
            start_date: {
              [Op.lte]: currentDate,
            },
            end_date: {
              [Op.gte]: currentDate,
            },
          },
          attributes: [
            'promotion_id',
            'free_product_sku',
            'product_sku',
            'buy_qty',
            'parent_id',
            'free_product_qty',
            'is_multiply',
            'is_free_product_in_stock',
          ],
        });

        if (!itemPromotions.length) {
          return { group_messages: [] };
        }

        const filterdPromWithNoPromName = itemPromotions.filter(
          (o) => !o?.promotion_name,
        );

        if (filterdPromWithNoPromName?.length) {
          const allSkus = filterdPromWithNoPromName.flatMap((promotion) => {
            return [promotion.product_sku, promotion.free_product_sku].filter(
              Boolean,
            );
          });

          const productData =
            await this.externalApiHelper.getProductDataFromSkuForPromotion(
              allSkus,
            );

          itemPromotions.forEach(async (promotion) => {
            const product = productData.find(
              (o) => o.sku === promotion.product_sku,
            );
            if (product) {
              const free_product = productData.find(
                (o) => o.sku === promotion.free_product_sku,
              );
              if (promotion?.is_multiply) {
                promotion.promotion_name = `Buy now and get ${promotion?.free_product_qty} ${free_product?.name} free with every ${promotion?.buy_qty} units of ${product?.name} purchased.`;
              } else {
                promotion.promotion_name = `Get ${promotion.free_product_qty} ${free_product?.name} free on purchase of ${promotion?.buy_qty} ${product?.name}.`;
              }
              await promotion.save();
            }
          });
        }

        const messages = itemPromotions?.map((promotion) => ({
          sku: promotion?.product_sku,
          message: promotion?.promotion_name,
        }));

        return { group_messages: messages };
      }
      // V2
      else {
        const itemPromotion = await ItemPromotion.findOne({
          where: {
            [Op.or]: [{ parent_sku: sku }, { product_sku: sku }],
            is_free_product_in_stock: true,
            start_date: {
              [Op.lte]: currentDate,
            },
            end_date: {
              [Op.gte]: currentDate,
            },
          },
        });

        if (!itemPromotion) {
          return { message: null };
        }
        let offerMessage = itemPromotion?.promotion_name;
        if (!itemPromotion?.promotion_name?.length) {
          const productData =
            await this.externalApiHelper.getProductDataFromSkuForPromotion([
              itemPromotion?.parent_sku
                ? itemPromotion?.parent_sku
                : itemPromotion?.product_sku,
              itemPromotion?.free_product_sku,
            ]);
          let productName;
          let freeProductName;
          productData.forEach((o) => {
            if (
              o?.sku === itemPromotion?.parent_sku ||
              o.sku === itemPromotion?.product_sku
            ) {
              productName = o.name;
            }
            if (o.sku === itemPromotion?.free_product_sku) {
              freeProductName = o?.name;
            }
          });

          if (freeProductName && itemPromotion?.is_multiply) {
            offerMessage = `Buy now and get ${itemPromotion?.free_product_qty} ${freeProductName} free with every ${itemPromotion?.buy_qty} units of ${productName} purchased.`;
          } else {
            offerMessage = `Get ${itemPromotion.free_product_qty} ${freeProductName} free on purchase of ${itemPromotion?.buy_qty} ${productName}`;
          }
          itemPromotion.update({ promotion_name: offerMessage });
          await itemPromotion.save();
        }

        return { message: offerMessage };
      }
    } catch (error) {
      console.log(error);
    }
  }

  async updatePromotionName(promotion: ItemPromotion) {
    const productDetails =
      await this.externalApiHelper.getProductDataFromSkuForPromotion([
        promotion?.product_sku,
        promotion.free_product_sku,
      ]);
    const freeProductDetails = productDetails.find(
      (o) => o.sku === promotion.free_product_sku,
    );
    const mainProductDetails = productDetails.find(
      (o) => o.sku === promotion?.product_sku,
    );

    if (promotion?.is_multiply) {
      promotion.promotion_name = `Buy now and get ${promotion?.free_product_qty} ${freeProductDetails?.name} free with every ${promotion?.buy_qty} units of ${mainProductDetails?.name} purchased.`;
    } else {
      promotion.promotion_name = `Get ${promotion.free_product_qty} ${freeProductDetails?.name} free on purchase of ${promotion?.buy_qty} ${mainProductDetails?.name}.`;
    }
  }
}
