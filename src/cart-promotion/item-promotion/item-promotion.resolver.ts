import { Args, Query, Resolver } from '@nestjs/graphql';
import { ItemPromotionService } from './item-promotion.service';

@Resolver()
export class ItemPromotionResolver {
  constructor(private readonly itemPromotionService: ItemPromotionService) {}

  @Query()
  async getAllItemPromotionProducts() {
    return await this.itemPromotionService.getAllItemPromotionProductsGQL();
  }

  // @Query()
  // async getSkuWisePromotionalOffer(
  //   @Args()
  //   request: {
  //     skus: string[];
  //   },
  // ) {
  //   // return await this.itemPromotionService.getSkuWisePromotionalOffer(
  //   //   request.skus,
  //   // );
  // }

  @Query()
  async getItemPromotionOfferBySku(
    @Args()
    request: {
      sku: string;
      parent_id: number;
    },
  ) {
    return await this.itemPromotionService.getItemPromotionOffer(
      request.sku,
      request.parent_id,
    );
  }
}
