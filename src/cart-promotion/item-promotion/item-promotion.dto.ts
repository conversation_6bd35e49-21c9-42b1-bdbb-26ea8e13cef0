import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsOptional,
  IsDateString,
  IsArray,
} from 'class-validator';

export class CreateItemPromotionDto {
  @IsString()
  @IsNotEmpty()
  promotion_name: string;

  @IsString()
  @IsNotEmpty()
  product_sku: string;

  @IsString()
  @IsOptional()
  parent_id: string;

  @IsNumber()
  @IsNotEmpty()
  buy_qty: number;

  @IsNumber()
  @IsNotEmpty()
  free_product_qty: number;

  @IsString()
  @IsNotEmpty()
  free_product_sku: string;

  @IsString()
  @IsNotEmpty()
  created_by: string;

  @IsDateString()
  @IsNotEmpty()
  start_date: Date;

  @IsDateString()
  @IsNotEmpty()
  end_date: Date;

  @IsBoolean()
  @IsNotEmpty()
  is_multiply: boolean;
}

export class UpdateItemPromotionDto {
  @IsString()
  @IsOptional()
  promotion_name?: string;

  @IsString()
  @IsOptional()
  product_sku?: string;

  @IsString()
  @IsOptional()
  parent_id: string;

  @IsString()
  @IsOptional()
  parent_sku?: string;

  @IsNumber()
  @IsOptional()
  buy_qty?: number;

  @IsNumber()
  @IsOptional()
  free_product_qty?: number;

  @IsString()
  @IsOptional()
  free_product_sku?: string;

  @IsString()
  @IsNotEmpty()
  created_by: string;

  @IsDate()
  @IsOptional()
  start_date?: Date;

  @IsDate()
  @IsOptional()
  end_date?: Date;

  @IsBoolean()
  @IsOptional()
  is_multiply: boolean;

  @IsBoolean()
  @IsOptional()
  is_free_product_in_stock: boolean;
}
