import { CartPromotionService } from './cart-promotion.service';
import { CartPromotionController } from './cart-promotion.controller';
import { Module } from '@nestjs/common';
import { UtilsModule } from 'src/utils/utils.module';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { DatabaseModule } from 'src/database/database.module';
import { PromotionHelper } from 'src/utils/promotions.helper';
// import { Sequelize } from '';

@Module({
  imports: [UtilsModule, DatabaseModule],
  controllers: [CartPromotionController],
  providers: [CartPromotionService, PromotionHelper],
})
export class CartPromotionModule {}
