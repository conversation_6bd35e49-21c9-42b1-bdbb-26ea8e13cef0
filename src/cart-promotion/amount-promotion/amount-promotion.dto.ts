import {
  IsN<PERSON>ber,
  IsString,
  <PERSON>NotEmpty,
  <PERSON>Date,
  IsOptional,
} from 'class-validator';

export class CreateAmountPromotionDto {
  @IsNumber()
  min_order_amount: number;

  @IsString()
  @IsNotEmpty()
  promotion_name: string;

  @IsString()
  @IsNotEmpty()
  product_sku: string;

  @IsNumber()
  buy_qty: number;

  @IsString()
  @IsNotEmpty()
  created_by: string;

  @IsDate()
  start_date: string;

  @IsDate()
  end_date: string;
}

export class UpdateAmountPromotionDto {
  @IsNumber()
  @IsOptional()
  min_order_amount?: number;

  @IsString()
  @IsOptional()
  promotion_name?: string;

  @IsString()
  @IsOptional()
  product_sku?: string;

  @IsNumber()
  @IsOptional()
  buy_qty?: number;

  @IsString()
  @IsNotEmpty()
  created_by: string;

  @IsDate()
  @IsOptional()
  start_date?: string;

  @IsDate()
  @IsOptional()
  end_date?: string;
}
