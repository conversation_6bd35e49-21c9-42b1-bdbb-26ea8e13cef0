import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { AmountPromotion } from 'src/database/entities/amount_promotion';
import {
  CreateAmountPromotionDto,
  UpdateAmountPromotionDto,
} from 'src/cart-promotion/amount-promotion/amount-promotion.dto';
import { Op } from 'sequelize';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { promotionsError, SERVER_ADDED_HOURS } from 'src/config/constants';

@Injectable()
export class AmountPromotionService {
  constructor(private readonly externalApiHelper: ExternalApiHelper) {}
  async createAmountPromotion(data: CreateAmountPromotionDto) {
    const createdAmountPromotion = await AmountPromotion.create({
      min_order_amount: data.min_order_amount,
      product_sku: data.product_sku,
      promotion_name: data.promotion_name,
      buy_qty: data.buy_qty,
      start_date: data.start_date,
      end_date: data.end_date,
      created_by: data.created_by,
    });
    if (!createdAmountPromotion?.promotion_name) {
      createdAmountPromotion.promotion_name = await this.getPromotionName(
        createdAmountPromotion.buy_qty,
        createdAmountPromotion.product_sku,
        createdAmountPromotion.min_order_amount,
      );
      await createdAmountPromotion.save();
    }
    console.log(JSON.stringify(data), 'CREATE_AMOUNT_PROMOTION');

    return createdAmountPromotion;
  }

  async updateAmountPromotion(
    amount_promotion_id: number,
    data: UpdateAmountPromotionDto,
  ) {
    const {
      min_order_amount,
      promotion_name,
      product_sku,
      buy_qty,
      start_date,
      end_date,
      created_by,
    } = data;
    const amountPromotion = await AmountPromotion.findByPk(amount_promotion_id);
    if (!amountPromotion) {
      throw new NotFoundException(
        `Amount promotion not found with amount_promotion_id ${amount_promotion_id}`,
      );
    }
    amountPromotion.set({
      promotion_name,
      product_sku,
      min_order_amount,
      buy_qty,
      end_date,
      start_date,
      created_by,
    });

    amountPromotion.promotion_name = await this.getPromotionName(
      amountPromotion?.buy_qty,
      amountPromotion?.product_sku,
      amountPromotion?.min_order_amount,
    );

    await amountPromotion.save();

    console.log(JSON.stringify(data), 'UPDATE_AMOUNT_PROMOTION');

    return amountPromotion;
  }

  async deleteAmountPromotion(amount_promotion_id: number) {
    const amountPromotion = await AmountPromotion.findByPk(amount_promotion_id);

    if (!amountPromotion) {
      throw new NotFoundException(
        `No amount promotions found with id ${amountPromotion}`,
      );
    }
    console.log(JSON.stringify(amountPromotion), 'DELETE_AMOUNT_PROMOTION');

    await amountPromotion.destroy();

    return {
      status: 'Ok',
      message: 'Deleted Successfully',
    };
  }

  async getAmountPromotion(amount_promotion_id: number) {
    const amountPromotion = await AmountPromotion.findByPk(amount_promotion_id);

    if (!amountPromotion) {
      throw new NotFoundException(
        `No cart promotions found with id ${amountPromotion}`,
      );
    }

    return amountPromotion;
  }

  async getAllAmountPromotion(page: number, size: number) {
    const allAmountPromotions = await AmountPromotion.findAndCountAll({
      offset: page * size, // Default page: 1, Default size: 10
      limit: size, // Default size: 10
    });

    if (!allAmountPromotions) {
      throw new NotFoundException(promotionsError.NO_AMOUNT_PROMOTION_ERROR);
    }

    const skuList = allAmountPromotions.rows.map((i) => i.product_sku);

    const prodData = await this.externalApiHelper.getProductMetadata(skuList);

    const skuMap = prodData.reduce(
      (prodMap, prod) => ({ ...prodMap, [prod.sku]: prod }),
      {},
    );

    return {
      page: page,
      totalPages: Math.ceil(allAmountPromotions.count / size),
      count: allAmountPromotions.count,
      rows: allAmountPromotions.rows.map((r) => ({
        ...r.dataValues,
        product: skuMap[r.product_sku],
        expired: new Date(r.end_date) < new Date(),
      })),
    };
  }

  async getAmountPromotionByCartValue(value: number) {
    const updatedTime = new Date().getTime() + SERVER_ADDED_HOURS;
    const currentDate = new Date(updatedTime);
    currentDate.setUTCHours(0, 0, 0, 0);
    try {
      const amountPromotion = await AmountPromotion.findOne({
        where: {
          min_order_amount: {
            [Op.gt]: value,
          },
          start_date: {
            [Op.lte]: currentDate,
          },
          end_date: {
            [Op.gte]: currentDate,
          },
        },
        order: [['min_order_amount', 'ASC']],
        limit: 1,
      });

      if (!amountPromotion?.promotion_name) {
        amountPromotion.promotion_name = await this.getPromotionName(
          amountPromotion?.buy_qty,
          amountPromotion?.product_sku,
          amountPromotion?.min_order_amount,
        );
        await amountPromotion.save();
      }

      return { message: amountPromotion.promotion_name };
    } catch (error) {
      console.log(error);
    }
  }

  async getPromotionName(
    buyQty: number,
    productSku: string,
    minOrderAmount: number,
  ) {
    const productDetails =
      await this.externalApiHelper.getProductDataFromSkuForPromotion([
        productSku,
      ]);
    const promotionName = `${buyQty} ${productDetails[0].name} With Every Purchase Over ₹${minOrderAmount}`;
    return promotionName;
  }

  async allActiveAmountPromotions() {
    try {
      const currentDate = new Date();
      const { count, rows } = await AmountPromotion.findAndCountAll({
        where: {
          start_date: {
            [Op.lte]: currentDate,
          },
          end_date: {
            [Op.gte]: currentDate,
          },
        },
        order: [['min_order_amount', 'ASC']],
      });

      if (count === 0) {
        return {
          count,
          data: [],
        };
      }

      const skuMap: { [key: string]: String } = {};

      const skus = rows.map((data) => data.product_sku);

      if (skus.length) {
        const productDetails =
          await this.externalApiHelper.getProductDataFromSkuForPromotion([
            ...skus,
          ]);

        if (productDetails.length) {
          for (const data of productDetails) {
            skuMap[data.sku] = data.name;
          }
        }
      }

      const data = rows.map((data) => ({
        buy_qty: data.buy_qty,
        free_sku: data.product_sku,
        min_order_amount: data.min_order_amount,
        message: `${data.buy_qty}  ${
          skuMap[data.product_sku] ?? data.product_sku
        }. Free With Every Purchase Over ₹${data.min_order_amount}`,
      }));

      return {
        count,
        data,
      };
    } catch (error) {
      throw new InternalServerErrorException(error?.message || error);
    }
  }
}
