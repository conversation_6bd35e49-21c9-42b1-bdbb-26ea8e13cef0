import { Module } from '@nestjs/common';
import { AmountPromotionController } from './amount-promotion.controller';
import { AmountPromotionService } from './amount-promotion.service';
import { HttpModule } from '@nestjs/axios';
import { AmountPromotionResolver } from './amount-promotion.resolver';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { UtilsModule } from 'src/utils/utils.module';

@Module({
  imports: [HttpModule, UtilsModule],
  controllers: [AmountPromotionController],
  providers: [
    AmountPromotionService,
    AmountPromotionResolver,
    ExternalApiHelper,
  ],
  exports: [AmountPromotionService],
})
export class AmountPromotionModule {}
