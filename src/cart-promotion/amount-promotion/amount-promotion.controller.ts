import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
} from '@nestjs/common';
import { AmountPromotionService } from './amount-promotion.service';
import {
  CreateAmountPromotionDto,
  UpdateAmountPromotionDto,
} from './amount-promotion.dto';
import { ParseQueryPipe } from 'src/pipes/query-params.pipe';
import { AdminValidationGuard } from 'src/guards/admin-validation.guard';

// @UseGuards(AdminValidationGuard)
@Controller('amount_promotion')
export class AmountPromotionController {
  constructor(private readonly amountPromtion: AmountPromotionService) {}

  @Post()
  async createAmountPromotion(@Body() input: CreateAmountPromotionDto) {
    return await this.amountPromtion.createAmountPromotion(input);
  }

  @Patch(':amount_promotion_id')
  async updateAmountPromotion(
    @Param('amount_promotion_id') amountPromotionId: number,
    @Body() input: UpdateAmountPromotionDto,
  ) {
    return await this.amountPromtion.updateAmountPromotion(
      amountPromotionId,
      input,
    );
  }

  @Delete(':amount_promotion_id')
  async deleteAmountPromotion(
    @Param('amount_promotion_id') amountPromotionId: number,
  ) {
    return await this.amountPromtion.deleteAmountPromotion(amountPromotionId);
  }

  @Get(':amount_promotion_id')
  async getAmountPromotion(
    @Param('amount_promotion_id') amountPromotionId: number,
  ) {
    return await this.amountPromtion.getAmountPromotion(amountPromotionId);
  }

  @Get()
  async getAllAmountPromotion(
    @Query('page', new ParseQueryPipe(0)) page: number,
    @Query('size', new ParseQueryPipe(10)) size: number,
  ) {
    return await this.amountPromtion.getAllAmountPromotion(page, size);
  }
}
