import { Args, Resolver, Query } from '@nestjs/graphql';
import { AmountPromotionService } from './amount-promotion.service';

@Resolver()
export class AmountPromotionResolver {
  constructor(
    private readonly amountPromotionService: AmountPromotionService,
  ) {}

  @Query()
  async getAmountPromotionByCartValue(
    @Args()
    request: {
      value: number;
    },
  ) {
    return await this.amountPromotionService.getAmountPromotionByCartValue(
      request?.value,
    );
  }
}
