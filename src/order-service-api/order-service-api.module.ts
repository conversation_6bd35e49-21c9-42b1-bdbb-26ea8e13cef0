import { Modu<PERSON> } from '@nestjs/common';
import { OrderServiceApiController } from './order-service-api.controller';
import { OrderServiceApiService } from './order-service-api.service';
import { OrderAPiMapper } from '../mapper/order-api-response';
import { UtilsModule } from '../utils/utils.module';
import { OrderServiceAdminController } from './order-service-admin.controller';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { OrderServiceAdminService } from './order-service-admin.service';
import { CartService } from 'src/utils/cart.service';
import { CartAddressService } from 'src/utils/cart-address-service';
import { RewardService } from 'src/utils/reward-service';
import { CouponService } from 'src/utils/coupon-service';
import { DatabaseModule } from 'src/database/database.module';

@Module({
  imports: [UtilsModule, DatabaseModule],
  controllers: [OrderServiceApiController, OrderServiceAdminController],
  providers: [
    OrderServiceApiService,
    OrderAPiMapper,
    ExternalApiHelper,
    OrderServiceAdminService,
    CartService,
    CartAddressService,
    CouponService,
    RewardService,
  ],
  exports: [OrderServiceApiService],
})
export class OrderServiceApiModule {}
