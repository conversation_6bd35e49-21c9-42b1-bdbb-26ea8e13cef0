import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Quote } from '../database/entities/quote';
import { QuoteItem } from 'src/database/entities/quote_item';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { OrderAPiMapper } from '../mapper/order-api-response';
import { ExternalApiHelper } from '../utils/external-api.helper';
import { AddressTypes } from '../config/constants';
import { logger } from '../utils/service-logger';
import { QuoteItemExtensionAttribute } from 'src/database/entities/quote_item_extension_attribute';
import { CustomerDetails } from 'src/interface/customer';
import { QuoteExtraInfo } from '../database/entities/quote_extra_info';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';

@Injectable()
export class OrderServiceApiService {
  constructor(
    private readonly orderAPiMapper: OrderAPiMapper,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}
  async incativeCart(id: string) {
    try {
      const isExistCart = await Quote.findOne({
        where: { masked_id: id, is_active: true },
      });
      if (!isExistCart)
        return new NotFoundException('Requested cart not found');
      await Quote.update(
        {
          is_active: false,
        },
        {
          where: { quote_id: isExistCart.quote_id },
        },
      );
      this.externalApiHelper.inactiveCart(id);
      return true;
    } catch (e) {
      logger.error('Error orderService incativeCart api', e);
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  getUserCart = async (cart_id: string, customer: CustomerDetails) => {
    try {
      const customerCartExist = await Quote.findOne({
        where: { masked_id: cart_id, customer_id: customer.id },
        include: [
          {
            model: QuoteItem,
            include: [
              { model: QuoteItemExtensionAttribute },
              { model: QuoteItemPromotion },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
        ],
      });
      if (!customerCartExist) throw new NotFoundException('Cart not found');
      return this.orderAPiMapper.cartResponse(customerCartExist, customer);
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  };

  getGuestCart = async (cart_id: string) => {
    try {
      const cartExist = await Quote.findOne({
        where: { masked_id: cart_id },
        include: [
          {
            model: QuoteItem,
            include: [
              { model: QuoteItemExtensionAttribute },
              { model: QuoteItemPromotion },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
        ],
      });
      if (!cartExist) throw new NotFoundException('Cart not found');
      return this.orderAPiMapper.cartResponse(cartExist);
    } catch (e) {
      logger.error('Error orderService getGuestCart api', e);
      throw new InternalServerErrorException(e?.message || e);
    }
  };

  async getpaymentInformation(
    cart_id: string,
    customer_id: number,
    isAdminRequest?: boolean,
  ) {
    try {
      let cartExists = null;
      if (customer_id) {
        cartExists = await Quote.findOne({
          where: {
            masked_id: cart_id,
            customer_id: customer_id,
          },
          include: [
            {
              model: QuoteItem,
              include: [
                { model: QuoteItemExtensionAttribute },
                { model: QuoteItemPromotion },
              ],
            },
            QuoteAmountPromotion,
            QuoteAddress,
          ],
        });
      } else if (cart_id) {
        cartExists = await Quote.findOne({
          where: { masked_id: cart_id },
          include: [QuoteItem, QuoteAddress],
        });
      }

      if (!cartExists) throw new NotFoundException('Cart not found');
      const shippingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );
      const billingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );
      let availablePaymentMethods =
        await this.externalApiHelper.getAvailablePaymentMethodV4({
          postcode:
            +shippingAddress?.customer_postcode ||
            +billingAddress?.customer_postcode,
          country_code:
            shippingAddress?.customer_country_id ||
            billingAddress?.customer_country_id,
          cart_data: {
            is_cod_on_cart: cartExists.items.every((o) => o.is_cod),
            cart_weight: +(cartExists.total_weight * 1000).toFixed(2),
            cart_amount: cartExists.grand_total,
          },
          products: {
            children: [
              +cartExists.items.find((item) => item.product_id !== undefined)
                ?.product_id || 0,
            ],
          },
        });

      if (isAdminRequest) {
        availablePaymentMethods = availablePaymentMethods
          ? availablePaymentMethods.filter(
              (item) => item.code === 'cashondelivery',
            )
          : [];
        availablePaymentMethods.push({
          title: 'Prepaid Order',
          code: 'prepaid',
        });
      }
      return await this.orderAPiMapper.buildPaymentInformation(
        cartExists,
        availablePaymentMethods,
      );
    } catch (e) {
      logger.error('Error orderService getpaymentInformation api', e);
      throw new InternalServerErrorException(e?.message || e);
    }
  }

  async fetchCartAndPaymentInformation(
    cart_id: string,
    customer?: CustomerDetails,
    is_buy_now_cart?: boolean,
  ) {
    const cartExists = await Quote.findOne({
      where: {
        masked_id: cart_id,
        customer_id: +customer?.id || null,
        is_active: is_buy_now_cart ? false : true,
      },
      include: [
        {
          model: QuoteItem,
          include: [
            { model: QuoteItemExtensionAttribute },
            { model: QuoteItemPromotion },
          ],
        },
        QuoteAmountPromotion,
        QuoteAddress,
      ],
    });

    if (!cartExists) throw new NotFoundException('Cart not found');

    const shippingAddress = cartExists.addresses?.find(
      (o) => o.address_type === AddressTypes.SHIPPING,
    );
    const billingAddress = cartExists.addresses?.find(
      (o) => o.address_type === AddressTypes.BILLING,
    );
    const availablePaymentMethods =
      await this.externalApiHelper.fetchPaymentMethods({
        postcode:
          +shippingAddress?.customer_postcode ||
          +billingAddress?.customer_postcode,
        country_code:
          shippingAddress?.customer_country_id ||
          billingAddress?.customer_country_id,
        cart_data: {
          is_cod_on_cart: cartExists.items.every((o) => o.is_cod),
          cart_weight: +(cartExists.total_weight * 1000).toFixed(2),
          cart_amount: cartExists.grand_total,
        },
      });

    const payment_info = await this.orderAPiMapper.buildPaymentInformation(
      cartExists,
      availablePaymentMethods,
    );

    return {
      cart_info: this.orderAPiMapper.cartResponse(cartExists, customer),
      payment_info,
    };
  }

  async inActiveCart(id: string, is_buy_now: boolean) {
    try {
      const isExistCart = await Quote.findOne({
        where: { masked_id: id, is_active: is_buy_now ? false : true },
      });
      if (!isExistCart) throw new NotFoundException('Requested cart not found');

      if (is_buy_now) {
        await QuoteExtraInfo.update(
          {
            is_active: false,
          },
          {
            where: { quote_id: isExistCart.quote_id },
          },
        );

        return true;
      }

      await Quote.update(
        {
          is_active: false,
        },
        {
          where: { quote_id: isExistCart.quote_id },
        },
      );

      return true;
    } catch (e) {
      throw new InternalServerErrorException(e?.message || e);
    }
  }
}
