import { BadRequestException, Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>per } from 'src/utils/mask.helper';
import { CartMapper } from 'src/mapper/cart.mapper';
import { GlobalCurrencyConfiguration } from 'src/interface/graphql-response';
import { CustomerDetails } from 'src/interface/customer';
import { Quote } from 'src/database/entities/quote';
import { AddDiscount } from 'src/interface/add-product-to-cart-request';
import { QuoteItem } from 'src/database/entities/quote_item';
import {
  QuoteItemExtensionAttribute,
  discountTypeEnum,
} from 'src/database/entities/quote_item_extension_attribute';

@Injectable()
export class OrderServiceAdminService {
  constructor(
    private readonly maskHelper: MaskHelper,
    private readonly cartMapper: CartMapper,
  ) {}

  async addDiscount(data: AddDiscount) {
    const cartItem = await QuoteItem.findByPk(data.cart_item_id, {
      include: [{ model: Quote }],
    });
    if (!cartItem) throw new BadRequestException('invalid cart item id');

    const discountAmount =
      data.discount_type === discountTypeEnum.FIXED
        ? data.discount_value
        : cartItem.qty * cartItem.price_incl_tax * (data.discount_value / 100); //percentage

    if (discountAmount > Number(cartItem.row_total_incl_tax)) {
      throw new BadRequestException(
        'discount cannot be greater than item price',
      );
    }

    const [quoteItemExtensionAttribute, isCreated] =
      await QuoteItemExtensionAttribute.findOrCreate({
        where: { quote_item_id: data.cart_item_id },
        defaults: {
          admin_discount_amount: discountAmount,
          admin_discount_percent:
            (discountAmount / Number(cartItem.row_total_incl_tax)) * 100,
          discount_type: data.discount_type,
          quote_item_id: data.cart_item_id,
        },
      });

    if (
      !isCreated &&
      quoteItemExtensionAttribute.admin_discount_amount !== data.discount_value
    ) {
      quoteItemExtensionAttribute.set({
        admin_discount_amount: discountAmount,
        admin_discount_percent:
          (discountAmount / Number(cartItem.row_total_incl_tax)) * 100,
        discount_type: data.discount_type,
      });

      await quoteItemExtensionAttribute.save();
    }

    return quoteItemExtensionAttribute;
  }
}
