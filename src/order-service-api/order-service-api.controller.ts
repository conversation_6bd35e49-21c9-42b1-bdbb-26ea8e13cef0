import {
  Controller,
  Param,
  Put,
  Get,
  BadRequestException,
  Req,
  UseGuards,
} from '@nestjs/common';
import { OrderServiceApiService } from './order-service-api.service';
import { ExternalApiHelper } from '../utils/external-api.helper';
import { InactiveCartGuard } from '../guards/admin-token-guard';

@Controller()
export class OrderServiceApiController {
  constructor(
    private readonly orderServiceApiService: OrderServiceApiService,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  getUserToken(authHeader: string) {
    const token = authHeader?.split(' ')[1];
    if (token === 'null') return undefined;
    return token;
  }

  @UseGuards(InactiveCartGuard)
  @Put('cart/:id/inactive')
  async incativeCartId(@Param() { id }) {
    if (!id) throw new BadRequestException('Cart id is mandatory');
    return await this.orderServiceApiService.incativeCart(id);
  }

  @Get('/carts/mine/:id')
  async getCustomerCartInfo(@Req() request, @Param() { id }) {
    const authToken = this.getUserToken(request.headers['authorization']);
    if (!authToken) {
      throw new BadRequestException(
        'User token is mandatory to access resource',
      );
    }
    if (!id) throw new BadRequestException('cartId not provided');
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');

    return await this.orderServiceApiService.getUserCart(id, customerExists);
  }

  @Get('/guest-carts/:id')
  async getGuestCart(@Param() { id }) {
    if (!id) throw new BadRequestException('Cart id is mandatory1');
    return await this.orderServiceApiService.getGuestCart(id);
  }

  @Get('/carts/mine/:id/payment-information')
  async getPaymentInformation(@Req() request, @Param() { id }) {
    const authToken = this.getUserToken(request.headers['authorization']);
    if (!authToken) {
      throw new BadRequestException(
        'User token is mandatory to access resource',
      );
    }
    if (!id) throw new BadRequestException('cartId not provided');

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.orderServiceApiService.getpaymentInformation(
      id,
      customerExists.id,
    );
  }

  @Get('/guest-carts/:id/payment-information')
  async guestPaymentInformation(@Param() { id }) {
    if (!id) throw new BadRequestException('Cart id is mandatory1');
    return await this.orderServiceApiService.getpaymentInformation(id, null);
  }
}
