import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { ExternalApiHelper } from 'src/utils/external-api.helper';
import { OrderServiceApiService } from './order-service-api.service';
import { Quote } from 'src/database/entities/quote';
import { OrderServiceAdminService } from './order-service-admin.service';
import {
  AddDiscount,
  AddProductToCartRequest,
  updateCartItemsRqequest,
} from 'src/interface/add-product-to-cart-request';
import { CartService } from 'src/utils/cart.service';
import { SetBillingAddressOnCartInput } from 'src/interface/set-billing-address-on-cart';
import { CartAddressService } from 'src/utils/cart-address-service';
import { SetShiipingAddressOnCartRequest } from 'src/interface/set-shipping-address-on-cart-request';
import { SetCouponCodeToCart } from 'src/interface/set-coupon-cart-to-cart';
import { CouponService } from 'src/utils/coupon-service';
import { RewardService } from 'src/utils/reward-service';
import { RemoveItemFromCartRequest } from 'src/interface/remove-item-from-cart-request';
import { MergeCartsRequest } from 'src/interface/merge-carts-request';
import { RemoveCouponFromCart } from 'src/interface/remove-coupon-from-cart';

@Controller('/admin')
export class OrderServiceAdminController {
  constructor(
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly orderServiceApiService: OrderServiceApiService,
    private readonly orderServiceAdminService: OrderServiceAdminService,
    private readonly cartService: CartService,
    private readonly cartAddressService: CartAddressService,
    private readonly couponService: CouponService,
    private readonly rewardService: RewardService,
  ) {}

  @Get('/cart/:customer_id/:cart_id')
  async getAvailableCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Param('cart_id') cart_id: string,
    @Query('get_order_res') order_res,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) {
      throw new BadRequestException('unable to get customer');
    }

    if (order_res)
      return await this.orderServiceApiService.getUserCart(cart_id, customer);
    return await this.cartService.getCartData(
      cart_id,
      customer_id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Get('/cart/payment-information/:customer_id/:cart_id')
  async getPaymentInformation(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Param('cart_id') cart_id: string,
  ) {
    return await this.orderServiceApiService.getpaymentInformation(
      cart_id,
      customer_id,
      true,
    );
  }

  @Post('/create-cart/:customer_id')
  async createCart(@Param('customer_id', ParseIntPipe) customer_id: number) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) {
      throw new BadRequestException('unable to get customer');
    }

    const customerCart = await Quote.findOne({
      where: {
        customer_id: customer.id,
        is_active: true,
      },
    });

    if (customerCart) return { cart_id: customerCart.masked_id };

    const customerTaxClassId =
      await this.externalApiHelper.getCustomerTaxClassFromGroup(
        customer.group_id,
      );

    const cartResponse = await this.cartService.createCart(
      '',
      customer,
      customerTaxClassId,
    );

    return { cart_id: cartResponse };
  }

  @Post('/add-item-to-cart/:customer_id')
  async addToCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: AddProductToCartRequest,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartService.addProductsToCart(
      input,
      customer.id,
      customer.group_id,
    );
  }

  @Post('/update-cart-item/:customer_id')
  async updateCartItem(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: updateCartItemsRqequest,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartService.updateCartItems(
      input,
      customer.id,
      customer.group_id,
    );
  }

  @Put('/remove-item-from-cart/:customer_id')
  async removeItemFromCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: RemoveItemFromCartRequest,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartService.removeItemFromCart(
      input,
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Post('/add-discount/:customer_id')
  async addDiscountToCartItem(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: AddDiscount,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.orderServiceAdminService.addDiscount(input);
  }

  @Post('/merge-carts/:customer_id')
  async mergeCarts(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: MergeCartsRequest,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartService.mergeCarts(
      input,
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Put('/set-billing-address/:customer_id')
  async setBillingAddressOnCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: SetBillingAddressOnCartInput,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartAddressService.setBillingAddressOnCart(
      input.billing_address,
      input.cart_id,
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Put('/set-shipping-address/:customer_id')
  async setShippingAddressesOnCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: SetShiipingAddressOnCartRequest,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.cartAddressService.setShippingAddressOnCart(
      input,
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Put('/apply-coupon-to-cart/:customer_id')
  async applyCouponToCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: SetCouponCodeToCart,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.couponService.applyCouponToCart(
      input,
      customer.id,
      customer.magento_customer_id,
      customer.group_id,
    );
  }

  @Put('/remove-coupon-from-cart/:customer_id')
  async removeCouponFromCart(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body(new ValidationPipe({ transform: true }))
    input: RemoveCouponFromCart,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.couponService.removeCouponFromCart(
      input,
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }

  @Get('/applicable-reward-points/:customer_id')
  async applicableRewardPoints(
    @Param('customer_id', ParseIntPipe) customer_id: number,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');
    return await this.rewardService.applicableRewardPoints(
      customer.customer_id,
    );
  }

  @Post('/apply-reward-points/:customer_id')
  async dkApplyRewardPointsV2(
    @Param('customer_id', ParseIntPipe) customer_id: number,
    @Body('reward_points', ParseIntPipe) reward_points: number,
  ) {
    const customer = await this.externalApiHelper.getCustomerDetailsFromId(
      customer_id,
    );

    if (!customer) throw new BadRequestException('Invalid customer id');

    return await this.rewardService.applyRewardPoints(
      { rewardpoints: reward_points },
      customer.id,
      customer.group_id,
      customer.magento_customer_id,
    );
  }
}
