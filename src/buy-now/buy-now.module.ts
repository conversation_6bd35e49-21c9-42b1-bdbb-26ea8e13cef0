import { Modu<PERSON> } from '@nestjs/common';
import { BuyNowResolver } from './buy-now.resolver';
import { BuyNowService } from './buy-now.service';
import { CartUtilityFunctions } from '../utils/cart-utility-function';
import { ExternalApiHelper } from '../utils/external-api.helper';
import { CartHelperFunctions } from '../utils/cart-service-helper';
import { CartMapper } from '../mapper/cart.mapper';
import { MaskHelper } from '../utils/mask.helper';
import { ExternalApiCaller } from '../utils/external-api-caller';
import { ShippingMethodService } from '../shipping-method/shipping-method.service';
import { HttpModule } from '@nestjs/axios';
import { OrderAPiMapper } from '../mapper/order-api-response';
import { BuyNowController } from './buy-now-controller';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    HttpModule.register({ maxRedirects: 5, timeout: 5000 }),
    DatabaseModule,
  ],
  providers: [
    BuyNowResolver,
    BuyNowService,
    CartUtilityFunctions,
    ExternalApiHelper,
    CartHelperFunctions,
    CartMapper,
    MaskHelper,
    ExternalApiCaller,
    ShippingMethodService,
    OrderAPiMapper,
  ],
  controllers: [BuyNowController],
  exports: [BuyNowService],
})
export class BuyNowModule {}
