import { Resolver, Mutation, Args, Context } from '@nestjs/graphql';
import { BadRequestException } from '@nestjs/common';
import { BuyNowService } from './buy-now.service';
import { BuyNowProdRequest } from '../interface/buy-now-add-request';
import { CartUtilityFunctions } from '../utils/cart-utility-function';
import { ExternalApiHelper } from '../utils/external-api.helper';
import { CartAddress } from 'src/interface/set-billing-address-on-cart';

@Resolver()
export class BuyNowResolver {
  constructor(
    private readonly buyNowService: BuyNowService,
    private readonly cartHelper: CartUtilityFunctions,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  @Mutation('buyNow')
  async addProdToBuyNowCart(
    @Args() request: { input: BuyNowProdRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartHelper.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return this.buyNowService.addProdToBuyNowCart(
      context?.req?.ip,
      request.input,
      customerExists,
    );
  }

  @Mutation('setShippingOnBuyNowCart')
  async setShippingOnBuyNowCart(
    @Args()
    request: {
      input: { cart_id: string; shipping_address: CartAddress };
    },
    @Context() context: any,
  ) {
    const authToken = this.cartHelper.getUserToken(
      context.req.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists) throw new BadRequestException('Invalid auth token');

    return this.buyNowService.setShipingAddressOnBuyNowCart(
      request.input.shipping_address,
      request.input.cart_id,
      customerExists,
    );
  }

  @Mutation('mergeBuyNowCart')
  async mergeBuyNowCart(
    @Args()
    request: { customer_cart_id: string; buy_now_cart_id: string },
    @Context() context: any,
  ) {
    const authToken = this.cartHelper.getUserToken(
      context.req.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists) throw new BadRequestException('Invalid auth token');

    return this.buyNowService.mergeBuyNowCartItems(
      request.customer_cart_id,
      request.buy_now_cart_id,
      customerExists,
    );
  }
}
