import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { BuyNowProdRequest } from '../interface/buy-now-add-request';
import { CartHelperFunctions } from 'src/utils/cart-service-helper';
import { CartUtilityFunctions } from 'src/utils/cart-utility-function';
import { CartMapper } from 'src/mapper/cart.mapper';
import { MaskHelper } from '../utils/mask.helper';
import { CustomerDetails } from 'src/interface/customer';
import { Quote } from '../database/entities/quote';
import { QuoteExtraInfo } from '../database/entities/quote_extra_info';
import { QuoteAddress } from '../database/entities/quote_address';
import { GlobalCurrencyConfiguration } from 'src/interface/graphql-response';
import {
  ProductTypes,
  ProductStatuses,
  AddressTypes,
  CartAction,
  MEMBERSHIP_SKUS,
} from 'src/config/constants';
import { ExternalApiHelper } from '../utils/external-api.helper';
import { ProductDataWithQuantity } from 'src/interface/product';
import { QuoteItem } from 'src/database/entities/quote_item';
import { QuoteDiscount } from '../database/entities/quote_discount';
import {
  BillingAddressDto,
  CartAddress,
} from '../interface/set-billing-address-on-cart';
import { QuoteShippingRate } from '../database/entities/quote_shipping_rate';
import { QuoteItemExtensionAttribute } from '../database/entities/quote_item_extension_attribute';
import { OrderAPiMapper } from '../mapper/order-api-response';
import * as _ from 'lodash';
import { OutputResponseType } from 'src/config/constants';
import { VALIDATION_ERRORS } from 'src/config/constants';
import { CustomBadRequestException } from 'src/filters/custom-exception-filter';
import { QuoteAmountPromotion } from 'src/database/entities/quote_amount_promotion';
import { QuoteItemPromotion } from 'src/database/entities/quote_item_promotion';

@Injectable()
export class BuyNowService {
  constructor(
    private readonly cartHelperFunctions: CartHelperFunctions,
    private readonly cartUtilityFun: CartUtilityFunctions,
    private readonly cartMapper: CartMapper,
    private readonly maskHelper: MaskHelper,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly orderApiMapper: OrderAPiMapper,
  ) {}

  /**
   * It create new buy now incative quote
   * @param remoteIp    Strung
   * @param cartId     String
   * @param CustomerDetails Registered user's
   * @returns
   **/
  async addProdToBuyNowCart(
    remoteIp: string,
    request: BuyNowProdRequest,
    customerDetails: CustomerDetails,
    outputResponseType?: OutputResponseType,
  ) {
    try {
      let shippingAddress = null,
        productTaxes = {};
      const skus: string[] = [],
        parentMappedPrice = {};

      const requestedQtyAndSku = Object.values(request.cart_items).map(
        (o: any) => {
          if (skus.indexOf(o['data'].sku) === -1) {
            skus.push(o['data'].sku);
          }
          return {
            sku: o['data'].sku,
            quantity: o['data'].quantity,
            parent_id: o['data']?.parent_id ?? null,
            referral_code: o['data']?.referral_code ?? null,
          };
        },
      );
      const allRequestedQtyAndSku = [...requestedQtyAndSku];
      const allSkus: string[] = [...new Set([...skus])];
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        allSkus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];

      const [productDetails] = await this.cartHelperFunctions.fetchApiDataV2(
        [...allSkus, ...freeSkus],
        null,
        null,
      );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      if (!productDetails || productDetails.length === 0) {
        throw new InternalServerErrorException(
          'Unable to add product to cart. No product data found',
        );
      }

      for (const prod of productDetails) {
        if (allSkus.indexOf(prod.sku) !== -1) {
          if (prod.status !== ProductStatuses.ENABLE) {
            throw new CustomBadRequestException(
              VALIDATION_ERRORS['disabled_product'].message(prod.name),
              VALIDATION_ERRORS['disabled_product'].errorCode,
            );
          }

          if (
            [ProductTypes.SIMPLE, ProductTypes.VIRTUAL].indexOf(
              prod.type_id,
            ) === -1
          )
            throw new CustomBadRequestException(
              VALIDATION_ERRORS['wrong_type_product'].message,
              VALIDATION_ERRORS['wrong_type_product'].errorCode,
            );

          this.cartUtilityFun.validateProductAvailability({
            product: prod,
            throwError: true,
            requestCartItems: allRequestedQtyAndSku,
            country_id: request.shipping_address?.address?.country_code ?? 'IN',
          });
        }
      }

      const masked_id = await this.maskHelper.generateQuoteMaskedId();

      const cartObj = this.cartMapper.mapUserCart(
        remoteIp,
        {} as GlobalCurrencyConfiguration,
        customerDetails,
      );
      cartObj['masked_id'] = masked_id;
      cartObj['is_active'] = false;

      const newCart = await Quote.create(cartObj);

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        null,
      );

      if (request.shipping_address) {
        const cartAddressObj = this.cartMapper.mapQuoteAddressObj(
          request.shipping_address,
          newCart.customer_email,
          newCart.customer_id,
          AddressTypes.SHIPPING,
        );

        shippingAddress = await QuoteAddress.create({
          quote_id: newCart.quote_id,
          customer_email: newCart.customer_email,
          ...cartAddressObj,
        });

        if (country_id && region_id) {
          productTaxes = await this.externalApiHelper.getTaxRatesForCart(
            country_id,
            region_id,
          );
        }
      }

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        [],
        allRequestedQtyAndSku,
        CartAction.ADD_TO_CART,
      );

      this.cartUtilityFun.computeGroupChildPrice(
        productDetails,
        skuwiseParentIdQty,
        parentMappedPrice,
      );

      const quoteItemsObj = [];
      for (const obj of productDetails) {
        if (skus.indexOf(obj.sku) !== -1)
          quoteItemsObj.push(
            this.cartMapper.builditemsObj(
              newCart.quote_id,
              obj as ProductDataWithQuantity,
              productTaxes ?? {},
              skuwiseParentIdQty,
              parentMappedPrice,
              this.cartUtilityFun.calculateItemsDiscount,
              this.cartUtilityFun.computeTierPrice,
            ),
          );
      }

      const newItems = await QuoteItem.bulkCreate(quoteItemsObj, {});

      if (newItems?.length) {
        allRequestedQtyAndSku?.forEach(async (item) => {
          if (item?.referral_code) {
            const matchingItems = newItems.find(
              (newItem) => newItem.sku === item.sku,
            );
            await this.cartUtilityFun.updateReferralCode(
              item?.referral_code,
              matchingItems,
            );
          }
        });
      }

      newCart.items = newItems;
      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerDetails.group_id || 0,
          allItems: newItems,
          quote: newCart,
          billingAddress: null,
          shippingAddress,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(newCart),
          countryId: country_id,
          regionId: region_id,
          customerId: customerDetails.id,
          quote_filter: { is_active: false },
          skuwiseParentIdQty,
          parentMappedPrice,
          productDetails: productDetailsClone,
          cartAction: CartAction.BUY_NOW_ADD,
          outputResponseType,
          itemPromotions,
          productTaxes,
        });

      await QuoteExtraInfo.create({
        is_active: true,
        quote_id: newCart.quote_id,
        customer_id: customerDetails.id,
      });

      const updatedCart = await Quote.findOne({
        where: {
          quote_id: newCart.quote_id,
          is_active: false,
        },
        include: [
          QuoteDiscount,
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
        ],
      });

      return this.cartMapper.buildCartResponse(
        updatedCart,
        [...(productDetailsClone || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        null,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
      );
    } catch (error) {
      console.log('buy_Now_Cart_Error', error);
      // this.cartUtilityFun.throwError(error);
    }
  }

  /**
   * It build data to be stored in QuoteAddress table
   * @param request    setShipingAddressOnBuyNowCart
   * @param cartId     String
   * @param CustomerDetails Registered user's
   * @returns
   **/
  async setShipingAddressOnBuyNowCart(
    request: CartAddress,
    cartId: string,
    customerDetails: CustomerDetails,
    outputResponseType?: OutputResponseType,
    billingAddressDto?: BillingAddressDto,
  ) {
    try {
      const skuWiseErrors = {},
        parentMappedPrice = {};
      const cartIDExists = await Quote.findOne({
        where: {
          masked_id: cartId,
          customer_id: customerDetails.id,
          is_active: false,
        },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!cartIDExists) {
        throw new BadRequestException('Cart not found');
      }

      // await this.cartHelperFunctions.validateCountryAndRegion(request);

      let shippingAddress: QuoteAddress = cartIDExists.addresses.find(
        (addressType) => addressType.address_type === AddressTypes.SHIPPING,
      );

      let billingAddress: QuoteAddress = cartIDExists.addresses.find(
        (addressType) => addressType.address_type === AddressTypes.BILLING,
      );

      //apppend region and region_code in case null for country india
      await this.cartHelperFunctions.getRegionAndRegionCode(request);

      const cartObj = this.cartMapper.mapQuoteAddressObj(
        request,
        cartIDExists.customer_email,
        cartIDExists.customer_id,
        AddressTypes.SHIPPING,
      );

      shippingAddress = await this.cartHelperFunctions.upsertAddressOnCart(
        cartIDExists.quote_id,
        cartIDExists.customer_email,
        cartIDExists.customer_id,
        AddressTypes.SHIPPING,
        shippingAddress,
        request.address,
      );

      if (billingAddressDto) {
        billingAddress = await this.cartHelperFunctions.upsertAddressOnCart(
          cartIDExists.quote_id,
          cartIDExists.customer_email,
          cartIDExists.customer_id,
          AddressTypes.BILLING,
          null,
          billingAddressDto,
        );
      } else {
        if (billingAddress) {
          await billingAddress.destroy();
          billingAddress = null;
        }
      }

      if (shippingAddress) {
        await QuoteAddress.update(cartObj, {
          where: {
            quote_address_id: shippingAddress.quote_address_id,
          },
          returning: true,
        });
        cartObj['quote_id'] = cartIDExists.quote_id;
        Object.assign(shippingAddress, cartObj);
      } else {
        shippingAddress = await QuoteAddress.create({
          quote_id: cartIDExists.quote_id,
          ...cartObj,
        });
      }

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        null,
      );

      const skus = cartIDExists?.items?.map((o) => o.sku);
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];
      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );
      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        cartIDExists.items,
        [],
        'set_shipping_address',
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      cartIDExists.items =
        await this.cartHelperFunctions.updatePreviousCartItemsWithoutTxn(
          cartIDExists.items,
          productDetails ?? [],
          productTaxes ?? {},
          skuWiseErrors,
          skuwiseParentIdQty,
          parentMappedPrice,
          country_id,
        );

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customerDetails.group_id || 0,
          allItems: cartIDExists.items,
          quote: cartIDExists,
          billingAddress: billingAddress,
          shippingAddress,
          throwError: true,
          countryId: country_id,
          regionId: region_id,
          cartAction: CartAction.SET_SHIPPING_BUY_NOW,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(cartIDExists),
          customerId: customerDetails.id,
          quote_filter: { is_active: false },
          productDetails: productDetailsClone,
          outputResponseType,
          itemPromotions,
          productTaxes,
        });

      const updatedQuote = await Quote.findOne({
        where: { quote_id: cartIDExists.quote_id, is_active: false },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteDiscount,
        ],
      });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
        billingAddress,
      );
    } catch (e) {
      this.cartUtilityFun.throwError(e);
    }
  }

  /**
   * It merge buy now cart with exisitng user cart
   * @param customer_cart_id
   * @param buy_now_cart_id
   * @param CustomerDetails Registered user's
   * @returns
   **/
  async mergeBuyNowCartItems(
    customer_cart_id: string,
    buy_now_cart_id: string,
    customer: CustomerDetails,
    outputResponseType?: OutputResponseType,
  ) {
    try {
      const buyNowCart = await Quote.findOne({
        where: {
          masked_id: buy_now_cart_id,
          customer_id: customer.id,
          is_active: false,
        },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!buyNowCart) throw new NotFoundException('Buy now cart not found');
      buyNowCart.items = buyNowCart.items.filter(
        (o) => MEMBERSHIP_SKUS.indexOf(o.sku) === -1,
      );

      const userCart = await Quote.findOne({
        where: {
          masked_id: customer_cart_id,
          customer_id: customer.id,
        },
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteAddress,
          QuoteDiscount,
        ],
      });

      if (!userCart) throw new NotFoundException('User cart not found');

      const skuWiseErrors = {},
        parentMappedPrice = {};
      const productSkus = [];

      const cartSkuSet = new Set([
        ...buyNowCart.items.map((o) => o.sku),
        ...userCart.items.map((o) => o.sku),
      ]);

      if (
        cartSkuSet.has(MEMBERSHIP_SKUS[0]) &&
        cartSkuSet.has(MEMBERSHIP_SKUS[1])
      ) {
        //remove membership sku on merge
        userCart.items = userCart.items.filter(
          (item) => MEMBERSHIP_SKUS.indexOf(item.sku) === -1,
        );
      }

      const shippingAddress = userCart.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );

      const billingAddress = userCart.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );

      const { country_id, region_id } = this.cartUtilityFun.getCountryAndRegion(
        shippingAddress,
        billingAddress,
      );

      const requestedQtyAndSku = buyNowCart.items?.map((o) => {
        productSkus.push(o.sku);
        return { sku: o.sku, quantity: +o.qty, parent_id: +o.parent_id };
      });

      const skus = [...productSkus, ...userCart.items?.map((o) => o.sku)];
      const { itemPromotions } = await this.cartUtilityFun.getAllItemPromotions(
        skus,
      );
      const freeSkus =
        itemPromotions?.length > 0
          ? itemPromotions.map((item) => item.free_product_sku)
          : [];

      const [productDetails, productTaxes] =
        await this.cartHelperFunctions.fetchApiDataV2(
          [...skus, ...freeSkus],
          country_id,
          region_id,
        );

      const productDetailsClone = productDetails
        ? _.cloneDeep(productDetails)
        : null;

      const skuwiseParentIdQty = this.cartUtilityFun.buildSkuwiseParentIdQty(
        userCart.items,
        requestedQtyAndSku,
        CartAction.MERGE_CART,
      );

      productDetails &&
        this.cartUtilityFun.computeGroupChildPrice(
          productDetails,
          skuwiseParentIdQty,
          parentMappedPrice,
        );

      const productDetailsForRequestSkus = productDetails?.filter((o) => {
        if (productSkus.indexOf(o.sku) !== -1) return o;
      });

      const itemsToModify = [];

      if (userCart.items.length) {
        for (const prod of userCart.items) {
          const index = productSkus.indexOf(prod.sku);
          if (index !== -1) {
            itemsToModify.push(prod.sku);
            productSkus.splice(index, 1);
            const pdIndex = productDetailsForRequestSkus.findIndex(
              (o) => o.sku === prod.sku,
            );
            productDetailsForRequestSkus.splice(pdIndex, 1);
          }
        }
      }

      const insertedItems = [];
      if (itemsToModify.length) {
        const updatedItems =
          await this.cartHelperFunctions.mergeToExistingCartItemsWihoutTxn(
            itemsToModify,
            userCart.items,
            requestedQtyAndSku,
            productDetails ?? [],
            productTaxes ?? {},
            false,
            skuWiseErrors,
            {},
            {},
            shippingAddress?.customer_country_id,
          );
        insertedItems.push(...updatedItems);
        userCart.items = userCart.items.filter(
          (o) => itemsToModify.indexOf(o.sku) === -1,
        );
      }

      if (productSkus.length) {
        const items = await this.cartHelperFunctions.buildQuoteItemsObj(
          userCart.quote_id,
          requestedQtyAndSku,
          productDetailsForRequestSkus,
          productTaxes ?? {},
          {},
          {},
          shippingAddress?.customer_country_id,
        );
        const newItems = await QuoteItem.bulkCreate(items, {});
        newItems?.length && insertedItems.push(...newItems);
      }

      const allItems = _.uniqBy([...userCart.items, ...insertedItems], 'sku');

      const {
        availableShippingMethods,
        availablePaymentMethods,
        is_member_ship_active,
        min_shipping_amount,
        delivery_charges,
      } =
        await this.cartHelperFunctions.updateQuoteAccordingToItemsWithoutTxnV2({
          customerGroupId: customer.group_id || 0,
          allItems: allItems,
          quote: userCart,
          billingAddress,
          shippingAddress,
          countryId: country_id,
          regionId: region_id,
          cartAction: CartAction.BUY_NOW_MERGE,
          couponCode: this.cartUtilityFun.getCustomerCouponCode(allItems),
          customerId: customer.id,
          productDetails: productDetailsClone,
          skuwiseParentIdQty,
          parentMappedPrice,
          outputResponseType,
          itemPromotions,
          productTaxes,
        });

      await QuoteItem.destroy({
        where: { quote_id: buyNowCart.quote_id },
        force: true,
      });

      await QuoteDiscount.destroy({
        where: { quote_id: buyNowCart.quote_id },
      });

      const buyShippingAddress = buyNowCart.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );

      if (buyShippingAddress)
        await QuoteShippingRate.destroy({
          where: {
            quote_address_id: buyShippingAddress.quote_address_id,
          },
        });

      await QuoteAddress.destroy({
        where: { quote_id: buyNowCart.quote_id },
      });

      await buyNowCart.destroy({});

      await QuoteExtraInfo.destroy({
        where: {
          quote_id: buyNowCart.quote_id,
        },
      });

      const updatedQuote = await Quote.findByPk(userCart.quote_id, {
        include: [
          {
            model: QuoteItem,
            include: [
              {
                model: QuoteItemExtensionAttribute,
              },
              {
                model: QuoteItemPromotion,
              },
            ],
          },
          QuoteAmountPromotion,
          QuoteDiscount,
        ],
      });

      return this.cartMapper.buildCartResponse(
        updatedQuote,
        [...(productDetailsClone || [])],
        shippingAddress,
        availableShippingMethods,
        availablePaymentMethods,
        skuWiseErrors,
        outputResponseType,
        { is_member_ship_active, min_shipping_amount, delivery_charges },
      );
    } catch (error) {
      this.cartUtilityFun.throwError(error);
    }
  }

  async incativeBuyNowCart(id: string) {
    try {
      const isExistCart = await Quote.findOne({
        where: { masked_id: id, is_active: false },
      });

      if (!isExistCart)
        return new NotFoundException('Requested buy now cart not found');

      await QuoteExtraInfo.update(
        {
          is_active: false,
        },
        {
          where: { quote_id: isExistCart.quote_id },
        },
      );
      return true;
    } catch (e) {
      this.cartUtilityFun.throwError(e);
    }
  }

  async fetchBuyNowCart(id: string, customer: CustomerDetails) {
    try {
      const cartExists = await Quote.findOne({
        where: { masked_id: id, is_active: false },
        include: [
          {
            model: QuoteItem,
            include: [
              { model: QuoteItemExtensionAttribute },
              { model: QuoteItemPromotion },
            ],
          },
          QuoteAddress,
          QuoteAmountPromotion,
        ],
      });

      if (!cartExists) throw new NotFoundException('Cart not found');

      const shippingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.SHIPPING,
      );

      const billingAddress = cartExists.addresses?.find(
        (o) => o.address_type === AddressTypes.BILLING,
      );

      const availablePaymentMethods =
        await this.externalApiHelper.getAvailablePaymentMethodV4({
          postcode:
            +shippingAddress?.customer_postcode ||
            +billingAddress?.customer_postcode,
          country_code:
            shippingAddress?.customer_country_id ||
            billingAddress?.customer_country_id,
          cart_data: {
            is_cod_on_cart: cartExists.items.every((o) => o.is_cod),
            cart_weight: +(cartExists.total_weight * 1000).toFixed(2),
            cart_amount: cartExists.grand_total,
          },
          products: {
            children: [
              +cartExists.items.find((item) => item.product_id !== undefined)
                ?.product_id || 0,
            ],
          },
        });

      const payment_info = await this.orderApiMapper.buildPaymentInformation(
        cartExists,
        availablePaymentMethods,
      );

      return {
        cart_info: this.orderApiMapper.cartResponse(cartExists, customer),
        payment_info,
      };
    } catch (e) {
      this.cartUtilityFun.throwError(e);
    }
  }
}
