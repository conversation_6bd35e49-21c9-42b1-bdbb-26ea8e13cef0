import {
  Controller,
  Get,
  Put,
  Param,
  UseGuards,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { BuyNowService } from './buy-now.service';
import { BuyNowInactiveCartGuard } from '../guards/buy-now-token-guard';
import { CartUtilityFunctions } from '../utils/cart-utility-function';
import { ExternalApiHelper } from '../utils/external-api.helper';

@Controller('buy-now-cart')
export class BuyNowController {
  constructor(
    private readonly buyNowService: BuyNowService,
    private readonly cartHelper: CartUtilityFunctions,
    private readonly externalApiHelper: ExternalApiHelper,
  ) {}

  @UseGuards(BuyNowInactiveCartGuard)
  @Put('/:id/inactive')
  async inactiveBuyNowCart(@Param() { id }) {
    return this.buyNowService.incativeBuyNowCart(id);
  }

  @Get('/:id/list')
  async fetchBuyNowCart(@Param() { id }, @Req() request) {
    const authToken = this.cartHelper.getUserToken(
      request.headers['authorization'],
    );

    if (!authToken) {
      throw new BadRequestException(
        'User token is mandatory to access resource',
      );
    }

    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );

    if (!customerExists) throw new BadRequestException('Invalid auth token');
    if (!id) throw new BadRequestException('Cart id not provided');

    return this.buyNowService.fetchBuyNowCart(id, customerExists);
  }
}
