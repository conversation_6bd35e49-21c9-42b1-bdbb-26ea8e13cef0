import { BadRequestException, UseInterceptors } from '@nestjs/common';
import { Args, Context, Mutation, Resolver, Query } from '@nestjs/graphql';
import { SetCouponCodeToCart } from 'src/interface/set-coupon-cart-to-cart';
import { RemoveCouponFromCart } from 'src/interface/remove-coupon-from-cart';
import { Quote } from './database/entities/quote';
import {
  AddProductToCartRequest,
  updateCartItemsRqequest,
} from './interface/add-product-to-cart-request';
import { CreateEmptyCartRequest } from './interface/create-empty-cart-request';
import { MergeCartsRequest } from './interface/merge-carts-request';
import { RemoveItemFromCartRequest } from './interface/remove-item-from-cart-request';
import { SetBillingAddressOnCartInput } from './interface/set-billing-address-on-cart';
import { SetEmailOnGuestRequest } from './interface/set-email-on-guest-cart';
import { SetShiipingAddressOnCartRequest } from './interface/set-shipping-address-on-cart-request';
import { CartService } from './utils/cart.service';
import { ExternalApiHelper } from './utils/external-api.helper';
import { CouponService } from './utils/coupon-service';
import { RewardService } from './utils/reward-service';
import { CartAddressService } from './utils/cart-address-service';
import { applyRewardPoints } from './interface/applyRewardPointRequest';
import { logger } from './utils/service-logger';
import { GraphQLXRayInterceptor } from './interceptors/graphql-xray.interceptor';
@UseInterceptors(GraphQLXRayInterceptor)
@Resolver('Cart')
export class CartResolver {
  constructor(
    private readonly cartService: CartService,
    private readonly externalApiHelper: ExternalApiHelper,
    private readonly couponService: CouponService,
    private readonly rewardService: RewardService,
    private readonly cartAddressService: CartAddressService,
  ) {}
  @Mutation()
  async createEmptyCartV2(
    @Args() request: { input: CreateEmptyCartRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.cartService.createGuestCart(
        context?.req?.ip,
        request?.input?.cart_id,
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    const customerCart = await Quote.findOne({
      where: { customer_id: customerExists.id, is_active: true },
    });

    if (customerCart) return customerCart.masked_id;
    const customerTaxClassId =
      await this.externalApiHelper.getCustomerTaxClassFromGroup(
        customerExists.group_id,
      );

    const cartResponse = this.cartService.createCart(
      context?.req?.ip,
      customerExists,
      customerTaxClassId,
      request?.input?.cart_id,
    );

    // console.log(
    //   `createEmptyCartV2_lOGS: ${customerExists?.id}:  ${JSON.stringify(
    //     cartResponse,
    //   )}`,
    // );

    return cartResponse;
  }

  @Mutation()
  async addSimpleProductsToCartV2(
    @Args() request: { input: AddProductToCartRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );

    // logger.info({
    //   message: 'Function addSimpleProductsToCart started',
    //   requestInput: JSON.stringify(request?.input),
    //   cartId: request?.input?.cart_id,
    //   inputToken: authToken,
    // });

    if (!authToken) {
      // for guest user
      return await this.cartService.addProductsToCart(request?.input);
    }
    // for registered user
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
      request?.input?.cart_id,
    );
    if (!customerExists) {
      // logger.info({
      //   message: 'Customer not found',
      //   inputToken: authToken,
      //   cartId: request?.input?.cart_id,
      // });
      throw new BadRequestException('Invalid auth token');
    }
    return await this.cartService.addProductsToCart(
      request?.input,
      customerExists.id,
      customerExists.group_id,
    );
  }

  @Mutation()
  async setGuestEmailOnCartV2(
    @Args() request: { input: SetEmailOnGuestRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.cartAddressService.setGuestEmail(
        request.input.cart_id,
        request.input.email,
      );
    }
    throw new BadRequestException('Invalid cart');
  }

  @Mutation()
  async setBillingAddressOnCartV2(
    @Args() request: { input: SetBillingAddressOnCartInput },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.cartAddressService.setBillingAddressOnCart(
        request.input.billing_address,
        request.input.cart_id,
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.cartAddressService.setBillingAddressOnCart(
      request.input.billing_address,
      request.input.cart_id,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Query()
  async cartV2(@Args() request: { cart_id: string }, @Context() context: any) {
    // logger.info({
    //   message: 'Function fetchCart started',
    //   requestInput: JSON.stringify(request),
    //   cartId: request?.cart_id,
    // });
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      if (!request.cart_id)
        throw new BadRequestException('Please provide cart_id.');
      return this.cartService.getCartData(request.cart_id);
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
      request.cart_id,
    );

    if (!customerExists) {
      // logger.info({
      //   message: 'Customer not found',
      //   inputToken: authToken,
      // });
      throw new BadRequestException('Invalid auth token');
    }
    return this.cartService.getCartData(
      request.cart_id,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async removeItemFromCartV2(
    @Args() request: { input: RemoveItemFromCartRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.cartService.removeItemFromCart(request.input);
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.cartService.removeItemFromCart(
      request.input,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async mergeCartsV2(
    @Args() request: MergeCartsRequest,
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException('Invalid auth token');
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.cartService.mergeCarts(
      request,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async setShippingAddressesOnCartV2(
    @Args() request: { input: SetShiipingAddressOnCartRequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.cartAddressService.setShippingAddressOnCart(request.input);
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');

    // if (
    //   request.input.shipping_addresses[0].address.firstname &&
    //   !request.input.shipping_addresses[0].address.region
    // ) {
    //   console.log(
    //     'setShippingAddressesOnCartV2',
    //     'REGION_NULL_WITH_NAME',
    //     context.req.headers['platform'],
    //     context.req.headers['version'],
    //     request.input.cart_id,
    //     JSON.stringify(request.input),
    //   );
    // } else {
    //   console.log(
    //     'setShippingAddressesOnCartV2',
    //     'REGION_WITH_NAME',
    //     context.req.headers['platform'],
    //     context.req.headers['version'],
    //     request.input.cart_id,
    //     JSON.stringify(request.input),
    //   );
    // }
    return await this.cartAddressService.setShippingAddressOnCart(
      request.input,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async applyCouponToCartV2(
    @Args() request: { input: SetCouponCodeToCart },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return this.couponService.applyCouponToCart(request.input);
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    // console.log(customerExists, 'CE');
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.couponService.applyCouponToCart(
      request.input,
      customerExists.id,
      customerExists.magento_customer_id,
      customerExists.group_id,
    );
  }

  @Mutation()
  async dkApplyRewardPointsV2(
    @Args()
    request: applyRewardPoints,
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.rewardService.applyRewardPoints(
      request,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async removeCouponFromCartV2(
    @Args() request: { input: RemoveCouponFromCart },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      return await this.couponService.removeCouponFromCart(request.input);
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.couponService.removeCouponFromCart(
      request.input,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Mutation()
  async updateCartItemsV2(
    @Args() request: { input: updateCartItemsRqequest },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      // for guest user
      return await this.cartService.updateCartItems(request?.input);
    }
    // for registered user
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.cartService.updateCartItems(
      request?.input,
      customerExists.id,
      customerExists.group_id,
      customerExists.magento_customer_id,
    );
  }

  @Query()
  async applicableRewardPointsV2(
    @Args()
    request: {
      cart_id: string;
      is_buy_now_cart: boolean;
    },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');
    return await this.rewardService.applicableRewardPoints(
      customerExists.customer_id,
      request,
    );
  }

  @Query()
  async CustomerCoupons(
    @Args() request: { cart_id: string; is_buy_now_cart: boolean },
    @Context() context: any,
  ) {
    const authToken = this.cartService.getUserToken(
      context.req.headers['authorization'],
    );
    if (!authToken) {
      throw new BadRequestException(
        'Current customer does not have access to the resource',
      );
    }
    const customerExists = await this.externalApiHelper.getCustomerDetails(
      authToken,
    );
    if (!customerExists) throw new BadRequestException('Invalid auth token');

    return await this.couponService.customerCoupons(
      request?.cart_id,
      request?.is_buy_now_cart,
      customerExists.customer_id,
      customerExists.group_id,
    );
  }
}
