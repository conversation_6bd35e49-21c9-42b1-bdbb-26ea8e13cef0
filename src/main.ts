import { NestFactory } from '@nestjs/core';
import { RequestMethod } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import config from './config/env';
import { logger } from './utils/service-logger';
import { json } from 'express';
import * as AWSXRay from 'aws-xray-sdk';
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('http'));

config.xray_daemon_address &&
  AWSXRay.setDaemonAddress(config.xray_daemon_address);

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule);
    const appPort = config.app.port;

    app.use(AWSXRay.express.openSegment(config.app.service_name));
    app.enableCors({ origin: '*', allowedHeaders: '*', methods: '*' });
    app.use(json({ limit: '1mb' }));
    app.setGlobalPrefix('/api/v1', {
      exclude: [
        { path: 'health', method: RequestMethod.GET },
        { path: 'cart/api/v1/carts(.*)', method: RequestMethod.ALL },
      ],
    });
    // simple comment to rerun pipeline
    const swaggerOptions = new DocumentBuilder()
      .setTitle('Cart service')
      .setDescription('Cart Sevice APIs')
      .setVersion('1.0')
      .build();
    const document = SwaggerModule.createDocument(app, swaggerOptions, {
      include: [AppModule],
    });
    SwaggerModule.setup('api-docs', app, document);

    await app.listen(appPort);
    logger.info('Cart microservice started on: ' + appPort);
  } catch (error) {
    console.log(error);
  }
}
bootstrap();
