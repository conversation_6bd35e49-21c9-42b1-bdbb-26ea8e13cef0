import { Injectable } from '@nestjs/common';
import { Quote } from '../database/entities/quote';
import { QuoteAddress } from 'src/database/entities/quote_address';
import {
  CartResponse,
  ShippingAssignment,
  CartItem,
} from '../interface/cart-api-response';
import { AddressTypes } from '../config/constants';
import { QuoteShippingRate } from '../database/entities/quote_shipping_rate';
import { QuoteItem } from 'src/database/entities/quote_item';
import {
  CartPaymentInfoResponse,
  PyamentInfoItem,
} from 'src/interface/cart-api-response';
import { CustomerDetails } from 'src/interface/customer';
import { PaymentMethod } from 'src/interface/graphql-response';

@Injectable()
export class OrderAPiMapper {
  cartResponse = (quote: Quote, customer?: any): CartResponse => {
    const shipping_address = quote.addresses?.find(
      (o) => o.address_type === AddressTypes.SHIPPING,
    );

    const billing_address = quote.addresses?.find(
      (o) => o.address_type === AddressTypes.BILLING,
    );

    return {
      id: quote.quote_id,
      masked_id: quote.masked_id,
      created_at: quote.createdAt,
      updated_at: quote.updatedAt,
      is_active: quote.is_active,
      is_virtual: quote.is_virtual,
      items_count: quote.items_count,
      items_qty: quote.items_qty,
      customer: this.buildCustomerDetails(customer, quote.customer_email),
      items: this.buildcartItems(quote.items),
      quote_amount_promotions: quote.quoteAmountPromotions,
      is_active_membership: quote.is_active_membership,
      billing_address: this.buildBillingAddress(
        billing_address || shipping_address,
        quote.customer_email,
      ),
      orig_order_id: quote.orig_order_id,
      currency: {
        global_currency_code: quote.quote_currency_code,
        base_currency_code: quote.quote_currency_code,
        store_currency_code: quote.quote_currency_code,
        quote_currency_code: quote.quote_currency_code,
        store_to_base_rate: quote.store_to_base_rate,
        store_to_quote_rate: quote.store_to_quote_rate,
        base_to_global_rate: quote.base_to_global_rate,
        base_to_quote_rate: quote.base_to_quote_rate,
      },
      applied_rule_ids: quote?.applied_rule_ids ?? null,
      customer_is_guest: quote.customer_is_guest,
      customer_note_notify: quote.customer_note_notify,
      customer_tax_class_id: quote.customer_tax_class_id,
      store_id: quote.store_id,
      extension_attributes: {
        shipping_assignments: this.buildShippingAssignment(
          quote.addresses?.find(
            (o) => o.address_type === AddressTypes.SHIPPING,
          ),
          quote.items,
        ),
      },
    };
  };

  buildcartItems = (items: QuoteItem[]): CartItem[] => {
    if (items.length === 0) return [];
    return items.map((item: QuoteItem) => ({
      item_id: item.quote_item_id,
      sku: item.sku,
      qty: item.qty,
      name: item.name,
      price: item.price,
      product_type: item.product_type,
      quote_id: item.quote_id.toString(),
      referral_code: item?.extension_attribute?.referral_code ?? null,
      // is_free_product: item?.extension_attribute?.is_free_product ?? false,
      parent_id: item?.parent_id ?? null,
      quote_item_promotions: item.quoteItemPromotions,
      buying_guide_qty: +item?.extension_attribute?.buying_guide_qty || 0,
    }));
  };

  buildShippingAssignment = (
    address: QuoteAddress,
    items: QuoteItem[],
  ): ShippingAssignment[] => {
    return [
      {
        shipping: {
          address: this.buildBillingAddress(address),
          method: '',
        },
        items: this.buildcartItems(items),
      },
    ];
  };

  buildBillingAddress = (address: QuoteAddress, email?: string) => ({
    id: address?.quote_address_id ?? null,
    region: address?.customer_region ?? null,
    region_id: address?.customer_region_id ?? null,
    region_code: address?.customer_region_code ?? null,
    country_id: address?.customer_country_id ?? null,
    street: address?.customer_street?.split(',') ?? [],
    telephone: address?.customer_telephone ?? null,
    postcode: address?.customer_postcode ?? null,
    city: address?.customer_city ?? null,
    firstname: address?.customer_firstname ?? null,
    lastname: address?.customer_lastname ?? null,
    email: address?.customer_email || email,
    same_as_billing: address?.same_as_billing ? 1 : 0,
    save_in_address_book: address?.save_in_address_book ? 1 : 0,
    gst_id: address?.gst_id,
    latitude: address?.latitude ?? null,
    longitude: address?.longitude ?? null,
    tag: address?.tag ?? null,
    map_address: address?.map_address ?? null,
    customer_street_2: address?.customer_street_2 ?? null,
  });

  buildPaymentInformation = async (
    quote: Quote,
    availablePaymentMethods: PaymentMethod[],
  ): Promise<CartPaymentInfoResponse> => {
    const shipping_amount = await this.getDeliveryCharges(quote.addresses);
    const total_tax_amount = this.buildQuoteTaxAmount(quote.items);
    return {
      payment_methods: availablePaymentMethods,
      totals: {
        coupon_code: quote.coupon_code,
        grand_total: +quote.grand_total,
        base_grand_total: +quote.base_grand_total,
        subtotal: quote.subtotal - total_tax_amount,
        base_subtotal: quote.base_subtotal - total_tax_amount,
        discount_amount: +quote.discount_amount,
        base_discount_amount: null,
        subtotal_with_discount: +quote.subtotal_with_discount,
        base_subtotal_with_discount: +quote.base_subtotal_with_discount,
        shipping_amount: shipping_amount,
        base_shipping_amount: shipping_amount,
        shipping_discount_amount: null,
        base_shipping_discount_amount: null,
        tax_amount: total_tax_amount,
        base_tax_amount: total_tax_amount,
        weee_tax_applied_amount: null,
        shipping_tax_amount: null,
        base_shipping_tax_amount: null,
        subtotal_incl_tax: +quote.subtotal_including_tax,
        shipping_incl_tax: null,
        base_shipping_incl_tax: null,
        base_currency_code:
          quote.quote_currency_symbol || quote.quote_currency_code,
        quote_currency_code:
          quote.quote_currency_symbol || quote.quote_currency_code,
        items_qty: quote.items_qty,
        items: this.buildPaymentInfoItems(quote.items),
        total_segments: await this.buildTotalSegment(quote),
        extension_attributes: {
          mp_membership: null,
        },
      },
    };
  };

  buildTotalSegment = async (quote: Quote) => {
    return [
      {
        code: 'subtotal',
        title: 'Subtotal',
        value: +quote.subtotal_including_tax,
      },
      {
        code: 'shipping',
        title: 'Shipping & Handling (Delivery charge - Delivery Charge)',
        value: await this.getDeliveryCharges(quote.addresses),
      },
      {
        code: 'total_savings',
        title: 'Total Savings',
        value: +quote.total_savings,
      },
      {
        code: 'grand_total',
        title: 'Grand Total',
        value: +quote.grand_total,
        area: 'footer',
      },
      {
        code: 'rewardsdiscount',
        title: 'Reward Discount',
        value: +quote.rewards_discount,
      },
      {
        code: 'handling_fee',
        title: 'Overweight delivery charges',
        value: +quote.overweight_delivery_charges,
      },
    ];
  };

  buildCustomerDetails = (customer: CustomerDetails, email?: string) => {
    if (!customer) {
      return {
        email: email ?? null,
        firstname: null,
        lastname: null,
      };
    }
    return customer;
  };

  getDeliveryCharges = async (addresses: QuoteAddress[]) => {
    let delivery_charges = 0;
    const isAddressEist = addresses?.find(
      (o) => o.address_type === AddressTypes.SHIPPING,
    );
    if (isAddressEist) {
      const shippingRateExists = await QuoteShippingRate.findOne({
        where: {
          quote_address_id: isAddressEist.quote_address_id,
        },
      });
      delivery_charges = shippingRateExists?.price ?? 0;
    }
    return +delivery_charges;
  };

  buildPaymentInfoItems = (items: QuoteItem[]): PyamentInfoItem[] => {
    if (items.length === 0) return [];
    return items.map((item: QuoteItem) => ({
      item_id: +item?.quote_item_id || 0,
      price: +item?.price || 0,
      base_price: +item?.base_price || 0,
      qty: +item.qty,
      row_total: +item?.row_total || 0,
      base_row_total: +item?.base_row_total || 0,
      row_total_with_discount: +item?.row_total_with_discount || 0,
      tax_amount: +item?.tax_amount || 0,
      base_tax_amount: +item?.base_tax_amount || 0,
      tax_percent: +item.tax_percent,
      discount_amount: +item.discount_amount,
      base_discount_amount: +item.base_discount_amount,
      discount_percent: +item.discount_percent,
      price_incl_tax: +item.price_incl_tax,
      base_price_incl_tax: +item.base_price_incl_tax,
      row_total_incl_tax: +item.row_total_incl_tax,
      base_row_total_incl_tax: +item.base_row_total_incl_tax,
      options: '[]',
      weee_tax_applied_amount: null,
      weee_tax_applied: null,
      name: item.name,
      referral_code: item?.extension_attribute?.referral_code ?? null,
    }));
  };
  buildQuoteTaxAmount(items: QuoteItem[]) {
    if (items.length === 0) return 0;
    return items.reduce((acc, item) => acc + +item.tax_amount, 0);
  }
}
