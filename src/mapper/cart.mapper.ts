import { Injectable } from '@nestjs/common';
import {
  AdminGlobalConfig,
  ProductTypes,
  QuoteConfig,
  ProductStatuses,
  addressErrors,
  itemsErrors,
  StockStatuses,
  MEMBERSHIP_PRODUCTS,
} from 'src/config/constants';
import { Quote } from 'src/database/entities/quote';
import { QuoteAddress } from 'src/database/entities/quote_address';
import { QuoteItem } from 'src/database/entities/quote_item';
import { CustomerDetails } from 'src/interface/customer';
import {
  AvailableShippingMethod,
  GlobalCurrencyConfiguration,
  PaymentMethod,
  ProductData,
} from 'src/interface/graphql-response';
import {
  ProductDataWithQuantity,
  ProductTax,
  ProductTaxesResponse,
} from 'src/interface/product';
import * as _ from 'lodash';
import { QuoteDiscountsObject } from 'src/interface/discount';
import {
  BillingAddressDto,
  CartAddress,
} from 'src/interface/set-billing-address-on-cart';
import {
  ParentUnitPriceInterface,
  SkuWiseParentIdAndQTy,
} from '../interface/parent-mapped-price';
import { CartResponse } from '../interface/cart-rest-api-response';
import { CartItem } from '../interface/cart-rest-api-response';
import {
  OutputResponseType,
  MIN_CART_SUBTOTAL_FOR_FREE_DELIVERY,
} from 'src/config/constants';

@Injectable()
export class CartMapper {
  /**
   * Build quote table object to store guest quote
   * @param remoteIp request's remote ip
   * @param currencyCofig GlobalCurrencyConfiguration
   * @returns
   */
  mapGuestCart = (
    remoteIp: string,
    currencyCofig: GlobalCurrencyConfiguration,
  ) => {
    return {
      store_id: AdminGlobalConfig.defaultStoreId,
      is_active: true,
      is_virtual: false,
      base_currency_code: AdminGlobalConfig.baseCurrencyCode,
      store_currency_code: AdminGlobalConfig.defaultStoreCurrencyCode,
      quote_currency_code: QuoteConfig.defaultCurrencyCode,
      quote_currency_symbol: QuoteConfig.defaultCurrencySymbol,
      customer_tax_class_id: null,
      customer_email: null,
      customer_is_guest: true,
      remote_ip: remoteIp,
      applied_rule_ids: '',
      global_currency_code: AdminGlobalConfig.baseCurrencyCode,
    };
  };

  /**
   * Build quote table object to store registered user's quote
   * @param remoteIp request's remote ip
   * @param currencyCofig GlobalCurrencyConfiguration
   * @returns
   */
  mapUserCart = (
    remoteIp: string | undefined,
    currencyCofig: GlobalCurrencyConfiguration,
    customer: CustomerDetails,
    customerTaxClassId?: string,
  ) => {
    return {
      store_id: AdminGlobalConfig.defaultStoreId,
      is_active: true,
      is_virtual: false,
      base_currency_code: AdminGlobalConfig.baseCurrencyCode,
      store_currency_code: AdminGlobalConfig.defaultStoreCurrencyCode,
      quote_currency_code: QuoteConfig.defaultCurrencyCode,
      quote_currency_symbol: QuoteConfig.defaultCurrencySymbol,
      customer_id: customer.id,
      customer_tax_class_id: customerTaxClassId,
      customer_group_id: customer.group_id,
      customer_email: customer.email,
      customer_firstname: customer.firstname,
      customer_lastname: customer.lastname,
      customer_is_guest: false,
      remote_ip: remoteIp,
      applied_rule_ids: '',
      global_currency_code: AdminGlobalConfig.baseCurrencyCode,
      customer_gst_id: customer.taxvat,
      customer_gender: customer.gender,
    };
  };

  /**
   * It builds parameters to send as Cart in response
   * @param quote Quote
   * @param productDetails ProductData[]
   * @returns
   */
  buildCartResponse = (
    quote: Quote,
    productDetails: ProductData[],
    quoteShippingAddress: QuoteAddress,
    availableShippingMethods: AvailableShippingMethod[],
    availablePaymentMethods: PaymentMethod[],
    skuWiseErrors?: any,
    outputResponseType?: OutputResponseType,
    memberShipInfo?: {
      is_member_ship_active: boolean;
      min_shipping_amount: number;
      delivery_charges: number;
    },
    billingAddress?: QuoteAddress,
  ) => {
    if (outputResponseType === OutputResponseType.REST) {
      return this.buildRestApiCartResponse(
        quote,
        productDetails,
        quoteShippingAddress,
        availableShippingMethods?.[0],
        skuWiseErrors,
        memberShipInfo,
        billingAddress,
      );
    }
    const { errorMsg } = this.itemsErrMsg(skuWiseErrors);
    return {
      cart: {
        applied_coupon: quote.coupon_code ? { code: quote.coupon_code } : null,
        applied_coupons: quote.coupon_code
          ? [{ code: quote.coupon_code }]
          : null,
        email: quote.customer_email,
        global_errors: errorMsg,
        global_errors_v2: [],
        id: quote.masked_id,
        is_virtual: quote.is_virtual,
        prices: this.buildinCartPrices(quote),
        shipping_addresses: quoteShippingAddress
          ? this.buildCartShippingAddresses(
              quote.total_weight,
              quoteShippingAddress,
              availableShippingMethods?.[0],
              availableShippingMethods,
            )
          : [],
        available_payment_methods: availablePaymentMethods,
        total_quantity: quote.items_qty,
        items: this.buildGqlItemsResponse(quote, productDetails, skuWiseErrors),
      },
    };
  };

  buildGqlItemsResponse(
    quote: Quote,
    productDetails: ProductData[],
    skuWiseErrors?: any,
  ) {
    const freeItemsMap = new Map<string, number>();
    const freeItemsArr = [];
    const itemsResponse = quote.items.map((item) => {
      const productDetail = productDetails.find((p) => p.sku === item.sku);
      const regularPrice =
        productDetail?.price?.regularPrice?.amount?.value ?? 0;
      const itemObj = {
        brand_image: null,
        discount: this.itemPercentSavings(
          regularPrice,
          +item.price_incl_tax,
          +item.qty,
        ),
        error_messages: skuWiseErrors?.[item.sku] ?? null,
        id: item.quote_item_id.toString(),
        prices: {
          discounts: [
            ...this.buildCartDiscounts(
              _.filter(quote.discount?.discounts, {
                item_product_id: item.product_id,
              }),
            ),
            ..._.filter(quote.items, {
              quote_item_id: item.quote_item_id,
            }).map((i: QuoteItem) => ({
              amount: {
                currency: QuoteConfig.defaultCurrencyCode,
                currency_symbol: QuoteConfig.defaultCurrencySymbol,
                value: i?.extension_attribute?.admin_discount_amount ?? 0,
              },
              label: 'Custom discount',
            })),
          ],
          price: {
            currency: quote.quote_currency_code,
            currency_symbol: quote.quote_currency_symbol,
            value: item.price,
          },
          row_total: {
            currency: quote.quote_currency_code,
            currency_symbol: quote.quote_currency_symbol,
            value: item.row_total,
          },
          row_total_including_tax: {
            currency: quote.quote_currency_code,
            currency_symbol: quote.quote_currency_symbol,
            value: item.row_total_incl_tax,
          },
          total_item_discount: {
            currency: quote.quote_currency_code,
            currency_symbol: quote.quote_currency_symbol,
            value: item.discount_amount,
          },
        },
        product: productDetail
          ? this.buildCartItemProductInterfaceResponse(productDetail)
          : null,
        qty_increments: 1,
        quantity: item.qty,
        reward_point_product: item.reward_points_earned,
        stock_status: productDetail?.is_in_stock ? 1 : 0,
        updated_at: new Date(item.updatedAt || Date.now()), //for dummy addToCart response
      };

      this.buildItemPromotionResponse(
        quote,
        item,
        freeItemsArr,
        freeItemsMap,
        productDetails,
        skuWiseErrors,
      );

      return itemObj;
    });

    this.buildAmountPromotionsItemResponse(
      quote,
      productDetails,
      freeItemsArr,
      freeItemsMap,
      skuWiseErrors,
    );

    return [...freeItemsArr, ...itemsResponse];
  }

  buildAmountPromotionsItemResponse(
    quote: Quote,
    products: ProductData[],
    freeItemsArr: any[],
    freeItemsMap: Map<string, number>,
    skuWiseErrors: any,
  ) {
    if (quote?.quoteAmountPromotions?.length > 0) {
      quote.quoteAmountPromotions.forEach((quoteAmountPromotion) => {
        const freeSku = quoteAmountPromotion.meta_info.free_sku;
        const freeProductDetail = products.find((o) => o.sku === freeSku);
        const freeProduct = quoteAmountPromotion?.meta_info;
        if (!freeItemsMap[freeSku]) {
          freeItemsMap[freeSku] = 1;
          const itemResponse = {
            error_messages: skuWiseErrors?.[freeProduct.free_sku] ?? null,
            item_id: quoteAmountPromotion.id,
            id: quoteAmountPromotion.id,
            item_pricing_details: {
              discounts: [],
              row_total_including_tax: {
                amount: {
                  label: 'Row total including tax',
                  value: freeProduct.price * freeProduct.qty,
                },
              },
              row_total_regular_price: {
                amount: {
                  label: 'Row Total regular price',
                  value: freeProduct.price * freeProduct.qty,
                },
              },
            },
            product: this.buildRestCartItemProductInterfaceResponse(
              freeProductDetail,
              true,
            ),
            parent_id: freeProductDetail?.parent_id ?? null,
            qty_increments: 1,
            quantity: +freeProduct?.qty,
            is_free_product: true,
            reward_point_product: 0,
            stock_status: freeProductDetail.is_in_stock ? 1 : 0,
          };
          freeItemsArr.push(itemResponse);
        } else {
          freeItemsMap[freeSku] += 1;
          const existingFreeProduct = freeItemsArr.find(
            (o) => o?.product?.sku === freeSku && o?.is_free_product,
          );
          if (existingFreeProduct) {
            existingFreeProduct.quantity += freeProduct?.qty;
          }
        }
      });
    }
  }

  buildItemPromotionResponse(
    quote: Quote,
    quoteItem: QuoteItem,
    freeItemsArr: any[],
    freeItemsMap: Map<string, number>,
    products: ProductData[],
    skuWiseErrors: Map<string, string[]>,
  ) {
    // HANDLING ITEM PROMOTION IN RESPONSE
    if (quoteItem?.quoteItemPromotions?.length > 0) {
      quoteItem.quoteItemPromotions.forEach((quoteItemPromotion) => {
        const freeProduct = quoteItemPromotion.meta_info;
        const freeSku = freeProduct.free_sku;
        const freeProductDetail = products.find((o) => o.sku === freeSku);
        if (!freeItemsMap[freeSku]) {
          freeItemsMap[freeSku] = 1;
          const itemResponse = {
            error_messages: skuWiseErrors?.[freeProduct.free_sku] ?? null,
            item_id: quoteItemPromotion.id,
            id: quoteItemPromotion.id,
            item_pricing_details: {
              discounts: [],
              row_total_including_tax: {
                amount: {
                  label: 'Row total including tax',
                  value: freeProduct.price * freeProduct.qty,
                },
              },
              row_total_regular_price: {
                amount: {
                  label: 'Row Total regular price',
                  value: freeProduct.price * freeProduct.qty,
                },
              },
            },
            product: this.buildRestCartItemProductInterfaceResponse(
              freeProductDetail,
              true,
            ),
            parent_id: freeProductDetail?.parent_id ?? null,
            qty_increments: 1,
            quantity: +freeProduct?.qty,
            is_free_product: true,
            reward_point_product: 0,
            stock_status: freeProductDetail.is_in_stock ? 1 : 0,
          };

          // freeItemsMap[freeSku] += 1;

          freeItemsArr.push(itemResponse);
        } else {
          // console.log('CHECK');
          freeItemsMap[freeSku] += 1;
          const existingFreeProduct = freeItemsArr.find(
            (o) => o?.product?.sku === freeSku && o?.is_free_product,
          );
          if (existingFreeProduct) {
            existingFreeProduct.quantity += freeProduct?.qty;
          }
        }
      });
    }

    // HANDLING AMOUNT PROMOTION IN RESPONSE
  }

  // return this.buildCartItemsResponse(
  //   quote,
  //   item,
  //   productDetail,
  //   skuWiseErrors?.[item.sku] ?? null,
  // );

  /**
   * It builds CartItemInterface type for response
   * @param quote Quote
   * @param quoteItem QuoteItem
   * @param product ProductData
   * @returns
   */
  buildCartItemsResponse = (
    quote: Quote,
    quoteItem: QuoteItem,
    product: ProductData,
    error_messages?: [],
  ) => {
    const regularPrice = product?.price?.regularPrice?.amount?.value ?? 0;
    return {
      brand_image: null,
      discount: this.itemPercentSavings(
        regularPrice,
        +quoteItem.price_incl_tax,
        +quoteItem.qty,
      ),
      error_messages: error_messages,
      id: quoteItem.quote_item_id.toString(),
      prices: {
        discounts: [
          ...this.buildCartDiscounts(
            _.filter(quote.discount?.discounts, {
              item_product_id: quoteItem.product_id,
            }),
          ),
          ..._.filter(quote.items, {
            quote_item_id: quoteItem.quote_item_id,
          }).map((i: QuoteItem) => ({
            amount: {
              currency: QuoteConfig.defaultCurrencyCode,
              currency_symbol: QuoteConfig.defaultCurrencySymbol,
              value: i?.extension_attribute?.admin_discount_amount ?? 0,
            },
            label: 'Custom discount',
          })),
        ],
        price: {
          currency: quote.quote_currency_code,
          currency_symbol: quote.quote_currency_symbol,
          value: quoteItem.price,
        },
        row_total: {
          currency: quote.quote_currency_code,
          currency_symbol: quote.quote_currency_symbol,
          value: quoteItem.row_total,
        },
        row_total_including_tax: {
          currency: quote.quote_currency_code,
          currency_symbol: quote.quote_currency_symbol,
          value: quoteItem.row_total_incl_tax,
        },
        total_item_discount: {
          currency: quote.quote_currency_code,
          currency_symbol: quote.quote_currency_symbol,
          value: quoteItem.discount_amount,
        },
      },
      product: product
        ? this.buildCartItemProductInterfaceResponse(product)
        : null,
      qty_increments: 1,
      quantity: quoteItem.qty,
      is_free_product: quoteItem?.extension_attribute?.is_free_product,
      reward_point_product: quoteItem.reward_points_earned,
      stock_status: product?.is_in_stock ? 1 : 0,
      updated_at: new Date(quoteItem.updatedAt || Date.now()), //for dummy addToCart response
    };
  };

  /**
   * It builds CartItemProductInterface to send as response
   * @param product ProductData
   * @returns
   */
  buildCartItemProductInterfaceResponse = (product: ProductData) => {
    return {
      average_rating: product?.average_rating,
      categories: [],
      dentalkart_custom_fee: product.dentalkart_custom_fee,
      description: product.short_description,
      dispatch_date: null,
      dispatch_days: product.dispatch_days,
      expiry: product.pd_expiry_date,
      gift_message_available: '',
      id: product.id,
      image: { url: product?.image_url, label: '' },
      is_cod: +product.is_cod,
      manufacturer: product.manufacturer,
      max_sale_qty: product.max_sale_qty,
      media_gallery_entries: product.media_gallery_entries,
      meta_description: product.meta_description,
      meta_keyword: product.meta_keyword,
      meta_title: product.meta_title,
      msrp: product.msrp,
      name: product.name,
      // only_x_left_in_stock: product. ,
      pd_expiry_date: product.pd_expiry_date,
      price: product.price,
      rating_count: product.rating_count,
      reward_point_product: product.reward_point_product,
      short_description: product.short_description,
      sku: product.sku,
      small_image: product.image_url,
      special_price: product.special_price,
      stock_status: product.is_in_stock
        ? StockStatuses.IN_STOCK
        : StockStatuses.OUT_OF_STOCK,
      thumbnail: { url: product?.thumbnail_url, label: '' },
      tier_prices: product.tier_prices,
      type_id: product.type_id,
      url_key: product.url_key,
      url_path: product.url_key,
      weight: product.weight,
    };
  };

  /**
   * It builds CartItemProductInterface to send as response
   * @param product ProductData
   * @returns
   */
  buildRestCartItemProductInterfaceResponse = (
    product: ProductData,
    isFreeProduct = false,
  ) => {
    return {
      average_rating: product?.average_rating,
      dentalkart_custom_fee: isFreeProduct
        ? null
        : product.dentalkart_custom_fee,
      description: product.short_description,
      dispatch_days: product.dispatch_days,
      expiry: product.pd_expiry_date,
      id: product.id,
      image: { url: product?.image_url, label: '' },
      is_cod: +product.is_cod,
      manufacturer: product.manufacturer,
      max_sale_qty: product.max_sale_qty,
      msrp: product.msrp,
      name: product.name,
      pd_expiry_date: product.pd_expiry_date,
      price: product.price,
      rating_count: product.rating_count,
      reward_point_product: +product?.reward_point_product || 0,
      sku: product.sku,
      small_image: product.image_url,
      special_price: product.special_price,
      stock_status: product.is_in_stock
        ? StockStatuses.IN_STOCK
        : StockStatuses.OUT_OF_STOCK,
      thumbnail: { url: product?.thumbnail_url, label: '' },
      tier_prices: product.tier_prices,
      type_id: product.type_id,
      url_key: product.url_key,
      url_path: product.url_key,
      weight: product.weight,
    };
  };

  /**
   * It builds CartPrice interaface typeto send as reponse
   * @param quote Quote
   * @returns
   */
  buildinCartPrices = (quote: Quote) => {
    return {
      applied_taxes: this.buildCartAppliedTaxes(quote.items),
      discount: {
        amount: {
          currency: QuoteConfig.defaultCurrencyCode,
          currency_symbol: QuoteConfig.defaultCurrencySymbol,
          value: quote.discount_amount,
        },
        label: [],
      },
      discounts: this.buildCartDiscounts(quote.discount?.discounts || []),
      grand_total: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.grand_total,
      },
      overweight_delivery_charges: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.overweight_delivery_charges,
      },
      rewardsdiscount: {
        amount: {
          currency: QuoteConfig.defaultCurrencyCode,
          currency_symbol: QuoteConfig.defaultCurrencySymbol,
          value: quote.rewards_discount,
        },
        label: '',
      },
      subtotal_excluding_tax: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.subtotal,
      },
      subtotal_including_tax: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.subtotal_including_tax,
      },
      subtotal_with_discount_excluding_tax: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.subtotal_with_discount,
      },
      total_savings: {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: quote.total_savings,
      },
    };
  };

  /**
   * It builds CartDiscounts type data
   * @param quoteDiscounts QuoteDiscount[]
   */
  buildCartDiscounts = (quoteDiscounts: QuoteDiscountsObject[]) => {
    const cartDiscounts = [];
    for (const discount of quoteDiscounts) {
      cartDiscounts.push({
        amount: {
          currency: QuoteConfig.defaultCurrencyCode,
          currency_symbol: QuoteConfig.defaultCurrencySymbol,
          value: discount.discount_amount,
        },
        label: discount.discount_label,
      });
    }
    return cartDiscounts;
  };

  /**
   * It builds CartDiscounts type data
   * @param quoteDiscounts QuoteDiscount[]
   */
  buildRestCartDiscounts = (quoteDiscounts: QuoteDiscountsObject[]) => {
    const cartDiscounts = [];
    for (const discount of quoteDiscounts) {
      cartDiscounts.push({
        amount: {
          code: 'quote_discount',
          value: discount.discount_amount,
          label: discount.discount_label,
        },
      });
    }
    return cartDiscounts;
  };

  /**
   * It builds CartTax interface data to send as response
   * @param quoteItems QuoteItem[]
   */
  buildCartAppliedTaxes = (quoteItems: QuoteItem[]) => {
    const cartTaxes = [];
    for (const item of quoteItems) {
      if (item.tax_amount > 0)
        cartTaxes.push({
          amount: {
            currency: QuoteConfig.defaultCurrencyCode,
            currency_symbol: QuoteConfig.defaultCurrencySymbol,
            value: item.tax_amount,
          },
          label: item.applied_tax_titles,
        });
    }
    return cartTaxes;
  };

  buildRestCartAppliedTaxes = (quoteItems: QuoteItem[]) => {
    const cartTaxes = [];
    for (const item of quoteItems) {
      if (item.tax_amount > 0)
        cartTaxes.push({
          amount: {
            label: item.applied_tax_titles,
            value: item.tax_amount,
          },
        });
    }
    return cartTaxes;
  };

  /**
   * It builds ShippingCartAddress type of data to send as response
   * @param totalWeight summation of items weight in cart
   * @param quoteShippingAddress selected shipping address for cart
   * @param selectedShippingMethod selected shipping method
   * @param availableShippingMethods all available shipping methods
   * @returns
   */
  buildCartShippingAddresses = (
    totalWeight: number,
    quoteShippingAddress: QuoteAddress,
    selectedShippingMethod: AvailableShippingMethod,
    availableShippingMethods: AvailableShippingMethod[],
  ) => {
    const selectedShippingPrice = {
      currency: QuoteConfig.defaultCurrencyCode,
      currency_symbol: QuoteConfig.defaultCurrencySymbol,
      value: selectedShippingMethod?.charges ?? 0,
    };
    return [
      {
        available_shipping_methods: availableShippingMethods
          ? this.buildCartAddressAvailableShippingMethods(
              availableShippingMethods,
            )
          : null,
        city: quoteShippingAddress.customer_city,
        company: quoteShippingAddress.customer_company,
        country: {
          code: quoteShippingAddress.customer_country_id,
          label: quoteShippingAddress.customer_country_id,
        },
        customer_notes: '',
        firstname: quoteShippingAddress.customer_firstname,
        items_weight: totalWeight,
        lastname: quoteShippingAddress.customer_lastname,
        postcode: quoteShippingAddress.customer_postcode,
        latitude: quoteShippingAddress?.latitude,
        longitude: quoteShippingAddress?.longitude,
        tag: quoteShippingAddress?.tag ?? null,
        map_address: quoteShippingAddress?.map_address ?? null,
        customer_street_2: quoteShippingAddress?.customer_street_2 ?? null,
        region: {
          code: quoteShippingAddress.customer_region_id,
          label: quoteShippingAddress.customer_region,
        },
        selected_shipping_method: selectedShippingMethod
          ? {
              amount: selectedShippingPrice,
              base_amount: selectedShippingPrice,
              carrier_code: selectedShippingMethod.carrier_code,
              carrier_title: selectedShippingMethod.carrier_title,
              method_code: selectedShippingMethod.method_code,
              method_title: selectedShippingMethod.method_title,
            }
          : null,
        street: quoteShippingAddress?.customer_street?.split(' '),
        telephone: quoteShippingAddress.customer_telephone,
        customer_address_id: quoteShippingAddress.customer_address_id,
      },
    ];
  };

  /**
   * It builds AvailableShippingMethod[] type of data to send in cart response
   * @param availableShippingMethods AvailableShippingMethod[]
   * @returns
   */
  buildCartAddressAvailableShippingMethods = (
    availableShippingMethods: AvailableShippingMethod[],
  ) => {
    return availableShippingMethods.map((o) => {
      const shippingPrice = {
        currency: QuoteConfig.defaultCurrencyCode,
        currency_symbol: QuoteConfig.defaultCurrencySymbol,
        value: o.charges,
      };
      return {
        amount: shippingPrice,
        available: true,
        base_amount: shippingPrice,
        carrier_code: o.carrier_code,
        carrier_title: o.carrier_title,
        error_message: null,
        method_code: o.method_code,
        method_title: o.method_title,
        price_excl_tax: shippingPrice,
        price_incl_tax: shippingPrice,
      };
    });
  };

  /**
   * It builds QuoteItem object for requested product
   * @param quoteId number
   * @param product ProductDataWithQuantity
   * @returns
   */
  builditemsObj(
    quoteId: number,
    product: ProductDataWithQuantity,
    productTaxes: ProductTaxesResponse,
    skuwiseParentIdQty: SkuWiseParentIdAndQTy,
    parentMappedPrice: ParentUnitPriceInterface,
    getItemsDiscount: (
      mrp: number,
      sellingPrice: number,
      qty: number,
      is_free_product?: boolean,
    ) => any,
    computeTierPrice: (
      tier_prices: any,
      productItem: any,
      parent_id: number,
      parentMappedPrice: ParentUnitPriceInterface,
    ) => number,
  ) {
    const parent_id = skuwiseParentIdQty?.[product.sku]?.parent_id ?? 0;
    let productPrice = product.price?.minimalPrice?.amount?.value;
    const regularPrice = product.price?.regularPrice?.amount?.value ?? 0;
    if (product?.tier_prices?.length && !product?.is_free_product) {
      productPrice = computeTierPrice(
        product.tier_prices,
        product,
        parent_id,
        parentMappedPrice,
      );
    }
    const { row_total_savings } = getItemsDiscount(
      regularPrice,
      productPrice,
      product.qty,
      product?.is_free_product,
    );
    const appliedTaxes = productTaxes[product?.tax_class_id] || [];
    const { appliedRules, taxPercent, taxAmount, appliedRuleTitles } =
      this.deriveProductTax(appliedTaxes, productPrice);
    const total_tax_amount = taxAmount * product.qty;
    const priceExclTax = productPrice - taxAmount;
    const rowTotalExclTax = productPrice * product.qty - total_tax_amount;
    return {
      quote_id: quoteId,
      product_id: product.id,
      store_id: AdminGlobalConfig.defaultStoreId,
      parent_item_id: null,
      is_virtual: product.type_id === ProductTypes.VIRTUAL ? true : false,
      sku: product.sku,
      name: product.name,
      description: product.short_description,
      weight: product.weight,
      qty: product.qty,
      price: priceExclTax,
      base_price: priceExclTax,
      row_total: rowTotalExclTax,
      base_row_total: rowTotalExclTax,
      row_total_with_discount: productPrice * product.qty,
      row_weight: product.weight * product.qty,
      product_type: product.type_id,
      product_tax_class_id: product.tax_class_id,
      price_incl_tax: productPrice,
      base_price_incl_tax: productPrice,
      row_total_incl_tax: productPrice * product.qty,
      base_row_total_incl_tax: productPrice * product.qty,
      reward_points_earned: product.reward_point_product,
      applied_tax_ids: appliedRules,
      tax_percent: taxPercent,
      tax_amount: total_tax_amount,
      base_tax_amount: total_tax_amount,
      base_tax_before_discount: total_tax_amount,
      tax_before_discount: total_tax_amount,
      applied_tax_titles: appliedRuleTitles,
      manufacturer: product.manufacturer,
      categories: product.categories?.map((o) => o.name).toString(),
      is_cod: product.is_cod === '1' ? true : false,
      item_handling_fee: product?.dentalkart_custom_fee ?? 0,
      row_total_savings: row_total_savings,
      discount_amount: 0,
      discount_percent: 0,
      parent_id: parent_id,
      is_free_product: product?.is_free_product ?? false,
      extension_attribute: product?.buying_guide_qty
        ? { buying_guide_qty: +product.buying_guide_qty }
        : null,
    };
  }

  /**
   * It build data to be stored in QuoteShippingRate table
   * @param shippingMethod AvailableShippingMethod - selected shipping method
   * @param quoteAddressId shipping address id
   * @returns
   */
  buildQuoteShippingRateObj = (
    shippingMethod: AvailableShippingMethod,
    quoteAddressId: number,
  ) => {
    return {
      quote_address_id: quoteAddressId,
      carrier: shippingMethod.carrier_code,
      carrier_title: shippingMethod.carrier_title,
      code: shippingMethod.carrier_code + '' + shippingMethod.method_code,
      method: shippingMethod.method_code,
      method_title: shippingMethod.method_title,
      price: shippingMethod.charges ?? 0,
    };
  };

  /**
   * It derives tax applied on a product based on
   * product's tax-class-id
   * @param appliedTaxes ProductTax[]
   * @param price_inc_tax price of product inc tax
   * @returns
   */
  deriveProductTax = (appliedTaxes: ProductTax[], price_inc_tax: number) => {
    let appliedRules = '';
    let taxPercent = 0;
    let taxAmount = 0;
    let appliedRuleTitles = '';
    for (const tax of appliedTaxes) {
      appliedRules += appliedRules.length ? `,${tax.rate_id}` : tax.rate_id;
      taxPercent += tax.rate;
      taxAmount += price_inc_tax - (price_inc_tax * 100) / (100 + tax.rate);
      appliedRuleTitles += appliedRuleTitles.length
        ? `,${tax.title}`
        : tax.title;
    }
    return { appliedRules, taxPercent, taxAmount, appliedRuleTitles };
  };

  /**
   * It will return object for create new quote address or update existing quote address
   * @param CartAddress
   * @param regionId
   * @param customerEmail
   * @param customerId
   * @param addressType
   * @returns
   */
  mapQuoteAddressObj = (
    CartAddress: Record<string, any>,
    customerEmail: string,
    customerId: number,
    addressType: string,
  ) => {
    return {
      customer_firstname: CartAddress.address.firstname ?? null,
      customer_lastname: CartAddress.address.lastname ?? null,
      customer_region: CartAddress.address.region ?? null,
      customer_region_code: CartAddress.address.region_code ?? null,
      customer_region_id: CartAddress.address.region_id ?? null,
      address_type: addressType,
      customer_company: CartAddress.address.company ?? null,
      customer_street: CartAddress.address?.street?.toString() ?? null,
      customer_city: CartAddress.address.city ?? null,
      customer_telephone: CartAddress.address.telephone ?? null,
      customer_address_id: CartAddress.customer_address_id ?? null,
      customer_country_id: CartAddress.address.country_code ?? null,
      gst_id: CartAddress.address.gst_id ?? null,
      customer_id: customerId,
      customer_email: customerEmail,
      customer_postcode: CartAddress.address.postcode ?? null,
      latitude: CartAddress.address?.latitude,
      longitude: CartAddress.address?.longitude,
      tag: CartAddress.address?.tag ?? null,
      map_address: CartAddress.address?.map_address ?? null,
      customer_street_2: CartAddress.address?.customer_street_2 ?? null,
    };
  };

  /**
   * It builds rewardsCartInfo object that can be sent as response
   * to dkApplyRewards mutation
   * @param discountObj QuoteDiscountsObject
   * @param userRewardsDetails
   * @param rewardsGlobalConfig
   * @returns
   */
  buildApplyRewardsResponse = (
    discountObj: QuoteDiscountsObject,
    userRewardsDetails: any,
    rewardsGlobalConfig: any,
  ) => {
    return {
      applied_points: discountObj.reward_points,
      applied_points_value: discountObj.discount_amount,
      balance: userRewardsDetails.available_point,
      currency: QuoteConfig.defaultCurrencyCode,
      earn_points: userRewardsDetails.earned_points,
      earn_points_value:
        userRewardsDetails.earned_points /
        rewardsGlobalConfig.spent_configuration,
      exchange_rate: rewardsGlobalConfig.spent_configuration,
      exchange_rate_currency: QuoteConfig.defaultCurrencyCode,
      exchange_rate_info: '',
      max_point_message: '',
      max_point_to_checkout: rewardsGlobalConfig.max_applicable_points,
      reward_gain_info: '',
      reward_icon_url: '',
      reward_term: '',
    };
  };

  getGlobalError(CartAddress: QuoteAddress, item_error_msg?: any) {
    try {
      const global_errors = [];
      if (!CartAddress) {
        global_errors.push({
          code: 'ADDROOIN',
          message: addressErrors['NO_ADDRESS_ERR'],
        });
      }

      if (CartAddress?.customer_country_id === 'IN') {
        if (!CartAddress.customer_postcode) {
          global_errors.push({
            code: 'ADDROOIN',
            message: addressErrors['NO_POSTCODE_ERR'],
          });
        }
        if (!CartAddress.customer_region) {
          global_errors.push({
            code: 'ADDROOIN',
            message: addressErrors['NO_REGION_ERR'],
          });
        }

        if (!CartAddress.customer_region_id) {
          global_errors.push({
            code: 'ADDROOIN',
            message: addressErrors['NO_REGION_ID_ERR'],
          });
        }
      }

      if (item_error_msg) {
        global_errors.push({
          code: 'ITEM00ERR',
          message: item_error_msg,
        });
      }
      return global_errors;
    } catch (e) {
      return [];
    }
  }

  itemsErrMsg(errors?: any) {
    if (!errors) return {};
    const msg = {},
      gloabalError = [];
    let errorMsg = ``;
    try {
      Object.keys(errors).map((keys) => {
        errors?.[keys]?.map((key: any) => {
          if (key.code in msg) {
            return;
          }
          msg[key.code] = itemsErrors[key.code] || '';
          if (msg[key.code]) {
            errorMsg += msg[key.code];
            gloabalError.push({
              code: key.code,
              message: msg[key.code],
            });
          }
        });
      });
      return { errorMsg, gloabalError };
    } catch (e) {
      return null;
    }
  }

  mapMagentoCartToexistingCart(data: any) {
    const {
      grand_total,
      subtotal,
      overweight_delivery_charges,
      total_savings,
    } = this.getTotalFigures(data.totals);
    return {
      quote: {
        items_count: +data.items_count,
        items_qty: +data.items_qty,
        grand_total: grand_total,
        subtotal: subtotal,
        subtotal_with_discount: +data.subtotal_with_discount,
        base_subtotal_with_discount: +data.subtotal_with_discount,
        base_grand_total: grand_total,
        coupon_code: null,
        rewards_discount: 0,
        base_subtotal: subtotal,
        overweight_delivery_charges: overweight_delivery_charges || 0,
        total_savings: total_savings,
        store_id: AdminGlobalConfig.defaultStoreId,
        is_active: data.is_active,
        is_virtual: data.is_virtual,
        base_currency_code: data.base_currency_code,
        store_currency_code: AdminGlobalConfig.defaultStoreCurrencyCode,
        quote_currency_code: QuoteConfig.defaultCurrencyCode,
        quote_currency_symbol: QuoteConfig.defaultCurrencySymbol,
        customer_id: +data.customer_id,
        customer_tax_class_id: +data.customer_tax_class_id,
        customer_group_id: +data.customer_group_id,
        customer_email: data.customer_email,
        customer_firstname: data.customer_firstname,
        customer_lastname: data.customer_lastname,
        customer_is_guest: +data.customer_is_guest ? true : false,
        remote_ip: null,
        applied_rule_ids: '',
        global_currency_code: data.global_currency_code,
        customer_gst_id: data.customer_taxvat,
        customer_gender: data.customer_gender,
      },
      address: {
        shipping: data.shipping_address
          ? {
              ...this.mapMagentoAddress(data.shipping_address),
            }
          : null,
        billing: data.billing_address
          ? {
              ...this.mapMagentoAddress(data.billing_address),
            }
          : null,
      },
    };
  }

  mapMagentoAddress(data: any) {
    return {
      customer_firstname: data.customer_firstname,
      customer_lastname: data.customer_firstname,
      customer_region: data.region,
      address_type: data.address_type,
      customer_company: data.company,
      customer_street: data.street,
      customer_city: data.city,
      customer_telephone: data.telephone,
      customer_address_id: data.customer_address_id,
      customer_country_id: data?.country_id ?? 'IN',
      customer_id: +data.customer_id,
      customer_email: data.customer_email,
      customer_postcode: data.postcode,
      customer_region_id: +data.region_id || 0,
    };
  }

  mapMagentoCartItems(items: any, quoteId: number) {
    return items.map((item: any) => {
      return {
        quote_id: quoteId,
        product_id: item.product_id,
        store_id: AdminGlobalConfig.defaultStoreId,
        parent_item_id: null,
        is_virtual: item.product_type === ProductTypes.VIRTUAL ? true : false,
        sku: item.sku,
        name: item.name,
        description: item.description,
        weight: +item.weight || null,
        qty: +item.qty || null,
        price: +item.price || null,
        base_price: +item.price || null,
        row_total: +item.row_total || null,
        base_row_total: +item.base_row_total || null,
        row_total_with_discount: +item.row_total_with_discount || null,
        row_weight: +item.row_weight || null,
        product_type: item.product_type,
        product_tax_class_id: null,
        price_incl_tax: +item.price_incl_tax || null,
        base_price_incl_tax: +item.base_price_incl_tax || null,
        row_total_incl_tax: +item.row_total_incl_tax || null,
        base_row_total_incl_tax: +item.base_row_total_incl_tax || null,
        discount_amount: +item.discount_amount,
        discount_percent: +item.discount_percent || null,
      };
    });
  }

  getTotalFigures(totals: any) {
    const totalFigures = {
      grand_total: 0,
      subtotal: 0,
      overweight_delivery_charges: 0,
      total_savings: 0,
      rewards_discount: 0,
    };
    for (const obj of totals) {
      if (obj['code'] === 'grand_total') {
        totalFigures['grand_total'] = obj['value'];
      }
      if (obj['code'] === 'total_savings') {
        totalFigures['total_savings'] = obj['value'];
      }
      if (obj['code'] === 'rewardsdiscount') {
        totalFigures['rewards_discount'] = obj['value'];
      }
      if (obj['code'] === 'handling_fee') {
        totalFigures['overweight_delivery_charges'] = obj['value'];
      }
      if (obj['code'] === 'subtotal') {
        totalFigures['subtotal'] = obj['value'];
      }
    }
    return totalFigures;
  }

  itemPercentSavings(regularPrice: number, minimalPrice: number, qty: number) {
    if (!regularPrice && !minimalPrice) return 0;
    if (regularPrice < minimalPrice) return 0;
    const mrp_amount = regularPrice * qty;
    const selling_amount = minimalPrice * qty;
    const percentage = ((mrp_amount - selling_amount) / mrp_amount) * 100;
    return +percentage.toFixed(2);
  }

  buildRestCartResponse(data: CartResponse | { cart: CartResponse }) {
    if ('cart' in data) {
      data.cart.items = this.sortCartItems(data.cart.items);
    } else {
      data.items = this.sortCartItems(data.items);
    }
    return data;
  }

  sortCartItems(items: CartItem[]): CartItem[] {
    const { outOfStockItems, freeProductItems, membershipItems, inStockItems } =
      items.reduce(
        (accumulator, currentItem) => {
          currentItem.is_member_ship_product = false;
          if (currentItem.product.stock_status === StockStatuses.OUT_OF_STOCK) {
            accumulator.outOfStockItems.push(currentItem);
          } else {
            if (MEMBERSHIP_PRODUCTS.indexOf(+currentItem.product.id) !== -1) {
              currentItem.is_member_ship_product = true;
              accumulator.membershipItems.push(currentItem);
            } else if (currentItem.is_free_product) {
              accumulator.freeProductItems.push(currentItem);
            } else {
              accumulator.inStockItems.push(currentItem);
            }
          }
          return accumulator;
        },
        {
          outOfStockItems: [],
          freeProductItems: [],
          membershipItems: [],
          inStockItems: [],
        },
      );

    // Sort IN_STOCK items by id in descending order (last added at top)
    inStockItems.sort((a, b) => b.item_id - a.item_id);

    return [
      ...outOfStockItems,
      ...freeProductItems,
      ...membershipItems,
      ...inStockItems,
    ];
  }

  buildRestApiCartResponse = (
    quote: Quote,
    productDetails: ProductData[],
    quoteShippingAddress: QuoteAddress,
    availableShippingMethods: AvailableShippingMethod,
    skuWiseErrors?: any,
    memberShipInfo?: {
      is_member_ship_active: boolean;
      min_shipping_amount: number;
      delivery_charges: number;
    },
    billingAddress?: QuoteAddress,
  ) => {
    const { gloabalError } = this.itemsErrMsg(skuWiseErrors);
    const {
      items,
      freeItemsArr,
      total_rewards,
      total_regular_price,
      total_row_total_including_tax,
    } = this.buildRestCartItemsResponse(
      quote,
      quote.items,
      productDetails,
      skuWiseErrors,
    );

    const total_savings = this.getTotalSavings(
      +total_regular_price.toFixed(2),
      +total_row_total_including_tax.toFixed(2),
      +quote.discount_amount,
      +quote.rewards_discount,
      availableShippingMethods,
      memberShipInfo?.delivery_charges ?? null,
    );
    return {
      cart: {
        delivery_and_saving_text: this.deliveryAndSavingText(
          quoteShippingAddress?.customer_country_id ?? null,
          availableShippingMethods?.charges ?? null,
          +quote.subtotal,
          memberShipInfo?.is_member_ship_active ?? false,
          memberShipInfo?.min_shipping_amount ?? 0,
          total_savings,
        ),
        global_errors: gloabalError,
        cart_id: quote.masked_id,
        is_virtual: quote.is_virtual,
        total_quantity: +quote.items_qty,
        total_weight: quote.total_weight,
        coupon_code: quote.coupon_code ? { code: quote.coupon_code } : null,
        cart_currency: {
          code: QuoteConfig.defaultCurrencyCode,
          currency_symbol: QuoteConfig.defaultCurrencySymbol,
        },
        pricing_details: this.buildinRestCartPrices(
          quote,
          availableShippingMethods,
          +total_regular_price.toFixed(2),
          +total_row_total_including_tax.toFixed(2),
          memberShipInfo?.delivery_charges ?? null,
        ),
        rewards: {
          total_coins: total_rewards,
          monetary_value: '[2 Coins = ₹ 1]',
        },
        addresses: quoteShippingAddress
          ? {
              country: {
                name: quoteShippingAddress.customer_country_id,
                isoCode2: quoteShippingAddress.customer_country_id,
                isoCode3: quoteShippingAddress.customer_country_id,
              },
              firstname: quoteShippingAddress.customer_firstname,
              city: quoteShippingAddress.customer_city,
              lastname: quoteShippingAddress.customer_lastname,
              postcode: quoteShippingAddress.customer_postcode,
              region: {
                code: quoteShippingAddress.customer_region_id,
                label: quoteShippingAddress.customer_region,
              },
              latitude: quoteShippingAddress?.latitude,
              longitude: quoteShippingAddress?.longitude,
              tag: quoteShippingAddress?.tag ?? null,
              map_address: quoteShippingAddress?.map_address ?? null,
              customer_street_2:
                quoteShippingAddress?.customer_street_2 ?? null,
              street: quoteShippingAddress?.customer_street?.split(' '),
              customer_address_id:
                +quoteShippingAddress.customer_address_id || null,
            }
          : null,
        billing_address: billingAddress
          ? {
              customer_address_id:
                +quoteShippingAddress?.customer_address_id || null,
              country: {
                name: billingAddress.customer_country_id,
                isoCode2: billingAddress.customer_country_id,
                isoCode3: billingAddress.customer_country_id,
              },
              firstname: billingAddress.customer_firstname,
              city: billingAddress.customer_city,
              lastname: billingAddress.customer_lastname,
              postcode: billingAddress.customer_postcode,
              region: {
                code: billingAddress.customer_region_id,
                label: billingAddress.customer_region,
              },
              street: billingAddress?.customer_street?.split(' '),
            }
          : null,
        items: [...freeItemsArr, ...items],
      },
    };
  };

  /**
   * It builds CartPrice interaface typeto send as reponse
   * @param quote Quote
   * @returns
   */
  buildinRestCartPrices = (
    quote: Quote,
    availableShippingMethods: AvailableShippingMethod,
    itemRegularPrice: number,
    totalSellingPrice: number,
    delivery_charges?: number,
  ) => {
    const totalSavings = this.getTotalSavings(
      itemRegularPrice,
      totalSellingPrice,
      +quote.discount_amount,
      +quote.rewards_discount,
      availableShippingMethods,
      delivery_charges,
    );

    const savingOnRegularPrice =
      itemRegularPrice > totalSellingPrice
        ? itemRegularPrice - totalSellingPrice
        : 0;

    let savingMsg = '';
    if (
      delivery_charges &&
      delivery_charges > 0 &&
      availableShippingMethods?.charges === 0
    ) {
      savingMsg = `Including ₹${delivery_charges} savings on delivery charges.`;
    }

    return {
      applied_taxes: this.buildRestCartAppliedTaxes(quote.items),
      discounts: [
        {
          code: 'coupon',
          amount: {
            label: 'Coupon Discount',
            value: +quote.discount_amount || null,
          },
        },
        {
          code: 'reward',
          amount: {
            label: 'Reward Discount',
            value: +quote.rewards_discount || null,
          },
        },
      ],
      grand_total: {
        amount: {
          label: 'Grand Total',
          value: +quote.grand_total,
        },
      },
      overweight_delivery_charges: {
        amount: {
          label: 'Overweight Delivery',
          value: +quote.overweight_delivery_charges,
        },
      },
      shipping_charges: {
        amount: {
          label: 'Delivery Charges',
          value: availableShippingMethods?.charges ?? null,
          applicable_charges: delivery_charges,
        },
      },
      subtotal_including_tax: {
        amount: {
          label: 'Sub total inclusive taxes',
          value: +quote.subtotal_with_discount,
        },
      },
      subtotal_excluding_tax: {
        amount: {
          label: 'Sub Total',
          value: +quote.subtotal,
        },
      },
      subtotal_with_discount_excluding_tax: {
        amount: {
          label: 'Sub total exclusive taxes',
          value: +quote.subtotal_with_discount,
        },
      },
      total_savings: {
        amount: {
          label: 'Total Savings ',
          msg: savingMsg,
          value: totalSavings,
        },
      },
      item_total_regular_price: {
        amount: {
          label: 'Total regular price ',
          value: itemRegularPrice,
        },
      },
      item_total_selling_price: {
        amount: {
          label: 'Total selling price ',
          value: totalSellingPrice,
        },
      },
      saving_on_regular_price: {
        amount: {
          label: 'Total saving on regular price',
          value: savingOnRegularPrice,
        },
      },
    };
  };

  deliveryAndSavingText(
    country: string,
    deliveryCharges: number,
    subTotal: number,
    memberShipActive: boolean,
    minMemberShipAmount: number,
    totalSavings: number,
  ) {
    if (subTotal <= 0) return '';
    if (!country || country !== 'IN') {
      return totalSavings > 0
        ? `YAY ₹${totalSavings} saved on this order.`
        : '';
    }

    if (deliveryCharges === 0)
      return `YAY ₹${totalSavings} saved on this order, Free Delivery unlocked !`;

    if (memberShipActive && minMemberShipAmount > 0) {
      if (minMemberShipAmount - subTotal > 0) {
        return `₹${totalSavings} saved ! Add items worth ₹${
          minMemberShipAmount - subTotal
        } more for Free Delivery`;
      }
    }

    if (MIN_CART_SUBTOTAL_FOR_FREE_DELIVERY - subTotal > 0) {
      return `₹${totalSavings} saved ! Add items worth ₹${
        MIN_CART_SUBTOTAL_FOR_FREE_DELIVERY - subTotal
      } more for Free Delivery`;
    }

    return totalSavings > 0 ? `YAY ₹${totalSavings} saved on this order.` : '';
  }

  // buildRestCartItemsResponse(
  //   quote: Quote,
  //   items: QuoteItem[],
  //   products: ProductData[],
  //   skuWiseErrors: any,
  // ) {
  //   const freeItemsResponse = [];
  //   const itemsResponse = items.reduce(
  //     (acc, quoteItem) => {
  //       const product = products.find((p) => p.sku === quoteItem.sku);
  //       const regularPrice = product?.price?.regularPrice?.amount?.value ?? 0;

  //       if (quoteItem?.quoteItemPromotions?.length > 0) {
  //       }

  //       const itemResponse = {
  //         error_messages: skuWiseErrors?.[quoteItem.sku] ?? null,
  //         item_id: +quoteItem.quote_item_id,
  //         item_pricing_details: this.getRestItemPricingDetails(
  //           quote,
  //           quoteItem,
  //           regularPrice,
  //         ),
  //         product: this.buildRestCartItemProductInterfaceResponse(product),
  //         parent_id: product?.parent_id ?? null,
  //         qty_increments: 1,
  //         quantity: +quoteItem.qty,
  //         is_free_product:
  //           quoteItem.extension_attribute?.is_free_product ?? false,
  //         reward_point_product: +quoteItem.reward_points_earned || 0,
  //         stock_status: product.is_in_stock ? 1 : 0,
  //       };

  //       acc.items.push(itemResponse);

  //       if (!itemResponse.is_free_product) {
  //         acc.total_rewards +=
  //           itemResponse.reward_point_product * itemResponse.quantity;
  //         acc.total_regular_price +=
  //           itemResponse.item_pricing_details.row_total_regular_price.amount.value;
  //         acc.total_row_total_including_tax +=
  //           itemResponse.item_pricing_details.row_total_including_tax.amount.value;
  //       }
  //       return acc;
  //     },

  //     {
  //       items: [],
  //       total_rewards: 0,
  //       total_regular_price: 0,
  //       total_row_total_including_tax: 0,
  //     },
  //   );

  //   return itemsResponse;
  // }

  buildRestCartItemsResponse(
    quote: Quote,
    quoteItems: QuoteItem[],
    products: ProductData[],
    skuWiseErrors: any,
  ) {
    try {
      const {
        items,
        freeItemsArr,
        total_rewards,
        total_regular_price,
        total_row_total_including_tax,
        freeItemsMap,
      } = quoteItems.reduce(
        (acc, quoteItem) => {
          const product = products.find((p) => p.sku === quoteItem.sku);
          const regularPrice = product?.price?.regularPrice?.amount?.value ?? 0;
          const itemResponse = {
            error_messages: skuWiseErrors?.[quoteItem.sku] ?? null,
            item_id: +quoteItem.quote_item_id,
            item_pricing_details: this.getRestItemPricingDetails(
              quote,
              quoteItem,
              regularPrice,
            ),
            product: this.buildRestCartItemProductInterfaceResponse(product),
            parent_id: product?.parent_id ?? null,
            qty_increments: 1,
            quantity: +quoteItem.qty,
            // is_free_product:
            //   quoteItem.extension_attribute?.is_free_product ?? false,
            reward_point_product: +quoteItem.reward_points_earned || 0,
            stock_status: product.is_in_stock ? 1 : 0,
          };

          acc.items.push(itemResponse);

          // if (!itemResponse.is_free_product) {
          acc.total_rewards +=
            itemResponse.reward_point_product * itemResponse.quantity;
          acc.total_regular_price +=
            itemResponse.item_pricing_details.row_total_regular_price.amount.value;
          acc.total_row_total_including_tax +=
            itemResponse.item_pricing_details.row_total_including_tax.amount.value;
          // }
          this.buildItemPromotionResponse(
            quote,
            quoteItem,
            acc.freeItemsArr,
            acc.freeItemsMap,
            products,
            skuWiseErrors,
          );
          return acc;
        },
        {
          items: [],
          freeItemsArr: [],
          total_rewards: 0,
          total_regular_price: 0,
          total_row_total_including_tax: 0,
          freeItemsMap: new Map<string, number>(),
        },
      );

      this.buildAmountPromotionsItemResponse(
        quote,
        products,
        freeItemsArr,
        freeItemsMap,
        skuWiseErrors,
      );

      return {
        items,
        freeItemsArr,
        total_rewards,
        total_regular_price,
        total_row_total_including_tax,
      };
    } catch (err) {
      console.log(err);
    }

    // console.log(JSON.stringify(itemsResponse, null, 4), 'TESTSTTS');
  }

  private getRestItemPricingDetails(
    quote: Quote,
    quoteItem: QuoteItem,
    regularPrice: number,
  ) {
    return {
      discounts: [
        ...this.buildRestCartDiscounts(
          quote.discount?.discounts.filter(
            (d) => d.item_product_id === quoteItem.product_id,
          ) || [],
        ),
        ...quote.items
          .filter((i) => i.quote_item_id === quoteItem.quote_item_id)
          .map((i) => ({
            code: 'admin_discount',
            amount: {
              label: 'Admin discount',
              value: i.extension_attribute?.admin_discount_amount ?? 0,
            },
          })),
        {
          code: 'total_discount',
          amount: {
            label: 'Total discount',
            value: +quoteItem.row_total_with_discount || null,
          },
        },
        {
          code: 'percentage_saving_on_regular_price',
          amount: {
            label: 'Saving on regular price',
            value: this.itemPercentSavings(
              regularPrice,
              +quoteItem.price_incl_tax,
              +quoteItem.qty,
            ),
          },
        },
      ],
      price: {
        amount: {
          label: 'Item Price',
          value: +quoteItem.price || null,
        },
      },
      row_total: {
        amount: {
          label: 'Row Total',
          value: +quoteItem.row_total || null,
        },
      },
      row_total_including_tax: {
        amount: {
          label: 'Row total including tax',
          value: +quoteItem.row_total_incl_tax || 0,
        },
      },
      row_total_regular_price: {
        amount: {
          label: 'Row Total regular price',
          value: regularPrice ? regularPrice * quoteItem.qty : 0,
        },
      },
    };
  }

  getTotalSavings(
    itemRegularPrice: number,
    totalSellingPrice: number,
    discount_amount: number,
    rewards_discount: number,
    availableShippingMethods: AvailableShippingMethod,
    delivery_charges?: number,
  ) {
    const savingOnRegularPrice =
      itemRegularPrice > totalSellingPrice
        ? itemRegularPrice - totalSellingPrice
        : 0;

    let totalSavings: number =
      savingOnRegularPrice + (+discount_amount || 0) + (+rewards_discount || 0);

    if (
      delivery_charges &&
      delivery_charges > 0 &&
      availableShippingMethods?.charges === 0
    ) {
      totalSavings += delivery_charges;
    }

    return parseFloat(totalSavings.toFixed(2));
  }
}
