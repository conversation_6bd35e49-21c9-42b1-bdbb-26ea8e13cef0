import {
  CartItem,
  CartPaymentInfoResponse,
  GuestCartResponse,
  productsForService,
  productsGQLResponse,
  PyamentInfoItem,
} from 'src/interface/cart-external-response';
import { sumBy, find } from 'lodash';
import {
  AddressTypes,
  CancellationAvailableStatuses,
  FetchOrderStatuses,
  OrderStatuses,
  PaymentMethods,
  ReturnAvailableStatuses,
  ORDER_VALIDADTION_ERROR,
} from 'src/config/constants';
import { PaymentCreateOrderResponse } from 'src/interface/payment-service';
import { DeliveryDay } from 'src/interface/delivery-info-request-response';
import { SalesOrder } from 'src/database/entities/sales-order';
import { SalesOrderItem } from 'src/database/entities/sales-order-item';
import { OrderHelper } from 'src/utils/order.helper';
import { BadRequestException, Injectable } from '@nestjs/common';
import config from 'src/config/env';
import { ExtensionAttributeInput } from '../interface/place-order-request';

@Injectable()
export class OrderMapper {
  constructor(private readonly orderHelper: OrderHelper) {}

  /**
   * It maps data according to Sales_Order table
   * @param cartInfo GuestCartResponse
   * @param paymentInfo CartPaymentInfoResponse
   * @param remoteIp string
   * @returns
   */
  buildSalesOrderObj = async (
    cartInfo: GuestCartResponse,
    paymentInfo: CartPaymentInfoResponse,
    remoteIp: string,
    paymentMethod: string,
    platform?: string,
    version?: string,
  ) => {
    let status = OrderStatuses.NEW_ORDER;
    if (paymentMethod === PaymentMethods.RAZOR_PAY) {
      status = OrderStatuses.PAYMENT_PENDING;
    }
    const app_platform = platform === 'undefined' ? null : platform;
    const app_version = version === 'undefined' ? null : version;
    const country_id = cartInfo?.billing_address?.country_id ?? null;

    const grandTotal = find(paymentInfo.totals.total_segments, {
      code: 'grand_total',
    })?.value;

    //validate grandTotal subtotal_incl_tax
    if (grandTotal <= 0) {
      throw new BadRequestException(
        ORDER_VALIDADTION_ERROR['invalid_grand_total'],
      );
    }

    if (paymentInfo.totals.subtotal_incl_tax <= 0) {
      throw new BadRequestException(
        ORDER_VALIDADTION_ERROR['invalid_subtotal'],
      );
    }

    return {
      status,
      coupon_code: paymentInfo.totals.coupon_code,
      protect_code: null,
      shipping_description: null,
      is_virtual: cartInfo.is_virtual,
      store_id: cartInfo.store_id,
      customer_id: cartInfo.customer.id || null,
      base_discount_amount: paymentInfo.totals.base_discount_amount,
      base_discount_canceled: null,
      base_discount_refunded: null,
      base_grand_total: paymentInfo.totals.base_grand_total,
      base_shipping_amount: paymentInfo.totals.base_shipping_amount,
      base_shipping_canceled: null,
      base_shipping_invoiced: null,
      base_shipping_refunded: null,
      base_shipping_tax_amount: paymentInfo.totals.base_shipping_tax_amount,
      base_shipping_tax_refunded: null,
      base_subtotal: paymentInfo.totals.subtotal,
      base_discount_invoiced: null,
      base_subtotal_canceled: null,
      base_subtotal_invoiced: null,
      base_subtotal_refunded: null,
      base_tax_amount: paymentInfo.totals.base_tax_amount,
      base_tax_canceled: null,
      base_tax_invoiced: null,
      base_tax_refunded: null,
      base_to_global_rate: cartInfo.currency.base_to_global_rate,
      base_to_order_rate: null,
      base_total_canceled: null,
      base_total_invoiced: null,
      base_total_invoiced_cost: null,
      base_total_offline_refunded: null,
      base_total_online_refunded: null,
      base_total_paid: null,
      base_total_qty_ordered: paymentInfo.totals.items_qty,
      base_total_refunded: null,
      discount_amount: paymentInfo.totals.discount_amount,
      discount_canceled: null,
      discount_invoiced: null,
      discount_refunded: null,
      grand_total: grandTotal,
      shipping_amount: paymentInfo.totals.shipping_amount,
      shipping_canceled: null,
      shipping_invoiced: null,
      shipping_refunded: null,
      shipping_tax_amount: paymentInfo.totals.shipping_tax_amount,
      shipping_tax_refunded: null,
      subtotal: paymentInfo.totals.subtotal,
      subtotal_canceled: null,
      subtotal_invoiced: null,
      subtotal_refunded: null,
      tax_amount: paymentInfo.totals.tax_amount,
      tax_canceled: null,
      tax_invoiced: null,
      tax_refunded: null,
      total_canceled: null,
      total_invoiced: null,
      total_offline_refunded: null,
      total_online_refunded: null,
      total_paid: null,
      total_qty_ordered: cartInfo.items_qty,
      total_refunded: null,
      customer_is_guest: cartInfo.customer.id ? false : true,
      billing_address_id: cartInfo.billing_address.id,
      customer_group_id: cartInfo.customer.group_id || null,
      edit_increment: null,
      payment_auth_expiration: null,
      quote_address_id: null,
      quote_id: cartInfo.items[0].quote_id,
      shipping_address_id:
        cartInfo.extension_attributes.shipping_assignments[0]?.shipping?.address
          ?.id ?? null,
      adjustment_negative: null,
      adjustment_positive: null,
      base_adjustment_negative: null,
      base_adjustment_positive: null,
      base_shipping_discount_amount:
        paymentInfo.totals.base_shipping_discount_amount,
      base_subtotal_incl_tax: paymentInfo.totals.subtotal_incl_tax,
      base_total_due: null,
      shipping_discount_amount: paymentInfo.totals.shipping_discount_amount,
      subtotal_incl_tax: paymentInfo.totals.subtotal_incl_tax,
      total_due: null,
      weight: null,
      increment_id: await this.orderHelper.generateIncrementIdV2(
        app_platform,
        app_version,
        country_id,
      ),
      applied_rule_ids: cartInfo?.applied_rule_ids ?? '',
      base_currency_code: cartInfo.currency.base_currency_code,
      customer_email: cartInfo.billing_address.email,
      customer_firstname: cartInfo.billing_address.firstname,
      customer_lastname: cartInfo.billing_address.lastname,
      discount_description: null,
      global_currency_code: cartInfo.currency.global_currency_code,
      hold_before_state: null,
      hold_before_status: null,
      order_currency_code: cartInfo.currency.quote_currency_code,
      original_increment_id: null,
      relation_child_id: null,
      relation_child_real_id: null,
      relation_parent_id: null,
      relation_parent_real_id: null,
      remote_ip: remoteIp,
      shipping_method:
        cartInfo.extension_attributes.shipping_assignments[0]?.shipping
          ?.method ?? null,
      store_currency_code: cartInfo.currency.store_currency_code,
      store_name: cartInfo.customer.created_in || null,
      customer_note: null,
      total_item_count: cartInfo.items_count,
      discount_tax_compensation_amount: null,
      base_discount_tax_compensation_amount: null,
      shipping_discount_tax_compensation_amount: null,
      base_shipping_discount_tax_compensation_amnt: null,
      discount_tax_compensation_invoiced: null,
      base_discount_tax_compensation_invoiced: null,
      discount_tax_compensation_refunded: null,
      base_discount_tax_compensation_refunded: null,
      shipping_incl_tax: paymentInfo.totals.shipping_incl_tax,
      base_shipping_incl_tax: paymentInfo.totals.base_shipping_incl_tax,
      is_active_membership: cartInfo.is_active_membership,
      rewards_discount:
        paymentInfo.totals.total_segments.find(
          (o) => o.code === 'rewardsdiscount',
        )?.value || 0,
      order_currency_symbol: paymentInfo.totals.quote_currency_code,
      handling_fee:
        find(paymentInfo.totals.total_segments, {
          code: 'handling_fee',
        })?.value ?? null,
      platform: app_platform,
      app_version: app_version,
    };
  };

  /**
   * It builds data that can be stored in sales_order_address table
   * Both "shipping" & "billing" data is generated here
   * @param cartInfo GuestCartResponse
   * @param orderId number
   * @returns
   */
  buildSalesOrderAddressObj = (
    cartInfo: GuestCartResponse,
    orderId: number,
  ) => {
    return {
      billing_address: {
        order_id: orderId,
        customer_address_id: cartInfo.billing_address.id,
        quote_address_id: null,
        region_id: cartInfo.billing_address.region_id,
        customer_id: cartInfo.customer.id || null,
        region: cartInfo.billing_address.region,
        postcode: cartInfo.billing_address.postcode,
        lastname: cartInfo.billing_address.lastname,
        street: cartInfo.billing_address.street.toString(),
        city: cartInfo.billing_address.city,
        latitude: cartInfo.billing_address?.latitude,
        longitude: cartInfo.billing_address?.longitude,
        tag: cartInfo.billing_address?.tag,
        map_address: cartInfo.billing_address?.map_address,
        customer_street_2: cartInfo.billing_address?.customer_street_2,
        email: cartInfo.billing_address.email,
        telephone: cartInfo.billing_address.telephone,
        country_id: cartInfo.billing_address.country_id,
        firstname: cartInfo.billing_address.firstname,
        address_type: AddressTypes.BILLING,
        company: null,
        gst_id: this.getTaxInfo(
          cartInfo?.customer?.taxvat,
          cartInfo?.billing_address?.gst_id,
        ),
        gst_is_valid: true,
      },
      shipping_address: cartInfo.extension_attributes.shipping_assignments[0]
        ?.shipping
        ? {
            order_id: orderId,
            customer_address_id:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.id,
            quote_address_id: null,
            region_id:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.region_id,
            customer_id: cartInfo.customer.id || null,
            region:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.region,
            postcode:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.postcode,
            lastname:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.lastname,
            street:
              cartInfo.extension_attributes.shipping_assignments[0].shipping.address.street.toString(),
            city: cartInfo.extension_attributes.shipping_assignments[0].shipping
              .address.city,
            latitude:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address?.latitude,
            longitude:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address?.longitude,
            tag: cartInfo.extension_attributes.shipping_assignments[0].shipping
              .address?.tag,
            map_address:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address?.map_address,
            customer_street_2:
              cartInfo.extension_attributes.shipping_assignments[0]?.shipping
                .address?.customer_street_2,
            email:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.email,
            telephone:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.telephone,
            country_id:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.country_id,
            firstname:
              cartInfo.extension_attributes.shipping_assignments[0].shipping
                .address.firstname,
            address_type: AddressTypes.SHIPPING,
            company: null,
            gst_id: this.getTaxInfo(
              cartInfo?.customer?.taxvat,
              cartInfo?.extension_attributes?.shipping_assignments[0]?.shipping
                ?.address?.gst_id,
            ),
            gst_is_valid: true,
          }
        : null,
    };
  };

  private getTaxInfo(
    customerTaxVat: string | undefined,
    taxVat: string | undefined,
  ) {
    if (taxVat) return taxVat;

    if (customerTaxVat) return customerTaxVat;

    return null;
  }

  /**
   * It builds data that can be stored in sales_order_tax table
   * @param paymentInfo CartPaymentInfoResponse
   * @param orderId number
   * @returns
   */
  buildSalesOrderTaxObj = (
    paymentInfo: CartPaymentInfoResponse,
    orderId: number,
  ) => {
    return {
      order_id: orderId,
      code: null,
      title: null,
      percent: sumBy(paymentInfo.totals.items, 'tax_percent'),
      amount: paymentInfo.totals.tax_amount,
      base_amount: paymentInfo.totals.base_tax_amount,
      base_real_amount: null,
    };
  };

  /**
   * It build sales_order_payment table object
   * @param paymentInfo CartPaymentInfoResponse
   * @param orderId unique order-id associated
   * @param paymentMethodCode method received from request
   * @returns
   */
  buildSalesOrderPaymentObj = (
    paymentInfo: CartPaymentInfoResponse,
    orderId: number,
    paymentMethodCode: string,
    razorpayOrder?: PaymentCreateOrderResponse,
  ) => {
    return {
      order_id: orderId,
      base_shipping_captured: null,
      shipping_captured: null,
      amount_refunded: null,
      base_amount_paid: null,
      amount_canceled: null,
      base_amount_authorized: null,
      base_amount_paid_online: null,
      base_amount_refunded_online: null,
      base_shipping_amount: paymentInfo.totals.base_shipping_amount,
      shipping_amount: paymentInfo.totals.shipping_amount,
      amount_paid: null,
      amount_authorized: null,
      base_amount_ordered: null,
      base_shipping_refunded: null,
      shipping_refunded: null,
      base_amount_refunded: null,
      amount_ordered: null,
      base_amount_canceled: null,
      additional_data: null,
      method: paymentMethodCode,
      additional_information: null,
      razorpay_order_id: razorpayOrder?.referenceNumber || null,
      order_receipt_id: razorpayOrder?.receiptId || null,
      notes: razorpayOrder?.notes || null,
    };
  };

  /**
   * It builds sales_order_item & sales_order_tax_item objects
   * for each product present in associated order
   * @param cartInfo GuestCartResponse
   * @param paymentInfo CartPaymentInfoResponse
   * @param orderId unique order-id associated
   * @param products productsGQLResponse
   * @param taxId tax-id associated with order
   * @returns
   */
  buildSalesOrderItemObj = (
    cartInfo: GuestCartResponse,
    paymentInfo: CartPaymentInfoResponse,
    orderId: number,
    products: productsGQLResponse,
    taxId: number,
    deliveryDays?: DeliveryDay[],
  ) => {
    const items = [];
    for (const key in cartInfo.items) {
      const cartItem = cartInfo.items[key];
      const paymentInfoItem = find(paymentInfo.totals.items, {
        item_id: cartItem.item_id,
      });
      const product = find(products.productForService, { sku: cartItem.sku });

      // Find delivery info for this product
      const deliveryInfo = deliveryDays?.find(
        (dd) => dd.product_id === +product.id,
      );

      const orderItem = {
        ...this.buildOrderItem(
          cartItem,
          paymentInfoItem,
          orderId,
          product,
          cartInfo.store_id,
        ),
        tax: this.buildTaxItem(taxId, paymentInfoItem),
        itemExtraInfo: this.buildItemExtraInfo(
          cartItem,
          deliveryInfo
            ? {
                days: deliveryInfo.days,
                dispatch_days: deliveryInfo.dispatch_days,
                warehouse_code: deliveryInfo.warehouse_code,
              }
            : undefined,
        ),
        salesOrderItemPromotions: this.mapItemPromotions(cartItem, products),
      };

      items.push(orderItem);
    }
    return items;
  };

  /**
   * It maps product details that can be stored in sales_order_item
   * @param item CartItem
   * @param paymentInfoItem PyamentInfoItem
   * @param orderId number
   * @param product productData
   * @param storeId number
   * @returns
   */
  buildOrderItem = (
    item: CartItem,
    paymentInfoItem: PyamentInfoItem,
    orderId: number,
    product: productsForService,
    storeId: number,
  ) => {
    return {
      order_id: orderId,
      parent_item_id: item?.parent_id ?? null,
      quote_item_id: item.item_id,
      store_id: storeId,
      product_id: product.id,
      product_sku: product.sku,
      product_name: item.name,
      product_type: item.product_type,
      product_options: '',
      weight: product.weight,
      is_virtual: item.product_type === 'virtual' ? true : false,
      applied_rule_ids: '',
      qty_backordered: null,
      qty_canceled: null,
      qty_invoiced: null,
      qty_ordered: item.qty,
      qty_refunded: null,
      qty_shipped: null,
      price: paymentInfoItem.price,
      base_price: paymentInfoItem.base_price,
      original_price: paymentInfoItem.price,
      base_original_price: paymentInfoItem.base_price,
      tax_percent: paymentInfoItem.tax_percent,
      tax_amount: paymentInfoItem.tax_amount,
      base_tax_amount: paymentInfoItem.base_tax_amount,
      tax_invoiced: null,
      base_tax_invoiced: null,
      discount_percent: paymentInfoItem.discount_percent,
      discount_amount: paymentInfoItem.discount_amount,
      base_discount_amount: paymentInfoItem.base_discount_amount,
      discount_invoiced: null,
      base_discount_invoiced: null,
      amount_refunded: null,
      base_amount_refunded: null,
      row_total: paymentInfoItem.row_total,
      base_row_total: paymentInfoItem.base_row_total,
      row_invoiced: null,
      base_row_invoiced: null,
      row_weight: product.weight * item.qty,
      base_tax_before_discount: null,
      tax_before_discount: null,
      price_incl_tax: paymentInfoItem.price_incl_tax,
      base_price_incl_tax: paymentInfoItem.base_price_incl_tax,
      row_total_incl_tax: paymentInfoItem.row_total_incl_tax,
      base_row_total_incl_tax: paymentInfoItem.base_row_total_incl_tax,
      discount_tax_compensation_amount: null,
      base_discount_tax_compensation_amount: null,
      discount_tax_compensation_invoiced: null,
      base_discount_tax_compensation_invoiced: null,
      discount_tax_compensation_refunded: null,
      base_discount_tax_compensation_refunded: null,
      tax_canceled: null,
      discount_tax_compensation_canceled: null,
      tax_refunded: null,
      base_tax_refunded: null,
      discount_refunded: null,
      base_discount_refunded: null,
      free_shipping: null,
      thumbnail_url: product.thumbnail_url,
      url_key: product.url_key,
      reward_points: item.is_free_product ? 0 : product.reward_point_product,
      product_expiry_date: product.pd_expiry_date,
      item_handling_fee: product.dentalkart_custom_fee,
    };
  };

  /**
   * It maps product-tax details that can be stored in sales_order_tax_item
   * @param taxId tax-id associated with item
   * @param paymentInfoItem PyamentInfoItem
   * @returns
   */
  buildTaxItem = (taxId: number, paymentInfoItem: PyamentInfoItem) => {
    return {
      tax_id: taxId,
      item_id: paymentInfoItem.item_id,
      tax_percent: paymentInfoItem.tax_percent,
      amount: paymentInfoItem.tax_amount,
      base_amount: paymentInfoItem.base_tax_amount,
      real_amount: paymentInfoItem.tax_amount,
      real_base_amount: paymentInfoItem.base_tax_amount,
    };
  };

  /**
   * It maps order & its items to be sent in response
   * @param orderObj SalesOrder
   * @returns
   */
  mapUserOrder = (orderObj: SalesOrder) => {
    return {
      can_cancel:
        CancellationAvailableStatuses.indexOf(orderObj.status) !== -1
          ? true
          : false,
      can_reorder: orderObj.status === OrderStatuses.HOLDED ? false : true,
      can_return:
        ReturnAvailableStatuses.indexOf(orderObj.status) !== -1 ? true : false,
      created_at: new Date(orderObj.createdAt)
        .toISOString()
        .replace('T', ' ')
        .replace('.000Z', ' '),
      currency: orderObj.order_currency_code,
      grand_total: orderObj.grand_total,
      id: orderObj.order_id,
      increment_id: orderObj.increment_id,
      is_processing:
        orderObj.status === OrderStatuses.PROCESSING ? true : false,
      items: this.mapUserOrderItems(orderObj.items),
      status: orderObj.status,
    };
  };

  /**
   * It maps each item present in order
   * @param items items list
   * @returns
   */
  mapUserOrderItems = (items: SalesOrderItem[]) => {
    const mappedItems = [];
    for (const item of items) {
      mappedItems.push({
        can_return: true, //add status column
        name: item.product_name,
        price: item.price,
        product_id: item.product_id,
        qty: item.qty_ordered,
        returnable_qty: 1, // add column - returned_qty & returnable_qty
        rewardpoints: item.reward_points,
        sku: item.product_sku,
        thumbnail: item.thumbnail_url,
        url_key: item.url_key,
      });
    }

    return mappedItems;
  };

  /**
   * It builds order & payment details to be sent
   * as fetchOrder query response
   * @param order SalesOrder
   * @returns
   */
  buildFetchOrderResponse = (order: SalesOrder) => {
    return {
      amount: this.orderHelper.roundAndToBaseCurrencyAmount(
        Number(order.grand_total) || 0,
        2,
        100,
      ),
      currency: order.order_currency_code,
      currency_symbol: order.order_currency_symbol,
      error_msg:
        order.status === OrderStatuses.PAYMENT_PENDING
          ? JSON.stringify(order.payment.additional_information)
          : '',
      failure_wait_time: config.payment.failure_wait_time,
      merchant_id: config.razorpay.api_key,
      order_created_at: new Date(order.createdAt)
        .toISOString()
        .replace('T', ' ')
        .replace('.000Z', ''),
      order_id: order.increment_id,
      reference_number: order.payment.razorpay_order_id,
      ...this.buildFetchOrderStatus(
        order.status,
        order.payment.method,
        order.payment.updatedAt,
      ),
    };
  };

  /**
   * Calculate fetch-order-status & build parameters
   * that depends on fetch-order-status
   * @param salesOrderStatus current sales-order status
   * @param paymentMethod order's payment method
   * @param paymentUpdatedAt last update made on sales_order_payment at
   * @returns
   */
  buildFetchOrderStatus = (
    salesOrderStatus: string,
    paymentMethod: string,
    paymentUpdatedAt: string,
  ) => {
    if (paymentMethod === PaymentMethods.COD) {
      return {
        status: FetchOrderStatuses.SUCCESS,
        can_refetch: false,
        can_retry_payment: false,
        order_detail_available: true,
      };
    }
    if (salesOrderStatus === OrderStatuses.PAYMENT_RECEIVED) {
      return {
        status: FetchOrderStatuses.SUCCESS,
        can_refetch: false,
        can_retry_payment: false,
        order_detail_available: true,
      };
    }
    if (
      [OrderStatuses.PAYMENT_RECEIVED].indexOf(salesOrderStatus) === -1 &&
      !this.isTimedOut(paymentUpdatedAt)
    ) {
      return {
        status: FetchOrderStatuses.PROCESSING,
        can_refetch: true,
        can_retry_payment: false,
        order_detail_available: false,
      };
    }

    if (
      salesOrderStatus === OrderStatuses.PAYMENT_PENDING ||
      this.isTimedOut(paymentUpdatedAt)
    ) {
      return {
        status: FetchOrderStatuses.FAILED,
        can_refetch: false,
        can_retry_payment: true,
        order_detail_available: false,
      };
    }
  };

  /**
   * Check if payment failure timed out or not
   * @param paymentUpdatedAt
   */
  isTimedOut = (paymentUpdatedAt: string) => {
    const diff = (Date.now() - new Date(paymentUpdatedAt).valueOf()) / 1000; // get difference in seconds
    return diff > +config.payment.failure_wait_time ? true : false;
  };

  /**
   * It builds order-extra-info
   * @param orderId sales_order_id
   * @param extension_attributes array of custom attributes
   * @returns
   */
  buildSalesExtraInfoObject = (
    orderId: number,
    extension_attributes?: ExtensionAttributeInput[],
    eddInfo?: {
      max_dispatch_days?: number;
      max_delivery_days?: number;
      max_delivery_warehouse_code?: string;
    },
  ) => {
    const mappedItems = { order_id: orderId };

    if (extension_attributes?.length) {
      for (const data of extension_attributes) {
        mappedItems[data.attribute_code] = data.value;
      }
    }

    // Add EDD info if available
    if (eddInfo?.max_dispatch_days && eddInfo?.max_delivery_days) {
      mappedItems[`exp_dispatch_days`] = eddInfo.max_dispatch_days;
      mappedItems[`exp_delivery_days`] = eddInfo.max_delivery_days;
    }

    // Add warehouse code if available
    if (eddInfo?.max_delivery_warehouse_code) {
      mappedItems[`max_delivery_warehouse_code`] =
        eddInfo.max_delivery_warehouse_code;
    }

    return mappedItems;
  };

  /**
   * It builds order-item-extra-info
   * @param cartItem cart_data
   * @param deliveryInfo delivery information for the item
   * @returns
   */
  buildItemExtraInfo(
    cartItem: CartItem,
    deliveryInfo?: {
      days?: number;
      dispatch_days?: number;
      warehouse_code?: string;
    },
  ) {
    const { referral_code, is_free_product, item_id, buying_guide_qty } =
      cartItem;

    let bg_qty = 0;
    if (buying_guide_qty && cartItem.qty) {
      bg_qty = Math.min(buying_guide_qty, cartItem.qty);
    }

    return referral_code || is_free_product || bg_qty || deliveryInfo
      ? {
          ...(referral_code && { referral_code }),
          ...(is_free_product && { is_free_product }),
          ...(bg_qty && { buying_guide_qty: bg_qty }),
          ...(deliveryInfo && { delivery_info: deliveryInfo }),
          item_id,
        }
      : null;
  }

  /**
   * Maps cart item promotions to the format required for SalesOrderItemPromotion
   * @param cartItem CartItem
   * @returns Array of promotion objects or empty array
   */
  mapItemPromotions(cartItem: CartItem, products: productsGQLResponse) {
    return cartItem.quote_item_promotions &&
      cartItem.quote_item_promotions.length > 0
      ? cartItem.quote_item_promotions.map((promotion) => {
          const product = find(products.productForService, {
            sku: promotion.sku,
          });
          return {
            promotion_id: promotion.promotion_id,
            sku: promotion.sku,
            qty: promotion.qty,
            meta_info: {
              product_id: product.id,
              type_id: product.type_id,
              url_key: product.url_key,
              name: product.name?.trim(),
              price:
                product.price?.minimalPrice?.amount?.value ||
                product.price?.regularPrice?.amount?.value,
              thumbnail_url: product.thumbnail_url?.trim(),
              product_expiry_date: product.pd_expiry_date,
              tax_percent: promotion.meta_info?.tax_percent || null,
              tax_amount: promotion.meta_info?.tax_amount || null,
              parent_id: promotion.meta_info?.parent_id || null,
              weight: product.weight,
              buy_qty: promotion.meta_info?.buy_qty || null,
              buy_sku: promotion.meta_info?.buy_sku || null,
              is_multiply: promotion.meta_info?.is_multiply || null,
              start_date: promotion.meta_info?.start_date || null,
              end_date: promotion.meta_info?.end_date || null,
            },
          };
        })
      : [];
  }

  /**
   * Maps cart amount promotions to the format required for SalesOrderAmountPromotion
   * @param cartInfo GuestCartResponse
   * @returns Array of promotion objects or empty array
   */
  mapAmountPromotions(
    cartInfo: GuestCartResponse,
    orderId: number,
    products: productsGQLResponse,
  ) {
    return cartInfo.quote_amount_promotions &&
      cartInfo.quote_amount_promotions.length > 0
      ? cartInfo.quote_amount_promotions.map((promotion) => {
          const product = find(products.productForService, {
            sku: promotion.sku,
          });
          return {
            promotion_id: promotion.promotion_id,
            sku: promotion.sku,
            qty: promotion.qty,
            order_id: orderId,
            meta_info: {
              product_id: product.id,
              type_id: product.type_id,
              url_key: product.url_key,
              name: product.name?.trim(),
              price:
                product.price?.minimalPrice?.amount?.value ||
                product.price?.regularPrice?.amount?.value,
              thumbnail_url: product.thumbnail_url?.trim(),
              product_expiry_date: product.pd_expiry_date,
              tax_percent: promotion.meta_info?.tax_percent || null,
              tax_amount: promotion.meta_info?.tax_amount || null,
              parent_id: promotion.meta_info?.parent_id || null,
              weight: product.weight,
            },
          };
        })
      : [];
  }

  buildOrderStatusChangeResponse(orderId: string, status: string) {
    return {
      order_id: orderId,
      status:
        status === OrderStatuses.NEW_ORDER
          ? FetchOrderStatuses.SUCCESS
          : status,
      can_refetch: false,
      can_retry_payment:
        [OrderStatuses.NEW_ORDER, OrderStatuses.CANCELLED].indexOf(status) !==
        -1
          ? false
          : true,
      order_detail_available: status === OrderStatuses.NEW_ORDER ? true : false,
    };
  }
}
