## Description

It contains cart related APIs that will be used by Dentalkart User portal.

## Installation

```bash
$ npm install
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Test

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

# Access GraphQL Playground in local machine

Hit [http://localhost:8000/graphql](http://localhost:8000/graphql) in browser.

# Access Swagger documentation in local machine

Hit [http://localhost:8000/api-docs](http://localhost:8000/api-docs) in browser.

# Run with docker

NOTE: Assuming docker is already installed.

## Build docker image

`docker build . -t cart`

## Run docker image

`docker run -p 8000:8000 --name cart-server cart`

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [<PERSON><PERSON><PERSON>](https://kamilmysliwiec.com)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](LICENSE).
